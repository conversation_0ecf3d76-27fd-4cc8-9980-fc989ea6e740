{"compilerOptions": {"allowJs": true, "baseUrl": ".", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ES2022"], "moduleResolution": "bundler", "noEmit": true, "paths": {"~/*": ["./app/*"]}, "resolveJsonModule": true, "strict": true, "target": "ES2022", "types": ["@react-router/node", "vite/client", "vitest/globals", "@types/intercom-web"], "rootDirs": [".", "./.react-router/types"], "skipLibCheck": true, "module": "ESNext", "noUncheckedIndexedAccess": true}, "include": ["**/*.ts", "**/*.tsx", "remix.env.d.ts", "vitest.config.ts", "vitest.extensions.d.ts", "vitest.setup.ts", "instrumentation.server.js", ".react-router/types/**/*"], "exclude": ["build"]}