import * as fs from "node:fs";
import * as url from "node:url";
import { createRequest<PERSON><PERSON><PERSON> } from "@react-router/express";
import compression from "compression";
import express from "express";
import morgan from "morgan";
import consoleStamp from "console-stamp";
import { trace, context } from "@opentelemetry/api";
import sourceMapSupport from "source-map-support";

import { loadEnv } from "./loadEnv.js";

// Add timestamps to the beginning of all messages logged to the console. This
// is a workaround: it is difficult to control all usages of console.X() in the
// codebase, so rather than attempt to do so, we ensure that all usages of
// console have a consistent timestamp so that we can accurately determine when
// a new log entry has started.
consoleStamp(console, {
  // The date and label formats match the console stamp defaults.
  format:
    ":date(dd.mm.yyyy HH:MM.ss.l) :label(7) [trace_id=:traceId() span_id=:spanId()]",
  tokens: {
    traceId: () => {
      return (
        trace.getSpan(context.active())?.spanContext().traceId ??
        "00000000000000000000000000000000"
      );
    },
    spanId: () => {
      return (
        trace.getSpan(context.active())?.spanContext().spanId ??
        "0000000000000000"
      );
    },
  },
});

// Load environment variables
loadEnv();

// Set up source map support for stack traces
sourceMapSupport.install({
  retrieveSourceMap: function (source) {
    const match = source.startsWith("file://");
    if (match) {
      const filePath = url.fileURLToPath(source);
      const sourceMapPath = `${filePath}.map`;
      if (fs.existsSync(sourceMapPath)) {
        return {
          url: source,
          map: fs.readFileSync(sourceMapPath, "utf8"),
        };
      }
    }
    return null;
  },
});

// Ensure that unhandled promises in loaders don't crash the server
process.on("unhandledRejection", (reason, p) => {
  // eslint-disable-next-line no-console
  console.error(
    "Unhandled Rejection at:",
    p,
    "reason:",
    reason,
    "stacktrace:",
    reason?.stack ?? "<none>"
  );
});

const app = express();

// Vite fingerprints its assets so we can cache forever.
app.use(
  "/assets",
  express.static("build/client/assets", { immutable: true, maxAge: "1y" })
);

// Everything else (like favicon.ico) is cached for an hour. You may want to be
// more aggressive with this caching.
app.use(express.static("build/client", { maxAge: "1h" }));

app.use(compression());

// Disable x-powered-by header
app.disable("x-powered-by");

// Return 404 errors for requests to sourcemaps.
app.get("/build", (req, res, next) => {
  if (req.path.endsWith(".js.map") || req.path.endsWith(".css.map")) {
    return res.sendStatus(404);
  }
  next();
});

// Add logging
app.use(
  morgan("tiny", {
    stream: {
      write: function (str) {
        // eslint-disable-next-line no-console
        console.info(str.trim());
      },
    },
  })
);

// Handle SSR requests
app.all(
  "*",
  createRequestHandler({
    build: await import("./build/server/index.js"),
  })
);

// ANSI color codes used when logging to terminal
const SUCCESS_PREFIX = "\x1b[44m\x1b[42m done \x1b[0m";
const INFO_PREFIX = "\x1b[44m\x1b[30m info \x1b[0m";

const port = process.env.PORT || 3000;
app.listen(port, () => {
  /* eslint-disable no-console */
  console.log(
    SUCCESS_PREFIX,
    `🚀 Express server listening at http://localhost:${port}`
  );
  console.log(INFO_PREFIX, `NODE_ENV=${process.env.NODE_ENV}`);
  console.log(INFO_PREFIX, `ZEPLYN_LOG_LEVEL=${process.env.ZEPLYN_LOG_LEVEL}`);
  console.log(
    INFO_PREFIX,
    `💡 Set ZEPLYN_LOG_LEVEL=info npm run dev to see detailed network logs.`
  );
  /* eslint-enable no-console */
});
