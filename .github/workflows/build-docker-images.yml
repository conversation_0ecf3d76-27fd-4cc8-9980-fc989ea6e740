name: Build and push Docker image to ECR

on:
  # Create an image for every commit to main, and every release tag.
  push:
    branches:
      - main
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
  # Allow developers to create builds for specific development branches.
  workflow_dispatch:

jobs:
  push-image:
    uses: zeplyn-ai/webapp/.github/workflows/build-docker-images.yml@main
    with:
      repository: zeplyn-web
    secrets: inherit
    permissions:
      id-token: write
      contents: read
