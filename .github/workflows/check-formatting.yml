name: Check formatting and do static error checking

on:
  pull_request:

permissions:
  contents: read

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: '${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}'
  cancel-in-progress: true

jobs:
  check-formatting:
    name: Check for formatting issues
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          cache: 'npm'
      - name: Install dependencies
        run: npm install
      - name: Verify formatting
        run: npm run format:check
  check-lint:
    name: Check for linter errors
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          cache: 'npm'
      - name: Install dependencies
        run: npm install
      - name: Check for lint errors
        run: npm run lint
  check-type-errors:
    name: Check for type errors
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          cache: 'npm'
      - name: Install dependencies
        run: npm install
      - name: Check for type errors
        run: npm run typecheck 
  check-for-jira-ticket:
    runs-on: ubuntu-latest
    name: Check for a Jira ticket in one of the commit messages
    permissions:
      pull-requests: read
    steps:
      - name: Get PR Commits
        id: get-pr-commits
        if: github.event_name == 'pull_request'
        uses: tim-actions/get-pr-commits@v1.3.1
        with:
          token: ${{ github.token}}
      - name: Check for Jira ticket number
        uses: tim-actions/commit-message-checker-with-regex@v0.3.2
        if: github.event_name == 'pull_request'
        with:
          commits: ${{ steps.get-pr-commits.outputs.commits }}
          pattern: 'ENG-\d{1,}|NOTICKET[:=].+'
          one_pass_all_pass: true
          error: >-
            Missing Jira ticket number in commit message. Please add a Jira
            ticket number to a commit message in one of the PR's commits, or
            add "NOTICKET: Reason" to the commit message if there is no Jira
            ticket.
      - name: Generate summary (failure)
        if: failure()
        run: |
          cat <<EOF >> $GITHUB_STEP_SUMMARY
          :red_circle:
          Missing Jira ticket number in commit message. Please add a Jira
          ticket number to a commit message in one of the PR's commits, or
          add "NOTICKET: Reason" to the commit message if there is no Jira
          ticket.
          EOF
