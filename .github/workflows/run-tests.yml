name: Run unit tests

on:
  push:
    branches:
      - main
  pull_request:

permissions:
  contents: read

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: '${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}'
  cancel-in-progress: true

jobs:
  test:
    name: Run unit tests
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          cache: 'npm'
      - name: Install 1Password CL<PERSON>
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: op document get 4fwoa2xe6suiitby7trbi57v3i -o .env.development.secrets
      - name: Install dependencies
        run: npm install
      - name: Run tests
        run: npm test 

  run-prod-server:
    name: Run the prod server
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          cache: 'npm'
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: op document get 4fwoa2xe6suiitby7trbi57v3i -o .env.development.secrets
      - name: Install dependencies
        run: npm install --include=dev
      - name: Build
        run: npm run build
      - name: Prune dev dependencies
        run: npm prune --omit=dev
      - name: Start the server
        run: npm run serve &
      - name: Check that the server is serving traffic
        run: |
         sleep 10
         curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/
