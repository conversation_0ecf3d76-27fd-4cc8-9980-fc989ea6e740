/// <reference types="vitest" />

import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";
import path from "path";
import { loadEnv } from "./loadEnv";

loadEnv();

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "./app"),
    },
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./vitest.setup.ts",
    reporters: process.env.GITHUB_ACTIONS
      ? ["dot", "github-actions"]
      : ["default"],
    coverage: {
      all: true,
      clean: true,
      include: ["app/**/*.{js,jsx,ts,tsx}"],
      exclude: ["**/__tests__", "**/*.test.*"],
      extension: [".js", ".jsx", ".ts", ".tsx"],
    },
  },
});
