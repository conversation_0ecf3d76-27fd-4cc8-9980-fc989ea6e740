{
  "workbench.colorCustomizations": {
    "statusBar.background": "#006542"
  },
  // General editor settings, mostly here to match <PERSON><PERSON><PERSON> configs
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.formatOnSave": true,
  "files.eol": "\n",
  "javascript.preferences.quoteStyle": "double",
  "typescript.preferences.quoteStyle": "double",

  // Don't suggest imports from these packages
  "typescript.preferences.autoImportFileExcludePatterns": [
    // Always use Remix exports, never their react-router-dom equivalents
    "react-router-dom",
    // Always use @shadcn/ui Calendar component, never react-day-picker directly
    "react-day-picker",
    // Always use @shadcn/ui components, never directly import from @radix/*
    "@radix-ui/*"
  ],

  // Hide files/folders from file explorer and search
  "files.exclude": {
    "**/*.js.snap": true,
    "**/build": true,
    "**/remix-app/build": true,
    "**/remix-app/public/build": true,
    "**/coverage": true,
    "**/dist": true,
    "**/node_modules": true,
    "**/*.log": true,
    "**/.cache": true,
    "**/generated": true
  },
  "search.exclude": {
    "**/package-lock.json": true
  },

  // Associate file extensions with language services
  "files.associations": {
    // Some VS Code JSON config files are actually JSON with Comments (JSONC)
    "*.json": "jsonc",
    ".env*": "properties",
    ".npmrc*": "properties",
    "*.css": "tailwindcss"
  },

  // Typescript/ESLint/Prettier configs
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.workingDirectories": [
    { "pattern": "./*" },
    { "pattern": "./remix-app/*" }
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "prettier.prettierPath": "./node_modules/prettier",
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // Tailwind configs
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "editor.inlineSuggest.enabled": true,
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "tailwindCSS.includeLanguages": {
    "html": "HTML",
    "javascript": "javascript",
    "javascriptreact": "javascriptreact",
    "typescript": "typescript",
    "typescriptreact": "typescriptreact",
    "plaintext": "javascript"
  },
  "tailwindCSS.classAttributes": [
    "class",
    "className",
    "ngClass",
    "enter",
    "enterFrom",
    "enterTo",
    "leave",
    "leaveFrom",
    "leaveTo"
  ],
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["clsx\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ]
}
