# This script creates a baseline **workspace** VS Code configuration file (`.vscode/settings.json`),
# if one does not already exist. The generated `.vscode/settings.json` file is never committed to Git
# to allow users to tweak their editor configurations as needed, without impacting everyone else.
#
# This script also installs some VS Code extensions, but these are kept to the bare minimum, since
# developers tend to have strong opinions about which extensions they use!
# - Prettier, to format files on save
# - ESLint, to display lint errors and warnings in-editor
#
# You can find a log of all actions performed by this hook in the
# .vscode/vscode-npm-prepare-hook-events.log file

if which code >/dev/null; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] Found a VS Code installation. Checking for existing configuation..." >> .vscode/vscode-npm-prepare-hook-events.log

  # Write configuration
  mkdir -p ./.vscode
  if [ -e ./.vscode/settings.json ]
  then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Found existing VS Code project configuration, keeping things as they are. You might want to periodically update your .vscode/settings.json with relevant configs from the .vscode/settings.template.json." >> .vscode/vscode-npm-prepare-hook-events.log
  else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] No existing VS Code project configuration found, creating a new configuration using the defaults from .vscode/settings.template.json." >> .vscode/vscode-npm-prepare-hook-events.log
    cp ./.vscode/settings.template.json ./.vscode/settings.json
  fi

  if ! echo "$(code --list-extensions)" | grep -E 'dbaeumer.vscode-eslint|esbenp.prettier-vscode' >/dev/null
  then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Installing missing VS Code extensions." >> .vscode/vscode-npm-prepare-hook-events.log
    code --install-extension dbaeumer.vscode-eslint
    code --install-extension esbenp.prettier-vscode
  else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Found existing VS Code extensions, keeping things as they are." >> .vscode/vscode-npm-prepare-hook-events.log
  fi
    # echo "[$(date '+%Y-%m-%d %H:%M:%S')] Installing VS Code extensions" >> $HOME/.dotfile-install-log
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] VS Code not installed, skipping setup." >> ./.vscode/vscode-npm-prepare-hook-events.log
fi

# Only keep the last 100 lines so the log file doesn't keep growing forever
echo "$(tail -n 100 ./.vscode/vscode-npm-prepare-hook-events.log)" > ./.vscode/vscode-npm-prepare-hook-events.log
