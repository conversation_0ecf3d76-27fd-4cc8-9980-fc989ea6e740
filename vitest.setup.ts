import "@testing-library/jest-dom";
import "@testing-library/jest-dom/vitest";
import * as matchers from "@testing-library/jest-dom/matchers";

// Mock matchMedia
beforeAll(() => {
  // Only define this in the browser testing environment, not the node environment.
  if (typeof window === "undefined") {
    return;
  }

  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query: string) => {
      return {
        matches: false,
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
    }),
  });
});

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Add custom project-specific matchers.
expect.extend({
  toBeHidden(received: HTMLElement) {
    return {
      pass: received.closest("[hidden]") !== null,
      message: () => "Expected element to be hidden",
    };
  },
});
