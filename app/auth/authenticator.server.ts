import { Authenticator } from "remix-auth";
import { FormStrategy } from "remix-auth-form";
import { MicrosoftStrategy } from "remix-auth-microsoft";
import { z } from "zod";
import { GoogleStrategy } from "./remix-auth-google/GoogleStrategy.server";
import { authSessionStorage } from "./session.server";
import { login } from "~/api/auth/login.server";
import { UserAuthSession } from "./types";
import { logError } from "~/utils/log.server";
import { googleLogin } from "~/api/auth/googleLogin.server";
import { microsoftLogin } from "~/api/auth/microsoftLogin.server";
import { formDataToObject } from "~/utils/validation";
import { AuthApi, Configuration } from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";

// Constants
export const EMAIL_PASSWORD_STRATEGY = "email-password-strategy";
export const GOOGLE_STRATEGY = "google-strategy";
export const MICROSOFT_STRATEGY = "microsoft-strategy";
export const IMPERSONATE_STRATEGY = "impersonate-strategy";

// Types
const LoginFormStruct = z.object({
  email: z.string().min(1, "Email required").email("Invalid email"),
  password: z.string().min(3, "Invalid password"),
});

const ImpersonateFormStruct = z.object({
  email: z.string().min(1, "Email required").email("Invalid email"),
  purpose: z.string().min(1, "Purpose required"),
  ttlSeconds: z.string(),
});

// Helpers
const createFormStrategy = () =>
  new FormStrategy(async ({ form }) => {
    // Validate form values
    const validatedFormData = LoginFormStruct.parse(formDataToObject(form));

    // Authenticate
    return await login(validatedFormData);
  });

const createImpersonateStrategy = () => {
  /*
  This strategy is used to impersonate a user. It requires the email of the user to impersonate, the purpose of the impersonation, and the time-to-live (ttlSeconds) of the impersonation.
  It uses the AuthAPI to get the credentials of the impersonated user and return them as a UserAuthSession.
  */
  return new FormStrategy(async ({ form, context }) => {
    if (!context || !context.request) {
      throw new Error("Request context is missing in impersonate strategy");
    }

    const validatedFormData = ImpersonateFormStruct.parse(
      formDataToObject(form)
    );
    const config = new Configuration(
      await configurationParameters(context.request as Request)
    );
    const res = await new AuthApi(config).authImpersonateUser({
      username: validatedFormData.email,
      purpose: validatedFormData.purpose,
      ttlSeconds: parseInt(validatedFormData.ttlSeconds),
    });

    return {
      accessToken: res.accessToken,
      email: res.userProfile.email,
      firstName: res.userProfile.firstName,
      lastName: res.userProfile.lastName,
      refreshToken: res.refreshToken,
      userId: res.userProfile.uuid,
      orgId: res.userProfile.orgId,
    };
  });
};

const createMicrosoftStrategy = () => {
  if (!process.env.ZEPLYN_MSAL_CLIENT_ID) {
    throw Error("Missing ZEPLYN_MSAL_CLIENT_ID");
  }
  if (!process.env.ZEPLYN_HOST) {
    throw Error("Missing ZEPLYN_HOST");
  }
  if (!process.env.ZEPLYN_MSAL_CLIENT_SECRET) {
    throw Error("Missing ZEPLYN_MSAL_CLIENT_SECRET");
  }
  return new MicrosoftStrategy(
    {
      clientId: process.env.ZEPLYN_MSAL_CLIENT_ID,
      clientSecret: process.env.ZEPLYN_MSAL_CLIENT_SECRET,
      redirectUri: `${process.env.ZEPLYN_HOST}/auth/microsoft/callback`,
      scope: "openid profile email User.Read",
      prompt: "select_account",
    },
    async ({ accessToken, profile }): Promise<UserAuthSession> => {
      const email = profile.emails.find(({ value }) => !!value)?.value;
      if (!email) {
        logError("MicrosoftStrategy.userAuthSession error", "Missing email");
        throw Error("[auth MicrosoftStrategy] Missing email");
      }
      try {
        const userAuthSession = await microsoftLogin({ accessToken });
        return userAuthSession;
      } catch (e) {
        logError("MicrosoftStrategy.userAuthSession error", e);
        throw e;
      }
    }
  );
};

const createGoogleStrategy = () => {
  if (!process.env.ZEPLYN_GOOGLE_CLIENT_ID) {
    throw Error("Missing ZEPLYN_GOOGLE_CLIENT_ID");
  }
  if (!process.env.ZEPLYN_GOOGLE_CLIENT_SECRET) {
    throw Error("Missing ZEPLYN_GOOGLE_CLIENT_SECRET");
  }
  if (!process.env.ZEPLYN_HOST) {
    throw Error("Missing ZEPLYN_HOST");
  }
  return new GoogleStrategy(
    {
      clientID: process.env.ZEPLYN_GOOGLE_CLIENT_ID,
      clientSecret: process.env.ZEPLYN_GOOGLE_CLIENT_SECRET,
      callbackURL: `${process.env.ZEPLYN_HOST}/auth/google/callback`,
      scope: "openid profile email",
    },
    async ({
      accessToken,
      refreshToken,
      profile,
    }): Promise<UserAuthSession> => {
      const email = profile.emails.find(({ value }) => !!value)?.value;
      if (!email) throw Error("[auth GoogleStrategy] Missing email");

      try {
        const userAuthSession = await googleLogin({ accessToken });
        return userAuthSession;
      } catch (e) {
        logError("GoogleStrategy.userAuthSession error", e);
        throw e;
      }
    }
  );
};

const createAuthenticator = () => {
  const authenticator = new Authenticator<UserAuthSession>(authSessionStorage);

  // Attach strategies here
  authenticator.use(createFormStrategy(), EMAIL_PASSWORD_STRATEGY);
  authenticator.use(createGoogleStrategy(), GOOGLE_STRATEGY);
  authenticator.use(createMicrosoftStrategy(), MICROSOFT_STRATEGY);
  authenticator.use(createImpersonateStrategy(), IMPERSONATE_STRATEGY);

  return authenticator;
};

// Exports
export const authenticator = createAuthenticator();

export const getUserSessionOrRedirect = async (request: Request) => {
  const url = new URL(request.url);
  const redirectTo = url.pathname + url.search;

  return await authenticator.isAuthenticated(request, {
    failureRedirect: `/auth/login?redirectTo=${encodeURIComponent(redirectTo)}`,
  });
};
