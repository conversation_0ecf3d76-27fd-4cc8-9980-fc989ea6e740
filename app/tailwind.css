@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * Tailwind/shadcn-ui color scheme
 * See docs section on CSS variables: https://ui.shadcn.com/docs/theming#css-variables
 */
@layer base {
  :root {
    --background-hex: #ffffff;
    --background: 0 0% 100%;
    --foreground-hex: #182349;
    --foreground: 227 51% 19%;

    --primary-hex: #4f6ddb;
    --primary: 227 66% 58%;
    --primary-foreground-hex: #f6faff;
    --primary-foreground: 213, 100%, 98%;

    --secondary-hex: #9ea4b8;
    --secondary: 226 15% 67%;
    --secondary-foreground-hex: #dfe0e1;
    --secondary-foreground: 210 3% 88%;
    /* ---- */

    --card-hex: #ffffff;
    --card: 0 0% 100%;
    --card-foreground-hex: #182349;
    --card-foreground: 227 51% 19%;

    --popover-hex: #ffffff;
    --popover: 0 0% 100%;
    --popover-foreground-hex: #182349;
    --popover-foreground: 227 51% 19%;

    --muted-hex: #f1f5f9;
    --muted: 210 40% 96.1%;
    --muted-foreground-hex: #64748b;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent-hex: #f1f5f9;
    --accent: 210 40% 96.1%;
    --accent-foreground-hex: #0f172a;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive-hex: #e63939;
    --destructive: 0 78% 56%;
    --destructive-muted-hex: #e8a6a6;
    --destructive-muted: 0 59% 78%;
    --destructive-foreground-hex: #ffe5e5;
    --destructive-foreground: 0 100% 95%;

    --warning-hex: #f86f03;
    --warning: 26 98% 49%;
    --warning-foreground-hex: #ffe9d8;
    --warning-foreground: 26 100% 92%;

    --success-hex: #137847;
    --success: 151 73% 27%;
    --success-foreground-hex: #c7ffdc;
    --success-foreground: 143 100% 89%;

    --border-hex: #b0b5c5;
    --border: 226 15% 73%;

    --input-hex: #e2e8f0;
    --input: 214.3 31.8% 91.4%;
    --ring-hex: #4f6ddb;
    --ring: 227 66% 58%;

    --radius: 0.5rem;

    --custom-light-blue: rgba(211, 219, 246, 0.38);

    --magic-gradient: linear-gradient(to right, #16a085, #27ae60, #f39c12);
    --magic-gradient: linear-gradient(to right, #9b59b6, #3498db);
    --magic-gradient: linear-gradient(to right, #2980b9, #8e44ad);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

.magic-gradient-border {
  position: relative;
  z-index: 0;

  color: #2980b9;
  background: var(--magic-gradient);
  border-radius: 0.5rem;
  box-shadow: var(--tw-shadow);
}

.magic-gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 0.45rem;;
  margin: 1px; /* thickness of the border */
  background: hsl(var(--background));
  z-index: -1;
}

.magic-gradient-text {
  background: var(--magic-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@layer base {
  * {
    @apply border-border;
  }
  .bg-light-blue {
    background-color: var(--custom-light-blue);
  }
  .bg-magic {
    background: var(--magic-gradient);
  }
  body {
    @apply bg-background text-foreground;
  }

  html,
  body,
  #root {
    box-sizing: border-box;
    height: 100%;
    margin: 0;
    padding: 0;
    width: 100%;
  }
}
