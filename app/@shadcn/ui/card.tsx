import { Slot } from "@radix-ui/react-slot";
import * as React from "react";
import { cn } from "~/@shadcn/utils";
import { Typography, type TypographyProps } from "~/@ui/Typography";

// Exports
export type CardProps = React.HTMLAttributes<HTMLDivElement> & {
  asChild?: boolean;
  compact?: boolean;
};
export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, asChild = false, compact = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";
    return (
      <Comp
        ref={ref}
        className={cn(
          "flex select-none flex-col rounded-2xl border bg-card text-card-foreground shadow",
          compact ? "p-3" : "p-4",
          className
        )}
        {...props}
      />
    );
  }
);
Card.displayName = "Card";

export type CardHeaderProps = React.HTMLAttributes<HTMLDivElement> & {
  asChild?: boolean;
};
export const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, asChild, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";
    return (
      <Comp
        ref={ref}
        className={cn("flex flex-col space-y-1.5", className)}
        {...props}
      />
    );
  }
);
CardHeader.displayName = "CardHeader";

export const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  TypographyProps
>(({ className, ...props }, ref) => (
  <Typography
    ref={ref}
    variant="h4"
    className={cn(
      "self-stretch overflow-hidden text-ellipsis whitespace-nowrap text-foreground",
      className
    )}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

export const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  TypographyProps
>(({ className, ...props }, ref) => (
  <Typography
    ref={ref}
    variant="body2"
    color="secondary"
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

export type CardContentProps = React.HTMLAttributes<HTMLDivElement> & {
  asChild?: boolean;
};
export const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, asChild, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";
    return <Comp ref={ref} className={cn("p-0", className)} {...props} />;
  }
);
CardContent.displayName = "CardContent";

export type CardFooterProps = React.HTMLAttributes<HTMLDivElement> & {
  asChild?: boolean;
};
export const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, asChild, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";
    return (
      <Comp
        ref={ref}
        className={cn("flex items-center p-0", className)}
        {...props}
      />
    );
  }
);
CardFooter.displayName = "CardFooter";
