import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "~/@shadcn/utils";
import { useAfterHydration } from "~/utils/hydration";

export const inputWrapperVariants = cva(
  "flex w-full cursor-text items-center transition-colors placeholder:text-secondary",
  {
    variants: {
      variant: {
        default:
          "border border-border bg-background shadow hover:border-foreground hover:bg-accent has-[input:read-only]:pointer-events-none has-[button:hover]:bg-background has-[input:focus]:outline-none has-[input:focus]:ring-2 has-[input:focus]:ring-ring",
      },
      size: {
        default: "gap-1 rounded-2xl p-3 text-base [&>svg]:h-6 [&>svg]:w-6",
        lg: "h-16 gap-2 rounded-2xl px-4 py-2 text-base [&>svg]:h-6 [&>svg]:w-6",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

type InputElemProps = React.InputHTMLAttributes<HTMLInputElement>;
export type InputProps = Omit<InputElemProps, "size"> &
  VariantProps<typeof inputWrapperVariants> & {
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
  };
export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    { className, id, leftIcon, rightIcon, size, type, variant, ...props },
    ref
  ) => {
    const afterHydration = useAfterHydration();
    const handleWrapperClick = afterHydration(
      // Focus input when user clicks on input icons
      () => id && document.getElementById(id)?.focus(),
      () => {}
    );
    return (
      <div
        className={cn(inputWrapperVariants({ size, variant }), className)}
        onClick={handleWrapperClick}
      >
        {leftIcon}
        <input
          id={id}
          type={type}
          className={cn(
            "flex w-full bg-transparent transition-colors placeholder:text-secondary focus-visible:outline-none",
            className
          )}
          ref={ref}
          {...props}
        />
        {rightIcon}
      </div>
    );
  }
);
Input.displayName = "Input";
