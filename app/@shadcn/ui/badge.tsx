import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "~/@shadcn/utils";
import { Slot } from "@radix-ui/react-slot";

export const badgeVariants = cva(
  "shrink-0 items-center rounded-full border px-2.5 py-0.5 text-sm font-normal transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80 hover:text-white",
        secondary: "border-transparent bg-secondary-foreground text-foreground",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
        slate: "rounded-xl border-sky-700 bg-slate-200 py-1 text-sky-700",
      },
      inline: {
        default: "flex",
        inline: "inline-flex",
      },
    },
    defaultVariants: {
      variant: "default",
      inline: "default",
    },
  }
);

// TODO: technically this should be a generic, but we're not doing anything
// fancy with Badge divs and spans, so we treat everything as a div
export type BadgeProps = React.HTMLAttributes<HTMLDivElement> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean };

export const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, inline = "default", asChild, variant, ...props }, ref) => {
    const BaseComp = inline === "default" ? "div" : "span";
    const Comp = asChild ? Slot : BaseComp;

    return (
      <Comp
        className={cn(badgeVariants({ inline, variant }), className)}
        ref={ref}
        {...props}
      />
    );
  }
);
Badge.displayName = "Badge";
