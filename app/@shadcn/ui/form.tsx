import * as React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";
import { Slot } from "@radix-ui/react-slot";
import { cn } from "~/@shadcn/utils";
import { Label } from "~/@shadcn/ui/label";
import { z } from "zod";

// Contexts
const FormFieldContextStruct = z.object({
  id: z.string(),
  name: z.string(),
  error: z.string().optional(),
  required: z.boolean().optional(),
});
type FormFieldContextValue = z.infer<typeof FormFieldContextStruct>;
export const FormFieldContext = React.createContext(
  {} as FormFieldContextValue
);

// Helpers
export const useFormField = () => {
  const ctx = React.useContext(FormFieldContext);
  try {
    const { id, error, name, required } = FormFieldContextStruct.parse(ctx);
    return {
      id,
      error,
      name,
      required,
      formItemId: `${id}-form-item`,
      formDescriptionId: `${id}-form-item-description`,
      formMessageId: `${id}-form-item-message`,
    };
  } catch {
    throw Error(
      "Invalid Form context. Form components should be wrapped in <FormField>, and `id` and `name` fields must be set."
    );
  }
};

// Exports
type HTMLDivElementProps = React.HTMLAttributes<HTMLDivElement>;
type FormFieldProps = HTMLDivElementProps & Omit<FormFieldContextValue, "id">;
export const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ className, name, required, error, ...props }, ref) => {
    const id = React.useId();
    return (
      <FormFieldContext.Provider value={{ id, error, name, required }}>
        <div
          ref={ref}
          className={cn(
            "flex flex-col gap-2 has-[input:read-only]:pointer-events-none",
            className
          )}
          {...props}
        />
      </FormFieldContext.Provider>
    );
  }
);
FormField.displayName = "FormField";

type LabelPrimitiveElem = React.ElementRef<typeof LabelPrimitive.Root>;
type LabelPrimitiveProps = React.ComponentPropsWithoutRef<
  typeof LabelPrimitive.Root
>;
type FormLabelProps = LabelPrimitiveProps & { error?: string };
export const FormLabel = React.forwardRef<LabelPrimitiveElem, FormLabelProps>(
  ({ className, error, children, ...props }, ref) => {
    const { required, formItemId } = useFormField();

    return (
      <Label
        ref={ref}
        className={cn(error && "text-destructive", className)}
        htmlFor={formItemId}
        {...props}
      >
        {children}
        {required === true ? "*" : null}
      </Label>
    );
  }
);
FormLabel.displayName = "FormLabel";

type SlotElem = React.ElementRef<typeof Slot>;
type FormControlProps = React.ComponentPropsWithoutRef<typeof Slot>;
export const FormControl = React.forwardRef<SlotElem, FormControlProps>(
  ({ ...props }, ref) => {
    const {
      error,
      name,
      required,
      formItemId,
      formDescriptionId,
      formMessageId,
    } = useFormField();

    return (
      <Slot
        ref={ref}
        id={formItemId}
        // @ts-ignore
        name={name}
        required={required}
        aria-describedby={
          !error
            ? `${formDescriptionId}`
            : `${formDescriptionId} ${formMessageId}`
        }
        aria-invalid={!!error}
        {...props}
      />
    );
  }
);
FormControl.displayName = "FormControl";

type FormDescriptionProps = React.HTMLAttributes<HTMLParagraphElement>;
export const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  FormDescriptionProps
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn("text-[0.8rem] text-muted-foreground", className)}
      {...props}
    />
  );
});
FormDescription.displayName = "FormDescription";

type FormMessageProps = React.HTMLAttributes<HTMLParagraphElement>;
export const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  FormMessageProps
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField();
  const body = error ?? children;
  if (!body) return null;

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn("text-[0.8rem] font-medium text-destructive", className)}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessage.displayName = "FormMessage";
