import { z } from "zod";

export const FlagValueStruct = z.object({
  is_active: z.boolean(),
  last_modified: z.string().datetime(),
});
export type FlagValue = z.infer<typeof FlagValueStruct>;

export const FlagsStruct = z.object({
  flags: z.record(z.string(), FlagValueStruct),
  switches: z.record(z.string(), FlagValueStruct),
  samples: z.record(z.string(), FlagValueStruct),
});
export type Flags = z.infer<typeof FlagsStruct>;
