import { createContext, useContext } from "react";

type UserAgentInfo = {
  isMobile: boolean;
  isAndroid: boolean;
  isIos: boolean;
  isFirefox: boolean;
  isChrome: boolean;
  isSafari: boolean;
};

export const defaultUa: UserAgentInfo = {
  isMobile: false,
  isAndroid: false,
  isIos: false,
  isFirefox: false,
  isChrome: false,
  isSafari: false,
};

export const UserAgentContext = createContext(defaultUa);

export const useUserAgent = () => useContext(UserAgentContext);

type UserAgentProvider = {
  children: React.ReactNode;
  value: UserAgentInfo;
};

export const UserAgentProvider = ({ value, children }: UserAgentProvider) => {
  return (
    <UserAgentContext.Provider value={value}>
      {children}
    </UserAgentContext.Provider>
  );
};
