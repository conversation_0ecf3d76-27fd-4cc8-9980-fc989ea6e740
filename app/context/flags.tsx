import { createContext, ReactNode, useContext } from "react";
import { Flags } from "./types";
import { datadogRum } from "@datadog/browser-rum";

const FlagContext = createContext<Flags>({
  flags: {},
  switches: {},
  samples: {},
});

type Props = { flags: Flags; children: ReactNode };
const FlagProvider = ({ flags, children }: Props) => (
  <FlagContext.Provider value={flags}>{children}</FlagContext.Provider>
);

function useFlag(name: string) {
  const flags = useContext(FlagContext);
  const isActive = flags.flags[name]?.is_active;
  datadogRum.addFeatureFlagEvaluation(name, isActive);
  return isActive;
}

export { FlagContext, FlagProvider, useFlag };
