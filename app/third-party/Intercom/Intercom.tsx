import { datadogLogs } from "@datadog/browser-logs";
import { type ReactNode, useEffect, useState } from "react";
import { UserAuthSession } from "~/auth/types";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";

// Constants
export const INTERCOM_LAUNCHER_SELECTOR = "zeplyn-intercom-launcher";

// Types
type IntercomQueue = { q: any[]; c: (...args: any[]) => void; (): void };

// Helpers
/**
 * This is the load script provided by Intercom, de-minified, with slightly
 * more type safety. See original here:
 * https://developers.intercom.com/installing-intercom/web/installation/#single-page-app
 */
export const initializeIntercom = (appId: string) => {
  if (typeof window.Intercom === "function") {
    window.Intercom("reattach_activator");
    window.Intercom("update", window.intercomSettings);
  } else {
    const intercomQueue = function () {
      intercomQueue.c(arguments);
    } as IntercomQueue;
    intercomQueue.q = [];
    intercomQueue.c = function (args: any) {
      intercomQueue.q.push(args);
    };
    window.Intercom = intercomQueue as any;
    const loadScript = function () {
      const scriptElement = document.createElement("script");
      scriptElement.type = "text/javascript";
      scriptElement.async = true;
      scriptElement.src = `https://widget.intercom.io/widget/${appId}`;
      const x = document.getElementsByTagName("script")[0]!;
      x.parentNode?.insertBefore(scriptElement, x);
    };

    if (document.readyState === "complete") {
      loadScript();
    } else {
      window.addEventListener("load", loadScript, false);
    }
  }
};

type IntercomProviderProps = Pick<
  UserAuthSession,
  "email" | "firstName" | "lastName" | "userId"
> & {
  appId: string;
  children: ReactNode;
};
export const IntercomProvider = ({
  appId,
  children,
  email,
  firstName,
  lastName,
  userId,
}: IntercomProviderProps) => {
  const breakpoints = useTailwindBreakpoints();

  useEffect(() => {
    if (typeof window === "undefined") {
      return;
    }
    try {
      // Load Intercom script
      initializeIntercom(appId);

      // Set configuration options. See full list of options here:
      // https://developers.intercom.com/installing-intercom/web/attributes-objects/
      window.Intercom("boot", {
        // Messenger attributes
        api_base: "https://api-iam.intercom.io",
        app_id: appId,
        custom_launcher_selector: `#${INTERCOM_LAUNCHER_SELECTOR}`,
        hide_default_launcher: false,

        // Data attributes
        name: `${firstName} ${lastName}`, // Full name
        email,
        user_id: userId,
      });
    } catch (error) {
      datadogLogs.logger.error(
        "Failed to load Intercom",
        {},
        error instanceof Error ? error : undefined
      );
    }
  }, [appId, email, firstName, lastName, userId]);

  useEffect(() => {
    try {
      const isMobile = !breakpoints.matchedBreakpoints.has("md");

      window.Intercom("update", {
        hide_default_launcher: isMobile,
      });

      return () => {
        window.Intercom("update", {
          hide_default_launcher: true,
        });
      };
    } catch (error) {
      datadogLogs.logger.error(
        "Failed to update Intercom",
        {},
        error instanceof Error ? error : undefined
      );
    }
  }, [breakpoints]);

  return <>{children}</>;
};

export const useIntercom = (): { isIntercomAvailable: boolean } => {
  // Intercom is blocked by ad-blockers, so we need a way to detect when it is
  // available. Using a polling approach here with a maximum wait time.
  const MAX_WAIT_TIME = 5000;
  const INTERVAL = 100;
  const [remainingTime, setRemainingTime] = useState(MAX_WAIT_TIME);
  const [isAvailable, setIsAvailable] = useState(false);

  useEffect(() => {
    if (!isAvailable && remainingTime > 0) {
      const timeoutId = setTimeout(() => {
        if (typeof window === "undefined") {
          return;
        }
        if (window.Intercom?.booted) {
          setIsAvailable(true);
        } else {
          setRemainingTime((prev) => prev - INTERVAL);
        }
      }, INTERVAL);

      return () => clearTimeout(timeoutId);
    }
  }, [isAvailable, remainingTime]);

  return { isIntercomAvailable: isAvailable };
};
