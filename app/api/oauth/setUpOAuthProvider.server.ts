import { logError } from "~/utils/log.server";
import {
  Configuration,
  OauthApi,
  OAuthRequestProviderEnum,
} from "../openapi/generated";
import { configurationParameters } from "../openapi/configParams";

export const setUpOAuthProvider = async ({
  authorizationCode,
  provider,
  request,
}: {
  authorizationCode: string;
  provider: OAuthRequestProviderEnum;
  request: Request;
}) => {
  try {
    const requestURLString = request.url.split("?")[0];
    if (!requestURLString) {
      throw Error("Could not parse request URL");
    }
    const requestURL = new URL(requestURLString);
    const originalProto = request.headers.get("X-Forwarded-Proto");
    if (originalProto) {
      requestURL.protocol = `${originalProto}:`;
    }

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    await new OauthApi(configuration).oauthSetUpOauthIntegration({
      oAuthRequest: {
        authorizationCode: authorizationCode,
        requestUrl: requestURL.toString(),
        provider: provider,
      },
    });
  } catch (error) {
    if (error instanceof Response) throw error;
    logError("!!! OAuthApi.oauthSetUpOAuthIntegration", error);
    throw Error("Something went wrong");
  }
};
