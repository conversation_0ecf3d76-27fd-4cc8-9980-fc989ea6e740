type MockData = {
  name: string;
  uuid: string;
  created: string;
  modified: string;
  lastActivity: string;
  tags: string[];
  annual_income: string;
  net_worth: string;
  task_completion_rate: string;
  client_satisfaction_score: string;
  advisorNotes: string[];
  scheduled_notes_for_client: {
    uuid: string;
    meetingName: string;
    created: string;
  }[];
  agenda: {
    text: string;
    type: string;
    nodes: { text: string }[];
  }[];
};

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]!;
}

function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomPercentage(min: number = 75, max: number = 100): string {
  return `${(Math.random() * (max - min) + min).toFixed(1)}%`;
}

function getRandomCurrency(min: number, max: number): string {
  const minimumValue = Math.max(min, 85000);
  const randomValue =
    Math.floor(getRandomNumber(minimumValue, max) / 1000) * 1000;
  return `$${randomValue.toLocaleString()}`;
}

export function generateMockData(): Partial<MockData> {
  const tags = [
    "High Net Worth",
    "Retirement Planning",
    "Estate Planning",
    "Tax Optimization",
    "Wealth Management",
    "Investment Strategy",
    "Asset Allocation",
    "Financial Planning",
    "Portfolio Management",
    "Tax-Efficient Investing",
    "Risk Management",
    "Capital Preservation",
    "Income Distribution",
    "Retirement Savings",
    "Long-Term Growth",
    "Tax-Deferred Growth",
    "Wealth Preservation",
    "Estate Conservation",
  ];

  const randomTags = Array.from({ length: getRandomNumber(1, 4) }, () =>
    getRandomElement(tags)
  );

  return {
    tags: randomTags,
    annual_income: getRandomCurrency(50000, 200000),
    net_worth: getRandomCurrency(500000, 5000000),
    task_completion_rate: getRandomPercentage(62.5, 100),
    client_satisfaction_score: getRandomPercentage(),
    lastActivity: new Date(
      Date.now() - getRandomNumber(0, 15) * 24 * 60 * 60 * 1000
    )
      .toISOString()
      .split("T")[0],
  };
}
const userMockDataMapping: { [userId: string]: MockData } = {};

export function generateMockDataForUser(userId: string): Partial<MockData> {
  // Check if the user already has mock data generated
  if (userMockDataMapping[userId]) {
    return userMockDataMapping[userId]!;
  }

  // Generate new mock data
  const newMockData = generateMockData();

  // Store the new mock data for the user
  userMockDataMapping[userId] = {
    ...mockData,
    ...newMockData,
  };

  return userMockDataMapping[userId]!;
}

export const mockData: MockData = {
  name: "John Doe",
  uuid: "61f57169-688e-4cd7-be5f-2d942a4bbf9b",
  created: "2024-05-08T13:51:00.980894Z",
  modified: "2024-05-14T12:48:07.906743Z",
  lastActivity: "Mon, Dec 20 - 3.45PM",
  tags: ["High Net Worth", "Retirement Planning", "Estate Planning"],
  annual_income: "$120,000",
  net_worth: "$750,000",
  task_completion_rate: "62.5%",
  client_satisfaction_score: "75%",
  advisorNotes: [
    "John Doe is recently married, indicating a significant life change and possibly new financial goals or adjustments..",
    "John Doe has inherited wealth, suggesting a need for estate and investment management advice.",
    "John Doe and his wife aim to buy a house by the end of 2025, indicating a short-term financial goal.",
    "John Doe is interested in real estate investment strategies, specifically in the context of liquidating inherited property tax-efficiently.",
  ],
  scheduled_notes_for_client: [
    {
      uuid: "9e8871b8-5365-4bbf-8e55-a989550a00cd",
      meetingName: "Q3 Review",
      created: "created today at 4:35PM",
    },
  ],
  agenda: [
    {
      text: "Conversation Starter (5 minutes)",
      type: "pointer",
      nodes: [
        {
          text: "Hi John, it's great to see you again. I remember you mentioned planning a family vacation to Hawaii recently—how was the trip? Did the kids enjoy it? Also, have there been any other exciting developments or changes in your life since we last spoke?",
        },
      ],
    },
    {
      text: "Review of Current Financial Situation (10 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Discuss current financial status: AUM: $750,000; Portfolio: $600,000 in retirement accounts, $150,000 in non-retirement funds;",
        },
        {
          text: "Discuss recent investment performance and adjustments made.",
        },
        {
          text: "Discuss any significant changes since the last meeting.",
        },
      ],
    },
    {
      text: "Review action items from the previous meeting (10 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Documentation for charitable donations ($15,000 received, potential increase to $20,000)",
        },
        {
          text: "Feedback on proposed changes to the investment portfolio.",
        },
      ],
    },
    {
      text: "Early Retirement Plan (15 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Review the implications of early retirement (2026) on his financial goals and savings strategy.",
        },
        {
          text: "Discuss the new financial plan tailored for early retirement: 1) Adjusted savings targets, 2) Revised portfolio strategy to ensure sufficient income and stability, 3) Tax efficiency and withdrawal strategies.",
        },
      ],
    },
    {
      text: "Tax Efficiency Strategies (10 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Evaluate potential tax-saving strategies to support early retirement.",
        },
        {
          text: "Discuss the impact of increased charitable contributions on tax savings.",
        },
      ],
    },
    {
      text: "Major Life Events Planning (10 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Discuss the upcoming purchase of a second home ($350,000) and its financial impact.",
        },
        {
          text: "Plan for setting up college funds for his children ($10,000 initial contribution per child).",
        },
      ],
    },
    {
      text: "Investment Strategy Review (10 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Evaluate the current conservative investment strategy.",
        },
        {
          text: "Discuss potential adjustments to ensure adequate income and stability for early retirement.",
        },
        {
          text: "Consider reallocating investments to include more tax-exempt bonds and low-cost ETFs.",
        },
      ],
    },
    {
      text: "Concerns and Questions (10 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Address any concerns John has about potential tax increases and their impact.",
        },
        {
          text: "Discuss any fears about running out of funds due to early retirement.",
        },
        {
          text: "Answer any questions John may have about the new financial plan and strategies.",
        },
      ],
    },
    {
      text: "Next Steps and Follow-up (5 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Recap the key decisions and action items",
        },
        {
          text: "Schedule the next follow-up meeting to review progress and finalize any pending decisions.",
        },
      ],
    },
    {
      text: "Closing Remarks (5 minutes)",
      type: "checklist",
      nodes: [
        {
          text: "Thank John for his time and trust in Acme Wealth Management",
        },
        {
          text: "Reiterate the commitment to helping him achieve his early retirement and financial goals.",
        },
      ],
    },
  ],
};

export const updatedMockData: MockData = {
  ...mockData,
  ...generateMockData(),
};
