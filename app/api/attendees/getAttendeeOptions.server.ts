import {
  RawApiResponseAttendeeOptions,
  AttendeeOptions,
  mapRawApiResponseAttendeeOptions,
} from "./types";
import { logError } from "~/utils/log.server";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { configurationParameters } from "../openapi/configParams";
import { AttendeesApi, Configuration } from "../openapi/generated";

// Exports
export type GetAttendeeOptionsArguments = {
  request: Request;
};

export const getAttendeeOptions = async ({
  request,
}: GetAttendeeOptionsArguments): Promise<AttendeeOptions> => {
  try {
    const { userId } = await getUserSessionOrRedirect(request);
    const config = new Configuration(await configurationParameters(request));
    const attendeeOptions = await new AttendeesApi(
      config
    ).attendeesListAttendeeOptions({ userId });
    const validatedPayload =
      RawApiResponseAttendeeOptions.parse(attendeeOptions);
    return mapRawApiResponseAttendeeOptions(validatedPayload);
  } catch (error) {
    if (error instanceof Response) throw error;
    // All other errors
    logError("!!! api/notes/getAttendeeOptions", error);
    throw Error("Something went wrong");
  }
};
