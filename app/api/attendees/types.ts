import { z } from "zod";

export const ClientStruct = z.object({
  uuid: z.string(),
  name: z.string(),
  type: z.literal("client").optional(),
  clientType: z.string().optional().nullable(),
  phoneNumber: z.optional(z.string().nullable()),
});

export const UserStruct = z.object({
  uuid: z.string(),
  name: z.string(),
  role: z.string().nullable().optional(),
  type: z.literal("user").optional(),
  phoneNumber: z.optional(z.string().nullable()),
});

const UnknownStruct = z.object({
  uuid: z.string(),
  name: z.string(),
  type: z.literal("unknown").optional(),
});

export const AttendeeOptionStruct = z.union([
  ClientStruct,
  UserStruct,
  UnknownStruct,
]);
export const AttendeeOptionsStruct = z.array(AttendeeOptionStruct);

export const RawApiResponseAttendeeOptions = z.object({
  users: z.array(UserStruct),
  clients: z.array(ClientStruct),
});

export type AttendeeOption = z.infer<typeof AttendeeOptionStruct>;
export type AttendeeOptions = z.infer<typeof AttendeeOptionsStruct>;

export const mapRawApiResponseAttendeeOptions = (
  data: z.infer<typeof RawApiResponseAttendeeOptions>
): AttendeeOptions => {
  const user: z.infer<typeof UserStruct>[] = data.users.map((u) => ({
    ...u,
    type: "user" as const,
  }));
  const client = data.clients.map((c) => ({ ...c, type: "client" as const }));
  return [...user, ...client].sort((a, b) => a.name.localeCompare(b.name));
};
