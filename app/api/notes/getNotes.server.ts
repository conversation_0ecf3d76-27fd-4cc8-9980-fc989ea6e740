import { configurationParameters } from "../openapi/configParams";
import {
  Configuration,
  ListNotesResponse,
  NoteApi,
} from "../openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { logError } from "~/utils/log.server";

// Exports
export type GetNotesArguments = {
  searchTerm?: string;
  request: Request;
  notBefore?: number;
  notAfter?: number;
};
export const getNotes = async ({
  request,
  searchTerm = "",
  notBefore,
  notAfter,
}: GetNotesArguments): Promise<ListNotesResponse[]> => {
  try {
    const { userId } = await getUserSessionOrRedirect(request);
    if (!userId) throw Error("Could not read userId from user session");
    const config = new Configuration(await configurationParameters(request));
    return new NoteApi(config).noteListNotes({
      q: searchTerm,
      notBefore,
      notAfter,
    });
  } catch (error) {
    if (error instanceof Response) throw error;
    // All other errors
    logError("!!! api/notes/getNotes", error);
    throw Error("Something went wrong");
  }
};
