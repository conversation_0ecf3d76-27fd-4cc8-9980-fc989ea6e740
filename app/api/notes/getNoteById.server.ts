import { Configuration, NoteApi, NoteResponse } from "../openapi/generated";
import { configurationParameters } from "../openapi/configParams";

export const getNoteById = async ({
  noteId,
  request,
}: {
  noteId: string;
  request: Request;
}): Promise<NoteResponse> => {
  const config = new Configuration(await configurationParameters(request));
  return new NoteApi(config).noteGetNote({ noteId });
};
