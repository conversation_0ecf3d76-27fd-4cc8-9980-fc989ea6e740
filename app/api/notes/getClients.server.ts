import { logError } from "~/utils/log.server";
import {
  ClientListResponse,
  Configuration,
  CrmApi,
} from "../openapi/generated";
import { configurationParameters } from "../openapi/configParams";

export const getClients = async ({
  searchTerm = "",
  pageSize = 0,
  cursor = "",
  request,
}: {
  searchTerm?: string;
  pageSize?: number;
  cursor?: string;
  request: Request;
}): Promise<ClientListResponse> => {
  try {
    const config = new Configuration(await configurationParameters(request));
    return new CrmApi(config).crmGetClientList({
      q: searchTerm,
      pageSize,
      cursor,
    });
  } catch (error) {
    logError("!!! api/notes/getClients", error);
    throw error;
  }
};
