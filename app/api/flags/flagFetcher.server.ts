import { fetch, fetchWithAuth } from "~/utils/fetch.server";
import { Flags, FlagsStruct } from "~/context/types";
import { logError } from "~/utils/log.server";
import { authenticator } from "~/auth/authenticator.server";

// Constants
const API_BASE = process.env.ZEPLYN_API_BASE_URL;
const ROUTE = "/v1/users/flags"; // GET

// Exports
export const fetchFlags = async (request: Request): Promise<Flags> => {
  try {
    // Fetch data
    const endpoint = `${API_BASE}${ROUTE}`;
    const isAuthenticated =
      (await authenticator.isAuthenticated(request)) !== null;
    const res = await (isAuthenticated
      ? fetchWithAuth(request, endpoint)
      : fetch(endpoint));

    return FlagsStruct.parse(res.payload);
  } catch (error) {
    logError("!!! users/flags", error);
    return Promise.resolve({ flags: {}, switches: {}, samples: {} });
  }
};
