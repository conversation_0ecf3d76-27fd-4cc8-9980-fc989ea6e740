import { UserAuthSession } from "~/auth/types";
import { logError } from "~/utils/log.server";
import { nonAuthenticatedConfigurationParameters } from "../openapi/configParams";
import { AuthApi, Configuration } from "../openapi/generated";

// Exports
type MicrosoftLoginArguments = { accessToken: string };
export const microsoftLogin = async ({
  accessToken,
}: MicrosoftLoginArguments): Promise<UserAuthSession> => {
  try {
    const params = await nonAuthenticatedConfigurationParameters();
    const res = await new AuthApi(
      new Configuration(params)
    ).authMicrosoftSignin({
      accessTokenAuthRequest: { accessToken },
    });

    return {
      accessToken: res.accessToken,
      email: res.userProfile.email,
      firstName: res.userProfile.firstName,
      lastName: res.userProfile.lastName,
      refreshToken: res.refreshToken,
      userId: res.userProfile.uuid,
      orgId: res.userProfile.orgId,
    };
  } catch (error) {
    logError("!!! api/auth/microsoftLogin", error);
    throw Error("Something went wrong");
  }
};
