import { UserAuthSession } from "~/auth/types";
import { logError } from "~/utils/log.server";
import { AuthApi, Configuration, ResponseError } from "~/api/openapi/generated";
import { nonAuthenticatedConfigurationParameters } from "~/api/openapi/configParams";

// Exports
type LoginArguments = { email: string; password: string };
export const login = async ({
  email,
  password,
}: LoginArguments): Promise<UserAuthSession> => {
  try {
    const params = await nonAuthenticatedConfigurationParameters();
    const res = await new AuthApi(new Configuration(params)).authLogin({
      loginRequest: { email, password },
    });
    return {
      accessToken: res.accessToken,
      email: res.userProfile.email,
      firstName: res.userProfile.firstName,
      lastName: res.userProfile.lastName,
      refreshToken: res.refreshToken,
      userId: res.userProfile.uuid,
      orgId: res.userProfile.orgId,
    };
  } catch (error) {
    if (error instanceof ResponseError) {
      if (error.response.status === 401) {
        throw Error("Invalid credentials");
      }
    }
    // All other errors
    logError("!!! api/auth/login", error);
    throw Error("Something went wrong");
  }
};
