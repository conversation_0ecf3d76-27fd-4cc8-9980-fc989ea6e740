import { UserAuthSession } from "~/auth/types";
import { logError } from "~/utils/log.server";
import { nonAuthenticatedConfigurationParameters } from "../openapi/configParams";
import { AuthApi, Configuration } from "../openapi/generated";

// Exports
type GoogleLoginArguments = { accessToken: string };
export const googleLogin = async ({
  accessToken,
}: GoogleLoginArguments): Promise<UserAuthSession> => {
  try {
    const params = await nonAuthenticatedConfigurationParameters();
    const res = await new AuthApi(new Configuration(params)).authGoogleSignin({
      accessTokenAuthRequest: { accessToken },
    });

    return {
      accessToken: res.accessToken,
      email: res.userProfile.email,
      firstName: res.userProfile.firstName,
      lastName: res.userProfile.lastName,
      refreshToken: res.refreshToken,
      userId: res.userProfile.uuid,
      orgId: res.userProfile.orgId,
    };
  } catch (error) {
    logError("!!! api/auth/googleLogin", error);
    throw Error("Something went wrong");
  }
};
