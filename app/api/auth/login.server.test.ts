// @vitest-environment node

import { login } from "./login.server";
import {
  AuthApi,
  ResponseError,
  UserLicenseType,
} from "~/api/openapi/generated";
import { nonAuthenticatedConfigurationParameters } from "~/api/openapi/configParams";
import { logError } from "~/utils/log.server";
import { describe, it, expect, vi } from "vitest";

describe("login", () => {
  const loginParams = { email: "<EMAIL>", password: "password" };

  beforeEach(() => {
    vi.mock("~/api/openapi/configParams");
    vi.mock("~/utils/log.server");
    vi.mocked(nonAuthenticatedConfigurationParameters).mockResolvedValue({});
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should return user session on successful login", async () => {
    const mockResponse = {
      accessToken: "mockAccessToken",
      userProfile: {
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        uuid: "mockUuid",
        orgId: "mockOrgId",
        isActive: true,
        status: "active",
        licenseType: UserLicenseType.Advisor,
      },
      refreshToken: "mockRefreshToken",
    };

    vi.spyOn(AuthApi.prototype, "authLogin").mockResolvedValue(mockResponse);

    expect(await login(loginParams)).toEqual({
      accessToken: mockResponse.accessToken,
      email: mockResponse.userProfile.email,
      firstName: mockResponse.userProfile.firstName,
      lastName: mockResponse.userProfile.lastName,
      refreshToken: mockResponse.refreshToken,
      userId: mockResponse.userProfile.uuid,
      orgId: mockResponse.userProfile.orgId,
    });
  });

  it("should throw 'Invalid credentials' error on 401 response", async () => {
    const error = new ResponseError(new Response("", { status: 401 }));

    vi.spyOn(AuthApi.prototype, "authLogin").mockRejectedValue(error);

    await expect(login(loginParams)).rejects.toThrowError(
      "Invalid credentials"
    );
  });

  it("should log error and throw 'Something went wrong' on other errors", async () => {
    const fakeError = new Error("Some error");

    vi.spyOn(AuthApi.prototype, "authLogin").mockRejectedValue(fakeError);

    await expect(login(loginParams)).rejects.toThrowError(
      "Something went wrong"
    );
    expect(logError).toHaveBeenCalledWith("!!! api/auth/login", fakeError);
  });
});
