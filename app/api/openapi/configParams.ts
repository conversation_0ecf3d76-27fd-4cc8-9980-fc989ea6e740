import {
  ConfigurationParameters,
  FetchParams,
  Middleware,
  ResponseContext,
} from "app/api/openapi/generated";
import { authenticator } from "~/auth/authenticator.server";

// Implements header-based authentication for the generated API clients.
class AuthenticationMiddleware implements Middleware {
  // The access token to be used for authentication.
  accessToken: string;

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  public async pre(context: ResponseContext): Promise<FetchParams | void> {
    return {
      url: context.url,
      init: {
        ...context.init,
        headers: new Headers({
          ...context.init.headers,
          Authorization: `Bearer ${this.accessToken}`,
        }),
      },
    };
  }
}

// Returns a set of signed-in configuration parameters for a generated API client.
//
// Redirects the user to the login page if they are not authenticated.
export const configurationParameters = async (
  request: Request
): Promise<ConfigurationParameters> => {
  const userAuthSession = await authenticator.isAuthenticated(request, {
    failureRedirect: "/auth/login",
  });

  return {
    basePath: process.env.ZEPLYN_BACKEND_BASE_URL,
    middleware: [new AuthenticationMiddleware(userAuthSession.accessToken)],
  };
};

// Returns a set of signed-out configuration parameters for a generated API client.
export const nonAuthenticatedConfigurationParameters =
  async (): Promise<ConfigurationParameters> => {
    return { basePath: process.env.ZEPLYN_BACKEND_BASE_URL };
  };
