/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  HTTPValidationError,
  PreferenceUpdate,
  PreferencesResponse,
} from '../models/index';
import {
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    PreferenceUpdateFromJSON,
    PreferenceUpdateToJSON,
    PreferencesResponseFromJSON,
    PreferencesResponseToJSON,
} from '../models/index';

export interface PreferencesUpdatePreferencesRequest {
    preferenceUpdate: PreferenceUpdate;
}

/**
 * 
 */
export class PreferencesApi extends runtime.BaseAPI {

    /**
     * Get Preference Schemas
     */
    async preferencesGetPreferenceSchemasRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<PreferencesResponse>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/preferences/`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => PreferencesResponseFromJSON(jsonValue));
    }

    /**
     * Get Preference Schemas
     */
    async preferencesGetPreferenceSchemas(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<PreferencesResponse> {
        const response = await this.preferencesGetPreferenceSchemasRaw(initOverrides);
        return await response.value();
    }

    /**
     * Update Preferences
     */
    async preferencesUpdatePreferencesRaw(requestParameters: PreferencesUpdatePreferencesRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['preferenceUpdate'] == null) {
            throw new runtime.RequiredError(
                'preferenceUpdate',
                'Required parameter "preferenceUpdate" was null or undefined when calling preferencesUpdatePreferences().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/preferences/`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: PreferenceUpdateToJSON(requestParameters['preferenceUpdate']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Update Preferences
     */
    async preferencesUpdatePreferences(requestParameters: PreferencesUpdatePreferencesRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.preferencesUpdatePreferencesRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
