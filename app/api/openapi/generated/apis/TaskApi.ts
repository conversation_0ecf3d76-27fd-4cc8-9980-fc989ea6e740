/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  CreateTaskRequest,
  CreateTaskResponse,
  HTTPValidationError,
  ListTasksResponse,
  TaskResponse,
  TaskUpdate,
} from '../models/index';
import {
    CreateTaskRequestFromJSON,
    CreateTaskRequestToJSON,
    CreateTaskResponseFromJSON,
    CreateTaskResponseToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    ListTasksResponseFromJSON,
    ListTasksResponseToJSON,
    TaskResponseFromJSON,
    TaskResponseToJSON,
    TaskUpdateFromJSON,
    TaskUpdateToJSON,
} from '../models/index';

export interface TaskCreateTaskRequest {
    createTaskRequest: CreateTaskRequest;
}

export interface TaskDeleteTaskRequest {
    taskUuid: string;
}

export interface TaskEditTaskRequest {
    taskUuid: string;
    taskUpdate: TaskUpdate;
}

export interface TaskListTasksRequest {
    userUuid: string;
    searchQuery?: string;
}

export interface TaskViewTaskRequest {
    taskUuid: string;
}

/**
 * 
 */
export class TaskApi extends runtime.BaseAPI {

    /**
     * Create a task for a specific user. The user must be authorized to create a task.
     * Create a task
     */
    async taskCreateTaskRaw(requestParameters: TaskCreateTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CreateTaskResponse>> {
        if (requestParameters['createTaskRequest'] == null) {
            throw new runtime.RequiredError(
                'createTaskRequest',
                'Required parameter "createTaskRequest" was null or undefined when calling taskCreateTask().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/task/create`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: CreateTaskRequestToJSON(requestParameters['createTaskRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CreateTaskResponseFromJSON(jsonValue));
    }

    /**
     * Create a task for a specific user. The user must be authorized to create a task.
     * Create a task
     */
    async taskCreateTask(requestParameters: TaskCreateTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CreateTaskResponse> {
        const response = await this.taskCreateTaskRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Delete a specific task by its ID. The user must be authorized to view the note.
     * Delete a specific task
     */
    async taskDeleteTaskRaw(requestParameters: TaskDeleteTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['taskUuid'] == null) {
            throw new runtime.RequiredError(
                'taskUuid',
                'Required parameter "taskUuid" was null or undefined when calling taskDeleteTask().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/task/{task_uuid}`.replace(`{${"task_uuid"}}`, encodeURIComponent(String(requestParameters['taskUuid']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Delete a specific task by its ID. The user must be authorized to view the note.
     * Delete a specific task
     */
    async taskDeleteTask(requestParameters: TaskDeleteTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.taskDeleteTaskRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Edit Task
     */
    async taskEditTaskRaw(requestParameters: TaskEditTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['taskUuid'] == null) {
            throw new runtime.RequiredError(
                'taskUuid',
                'Required parameter "taskUuid" was null or undefined when calling taskEditTask().'
            );
        }

        if (requestParameters['taskUpdate'] == null) {
            throw new runtime.RequiredError(
                'taskUpdate',
                'Required parameter "taskUpdate" was null or undefined when calling taskEditTask().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/task/{task_uuid}`.replace(`{${"task_uuid"}}`, encodeURIComponent(String(requestParameters['taskUuid']))),
            method: 'PATCH',
            headers: headerParameters,
            query: queryParameters,
            body: TaskUpdateToJSON(requestParameters['taskUpdate']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Edit Task
     */
    async taskEditTask(requestParameters: TaskEditTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.taskEditTaskRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List Tasks
     */
    async taskListTasksRaw(requestParameters: TaskListTasksRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListTasksResponse>> {
        if (requestParameters['userUuid'] == null) {
            throw new runtime.RequiredError(
                'userUuid',
                'Required parameter "userUuid" was null or undefined when calling taskListTasks().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['searchQuery'] != null) {
            queryParameters['search_query'] = requestParameters['searchQuery'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/task/list_tasks/{user_uuid}`.replace(`{${"user_uuid"}}`, encodeURIComponent(String(requestParameters['userUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ListTasksResponseFromJSON(jsonValue));
    }

    /**
     * List Tasks
     */
    async taskListTasks(requestParameters: TaskListTasksRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListTasksResponse> {
        const response = await this.taskListTasksRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Retrieve details of a specific task by its ID. The user must be authorized to view the task.
     * Retrieve data of a specific Task
     */
    async taskViewTaskRaw(requestParameters: TaskViewTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<TaskResponse>> {
        if (requestParameters['taskUuid'] == null) {
            throw new runtime.RequiredError(
                'taskUuid',
                'Required parameter "taskUuid" was null or undefined when calling taskViewTask().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/task/{task_uuid}`.replace(`{${"task_uuid"}}`, encodeURIComponent(String(requestParameters['taskUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => TaskResponseFromJSON(jsonValue));
    }

    /**
     * Retrieve details of a specific task by its ID. The user must be authorized to view the task.
     * Retrieve data of a specific Task
     */
    async taskViewTask(requestParameters: TaskViewTaskRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<TaskResponse> {
        const response = await this.taskViewTaskRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
