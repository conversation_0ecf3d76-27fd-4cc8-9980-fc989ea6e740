/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  AccessTokenAuthRequest,
  HTTPValidationError,
  LoginRequest,
  LoginResponse,
  RefreshTokensResponse,
} from '../models/index';
import {
    AccessTokenAuthRequestFromJSON,
    AccessTokenAuthRequestToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    LoginRequestFromJSON,
    LoginRequestToJSON,
    LoginResponseFromJSON,
    LoginResponseToJSON,
    RefreshTokensResponseFromJSON,
    RefreshTokensResponseToJSON,
} from '../models/index';

export interface AuthForgotPasswordRequest {
    email: string;
}

export interface AuthGoogleSigninRequest {
    accessTokenAuthRequest: AccessTokenAuthRequest;
}

export interface AuthHandleZoomAuthorizationRequest {
    code: string;
}

export interface AuthImpersonateUserRequest {
    username: string;
    purpose: string;
    ttlSeconds?: number;
}

export interface AuthLoginRequest {
    loginRequest: LoginRequest;
}

export interface AuthMicrosoftSigninRequest {
    accessTokenAuthRequest: AccessTokenAuthRequest;
}

export interface AuthRefreshTokensRequest {
    refreshToken: string;
}

export interface AuthResetPasswordRequest {
    email: string;
    code: string;
    newPassword: string;
}

/**
 * 
 */
export class AuthApi extends runtime.BaseAPI {

    /**
     * Sends a password reset email to the user.
     */
    async authForgotPasswordRaw(requestParameters: AuthForgotPasswordRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['email'] == null) {
            throw new runtime.RequiredError(
                'email',
                'Required parameter "email" was null or undefined when calling authForgotPassword().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['email'] != null) {
            queryParameters['email'] = requestParameters['email'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        const response = await this.request({
            path: `/api/v2/auth/forgot_password`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Sends a password reset email to the user.
     */
    async authForgotPassword(requestParameters: AuthForgotPasswordRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.authForgotPasswordRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Logs in a user via Google SSO
     */
    async authGoogleSigninRaw(requestParameters: AuthGoogleSigninRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<LoginResponse>> {
        if (requestParameters['accessTokenAuthRequest'] == null) {
            throw new runtime.RequiredError(
                'accessTokenAuthRequest',
                'Required parameter "accessTokenAuthRequest" was null or undefined when calling authGoogleSignin().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        const response = await this.request({
            path: `/api/v2/auth/google-signin`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: AccessTokenAuthRequestToJSON(requestParameters['accessTokenAuthRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => LoginResponseFromJSON(jsonValue));
    }

    /**
     * Logs in a user via Google SSO
     */
    async authGoogleSignin(requestParameters: AuthGoogleSigninRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<LoginResponse> {
        const response = await this.authGoogleSigninRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Handle Zoom Authorization
     */
    async authHandleZoomAuthorizationRaw(requestParameters: AuthHandleZoomAuthorizationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['code'] == null) {
            throw new runtime.RequiredError(
                'code',
                'Required parameter "code" was null or undefined when calling authHandleZoomAuthorization().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['code'] != null) {
            queryParameters['code'] = requestParameters['code'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        const response = await this.request({
            path: `/api/v2/auth/zoomauth`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Handle Zoom Authorization
     */
    async authHandleZoomAuthorization(requestParameters: AuthHandleZoomAuthorizationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.authHandleZoomAuthorizationRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Impersonate User
     */
    async authImpersonateUserRaw(requestParameters: AuthImpersonateUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<LoginResponse>> {
        if (requestParameters['username'] == null) {
            throw new runtime.RequiredError(
                'username',
                'Required parameter "username" was null or undefined when calling authImpersonateUser().'
            );
        }

        if (requestParameters['purpose'] == null) {
            throw new runtime.RequiredError(
                'purpose',
                'Required parameter "purpose" was null or undefined when calling authImpersonateUser().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['username'] != null) {
            queryParameters['username'] = requestParameters['username'];
        }

        if (requestParameters['purpose'] != null) {
            queryParameters['purpose'] = requestParameters['purpose'];
        }

        if (requestParameters['ttlSeconds'] != null) {
            queryParameters['ttl_seconds'] = requestParameters['ttlSeconds'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/auth/impersonate_user`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => LoginResponseFromJSON(jsonValue));
    }

    /**
     * Impersonate User
     */
    async authImpersonateUser(requestParameters: AuthImpersonateUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<LoginResponse> {
        const response = await this.authImpersonateUserRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Logs in a user by username and password.
     */
    async authLoginRaw(requestParameters: AuthLoginRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<LoginResponse>> {
        if (requestParameters['loginRequest'] == null) {
            throw new runtime.RequiredError(
                'loginRequest',
                'Required parameter "loginRequest" was null or undefined when calling authLogin().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        const response = await this.request({
            path: `/api/v2/auth/login`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: LoginRequestToJSON(requestParameters['loginRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => LoginResponseFromJSON(jsonValue));
    }

    /**
     * Logs in a user by username and password.
     */
    async authLogin(requestParameters: AuthLoginRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<LoginResponse> {
        const response = await this.authLoginRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Logs in a user via Microsoft SSO
     */
    async authMicrosoftSigninRaw(requestParameters: AuthMicrosoftSigninRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<LoginResponse>> {
        if (requestParameters['accessTokenAuthRequest'] == null) {
            throw new runtime.RequiredError(
                'accessTokenAuthRequest',
                'Required parameter "accessTokenAuthRequest" was null or undefined when calling authMicrosoftSignin().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        const response = await this.request({
            path: `/api/v2/auth/microsoft-signin`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: AccessTokenAuthRequestToJSON(requestParameters['accessTokenAuthRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => LoginResponseFromJSON(jsonValue));
    }

    /**
     * Logs in a user via Microsoft SSO
     */
    async authMicrosoftSignin(requestParameters: AuthMicrosoftSigninRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<LoginResponse> {
        const response = await this.authMicrosoftSigninRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Refresh Tokens
     */
    async authRefreshTokensRaw(requestParameters: AuthRefreshTokensRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RefreshTokensResponse>> {
        if (requestParameters['refreshToken'] == null) {
            throw new runtime.RequiredError(
                'refreshToken',
                'Required parameter "refreshToken" was null or undefined when calling authRefreshTokens().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['refreshToken'] != null) {
            queryParameters['refresh_token'] = requestParameters['refreshToken'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        const response = await this.request({
            path: `/api/v2/auth/refresh_tokens`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RefreshTokensResponseFromJSON(jsonValue));
    }

    /**
     * Refresh Tokens
     */
    async authRefreshTokens(requestParameters: AuthRefreshTokensRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RefreshTokensResponse> {
        const response = await this.authRefreshTokensRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Resets the user\'s password.
     */
    async authResetPasswordRaw(requestParameters: AuthResetPasswordRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['email'] == null) {
            throw new runtime.RequiredError(
                'email',
                'Required parameter "email" was null or undefined when calling authResetPassword().'
            );
        }

        if (requestParameters['code'] == null) {
            throw new runtime.RequiredError(
                'code',
                'Required parameter "code" was null or undefined when calling authResetPassword().'
            );
        }

        if (requestParameters['newPassword'] == null) {
            throw new runtime.RequiredError(
                'newPassword',
                'Required parameter "newPassword" was null or undefined when calling authResetPassword().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['email'] != null) {
            queryParameters['email'] = requestParameters['email'];
        }

        if (requestParameters['code'] != null) {
            queryParameters['code'] = requestParameters['code'];
        }

        if (requestParameters['newPassword'] != null) {
            queryParameters['new_password'] = requestParameters['newPassword'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        const response = await this.request({
            path: `/api/v2/auth/reset_password`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Resets the user\'s password.
     */
    async authResetPassword(requestParameters: AuthResetPasswordRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.authResetPasswordRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
