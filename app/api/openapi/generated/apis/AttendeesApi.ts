/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  AttendeeInfo,
  HTTPValidationError,
  ListAttendeesResponse,
} from '../models/index';
import {
    AttendeeInfoFromJSON,
    AttendeeInfoToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    ListAttendeesResponseFromJSON,
    ListAttendeesResponseToJSON,
} from '../models/index';

export interface AttendeesListAttendeeOptionsRequest {
    userId: string;
    q?: string;
}

/**
 * 
 */
export class AttendeesApi extends runtime.BaseAPI {

    /**
     * List Attendee Options
     */
    async attendeesListAttendeeOptionsRaw(requestParameters: AttendeesListAttendeeOptionsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListAttendeesResponse>> {
        if (requestParameters['userId'] == null) {
            throw new runtime.RequiredError(
                'userId',
                'Required parameter "userId" was null or undefined when calling attendeesListAttendeeOptions().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/attendee/attendee_options/{user_id}`.replace(`{${"user_id"}}`, encodeURIComponent(String(requestParameters['userId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ListAttendeesResponseFromJSON(jsonValue));
    }

    /**
     * List Attendee Options
     */
    async attendeesListAttendeeOptions(requestParameters: AttendeesListAttendeeOptionsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListAttendeesResponse> {
        const response = await this.attendeesListAttendeeOptionsRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List Attendees
     */
    async attendeesListAttendeesRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<AttendeeInfo>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/attendee/`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(AttendeeInfoFromJSON));
    }

    /**
     * List Attendees
     */
    async attendeesListAttendees(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<AttendeeInfo>> {
        const response = await this.attendeesListAttendeesRaw(initOverrides);
        return await response.value();
    }

}
