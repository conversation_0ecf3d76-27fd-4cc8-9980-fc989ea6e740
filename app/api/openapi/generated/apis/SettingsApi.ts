/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  HTTPValidationError,
  MenuItem,
  SaveRequest,
  SectionDetails,
} from '../models/index';
import {
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    MenuItemFromJSON,
    MenuItemToJSON,
    SaveRequestFromJSON,
    SaveRequestToJSON,
    SectionDetailsFromJSON,
    SectionDetailsToJSON,
} from '../models/index';

export interface SettingsGetSettingsDetailsRouteRequest {
    identifier?: string;
}

export interface SettingsSaveRequest {
    saveRequest: SaveRequest;
}

/**
 * 
 */
export class SettingsApi extends runtime.BaseAPI {

    /**
     * Get Settings Details Route
     */
    async settingsGetSettingsDetailsRouteRaw(requestParameters: SettingsGetSettingsDetailsRouteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SectionDetails>> {
        const queryParameters: any = {};

        if (requestParameters['identifier'] != null) {
            queryParameters['identifier'] = requestParameters['identifier'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/details`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SectionDetailsFromJSON(jsonValue));
    }

    /**
     * Get Settings Details Route
     */
    async settingsGetSettingsDetailsRoute(requestParameters: SettingsGetSettingsDetailsRouteRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SectionDetails> {
        const response = await this.settingsGetSettingsDetailsRouteRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get Settings Menu Route
     */
    async settingsGetSettingsMenuRouteRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<MenuItem>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/menu`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(MenuItemFromJSON));
    }

    /**
     * Get Settings Menu Route
     */
    async settingsGetSettingsMenuRoute(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<MenuItem>> {
        const response = await this.settingsGetSettingsMenuRouteRaw(initOverrides);
        return await response.value();
    }

    /**
     * Save
     */
    async settingsSaveRaw(requestParameters: SettingsSaveRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<number>> {
        if (requestParameters['saveRequest'] == null) {
            throw new runtime.RequiredError(
                'saveRequest',
                'Required parameter "saveRequest" was null or undefined when calling settingsSave().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/save`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SaveRequestToJSON(requestParameters['saveRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<number>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Save
     */
    async settingsSave(requestParameters: SettingsSaveRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<number> {
        const response = await this.settingsSaveRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
