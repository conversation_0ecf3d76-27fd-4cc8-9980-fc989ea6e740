/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  HTTPValidationError,
  SearchResponse,
} from '../models/index';
import {
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    SearchResponseFromJSON,
    SearchResponseToJSON,
} from '../models/index';

export interface SearchSearchRequest {
    noteId: string;
    query: string;
}

export interface SearchSearchClientRequest {
    clientId: string;
    query: string;
}

/**
 * 
 */
export class SearchApi extends runtime.BaseAPI {

    /**
     * Perform a search on the note using the search query provided by the user, the search is done on the transcript and if not available use the summary instead
     * Perform a search on the note using the search query provided by the user
     */
    async searchSearchRaw(requestParameters: SearchSearchRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SearchResponse>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling searchSearch().'
            );
        }

        if (requestParameters['query'] == null) {
            throw new runtime.RequiredError(
                'query',
                'Required parameter "query" was null or undefined when calling searchSearch().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['query'] != null) {
            queryParameters['query'] = requestParameters['query'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/search/{note_id}`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SearchResponseFromJSON(jsonValue));
    }

    /**
     * Perform a search on the note using the search query provided by the user, the search is done on the transcript and if not available use the summary instead
     * Perform a search on the note using the search query provided by the user
     */
    async searchSearch(requestParameters: SearchSearchRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SearchResponse> {
        const response = await this.searchSearchRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Search across all notes and information for a client using the provided query
     * Search across all notes and information for a client
     */
    async searchSearchClientRaw(requestParameters: SearchSearchClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SearchResponse>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling searchSearchClient().'
            );
        }

        if (requestParameters['query'] == null) {
            throw new runtime.RequiredError(
                'query',
                'Required parameter "query" was null or undefined when calling searchSearchClient().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['query'] != null) {
            queryParameters['query'] = requestParameters['query'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/search/client/{client_id}`.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SearchResponseFromJSON(jsonValue));
    }

    /**
     * Search across all notes and information for a client using the provided query
     * Search across all notes and information for a client
     */
    async searchSearchClient(requestParameters: SearchSearchClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SearchResponse> {
        const response = await this.searchSearchClientRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
