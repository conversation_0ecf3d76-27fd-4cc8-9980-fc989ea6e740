/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  Bot,
  HTTPValidationError,
} from '../models/index';
import {
    BotFromJSON,
    BotToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
} from '../models/index';

export interface BotGetBotRequest {
    botUuid: string;
}

export interface BotLeaveMeetingRequest {
    botUuid: string;
}

export interface BotPauseRecordingRequest {
    botUuid: string;
}

export interface BotResumeRecordingRequest {
    botUuid: string;
}

export interface BotStartRecordingRequest {
    botUuid: string;
}

export interface BotStreamBotEventsRequest {
    botUuid: string;
}

/**
 * 
 */
export class BotApi extends runtime.BaseAPI {

    /**
     * Get Bot
     */
    async botGetBotRaw(requestParameters: BotGetBotRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Bot>> {
        if (requestParameters['botUuid'] == null) {
            throw new runtime.RequiredError(
                'botUuid',
                'Required parameter "botUuid" was null or undefined when calling botGetBot().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/bot/{bot_uuid}`.replace(`{${"bot_uuid"}}`, encodeURIComponent(String(requestParameters['botUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => BotFromJSON(jsonValue));
    }

    /**
     * Get Bot
     */
    async botGetBot(requestParameters: BotGetBotRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Bot> {
        const response = await this.botGetBotRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Leave Meeting
     */
    async botLeaveMeetingRaw(requestParameters: BotLeaveMeetingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['botUuid'] == null) {
            throw new runtime.RequiredError(
                'botUuid',
                'Required parameter "botUuid" was null or undefined when calling botLeaveMeeting().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/bot/{bot_uuid}/leave`.replace(`{${"bot_uuid"}}`, encodeURIComponent(String(requestParameters['botUuid']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Leave Meeting
     */
    async botLeaveMeeting(requestParameters: BotLeaveMeetingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.botLeaveMeetingRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Pause Recording
     */
    async botPauseRecordingRaw(requestParameters: BotPauseRecordingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['botUuid'] == null) {
            throw new runtime.RequiredError(
                'botUuid',
                'Required parameter "botUuid" was null or undefined when calling botPauseRecording().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/bot/{bot_uuid}/pause`.replace(`{${"bot_uuid"}}`, encodeURIComponent(String(requestParameters['botUuid']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Pause Recording
     */
    async botPauseRecording(requestParameters: BotPauseRecordingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.botPauseRecordingRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Resume Recording
     */
    async botResumeRecordingRaw(requestParameters: BotResumeRecordingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['botUuid'] == null) {
            throw new runtime.RequiredError(
                'botUuid',
                'Required parameter "botUuid" was null or undefined when calling botResumeRecording().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/bot/{bot_uuid}/resume`.replace(`{${"bot_uuid"}}`, encodeURIComponent(String(requestParameters['botUuid']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Resume Recording
     */
    async botResumeRecording(requestParameters: BotResumeRecordingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.botResumeRecordingRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Start Recording
     */
    async botStartRecordingRaw(requestParameters: BotStartRecordingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['botUuid'] == null) {
            throw new runtime.RequiredError(
                'botUuid',
                'Required parameter "botUuid" was null or undefined when calling botStartRecording().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/bot/{bot_uuid}/start`.replace(`{${"bot_uuid"}}`, encodeURIComponent(String(requestParameters['botUuid']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Start Recording
     */
    async botStartRecording(requestParameters: BotStartRecordingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.botStartRecordingRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Stream Bot Events
     */
    async botStreamBotEventsRaw(requestParameters: BotStreamBotEventsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['botUuid'] == null) {
            throw new runtime.RequiredError(
                'botUuid',
                'Required parameter "botUuid" was null or undefined when calling botStreamBotEvents().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            queryParameters["access_token"] = await this.configuration.apiKey("access_token"); // APIKeyQuery authentication
        }

        const response = await this.request({
            path: `/api/v2/bot/{bot_uuid}/events`.replace(`{${"bot_uuid"}}`, encodeURIComponent(String(requestParameters['botUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Stream Bot Events
     */
    async botStreamBotEvents(requestParameters: BotStreamBotEventsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.botStreamBotEventsRaw(requestParameters, initOverrides);
    }

}
