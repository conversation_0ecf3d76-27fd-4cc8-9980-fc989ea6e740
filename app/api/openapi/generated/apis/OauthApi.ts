/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  HTTPValidationError,
  OAuthRequest,
} from '../models/index';
import {
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    OAuthRequestFromJSON,
    OAuthRequestToJSON,
} from '../models/index';

export interface OauthSetUpOauthIntegrationRequest {
    oAuthRequest: OAuthRequest;
}

/**
 * 
 */
export class OauthApi extends runtime.BaseAPI {

    /**
     * Set up OAuth integration for various providers (Wealthbox, Microsoft, Google, Salesforce)
     * Set up OAuth integration
     */
    async oauthSetUpOauthIntegrationRaw(requestParameters: OauthSetUpOauthIntegrationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['oAuthRequest'] == null) {
            throw new runtime.RequiredError(
                'oAuthRequest',
                'Required parameter "oAuthRequest" was null or undefined when calling oauthSetUpOauthIntegration().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/oauth/configure`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: OAuthRequestToJSON(requestParameters['oAuthRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Set up OAuth integration for various providers (Wealthbox, Microsoft, Google, Salesforce)
     * Set up OAuth integration
     */
    async oauthSetUpOauthIntegration(requestParameters: OauthSetUpOauthIntegrationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.oauthSetUpOauthIntegrationRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
