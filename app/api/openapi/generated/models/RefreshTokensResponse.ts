/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface RefreshTokensResponse
 */
export interface RefreshTokensResponse {
    /**
     * 
     * @type {string}
     * @memberof RefreshTokensResponse
     */
    accessToken: string;
    /**
     * 
     * @type {string}
     * @memberof RefreshTokensResponse
     */
    refreshToken: string;
}

/**
 * Check if a given object implements the RefreshTokensResponse interface.
 */
export function instanceOfRefreshTokensResponse(value: object): value is RefreshTokensResponse {
    if (!('accessToken' in value) || value['accessToken'] === undefined) return false;
    if (!('refreshToken' in value) || value['refreshToken'] === undefined) return false;
    return true;
}

export function RefreshTokensResponseFromJSON(json: any): RefreshTokensResponse {
    return RefreshTokensResponseFromJSONTyped(json, false);
}

export function RefreshTokensResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RefreshTokensResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'accessToken': json['access_token'],
        'refreshToken': json['refresh_token'],
    };
}

export function RefreshTokensResponseToJSON(value?: RefreshTokensResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'access_token': value['accessToken'],
        'refresh_token': value['refreshToken'],
    };
}

