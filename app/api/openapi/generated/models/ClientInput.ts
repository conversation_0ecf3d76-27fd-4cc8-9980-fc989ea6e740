/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ClientInput
 */
export interface ClientInput {
    /**
     * 
     * @type {string}
     * @memberof ClientInput
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ClientInput
     */
    name: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientInput
     */
    email: string | null;
}

/**
 * Check if a given object implements the ClientInput interface.
 */
export function instanceOfClientInput(value: object): value is ClientInput {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    return true;
}

export function ClientInputFromJSON(json: any): ClientInput {
    return ClientInputFromJSONTyped(json, false);
}

export function ClientInputFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientInput {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'email': json['email'],
    };
}

export function ClientInputToJSON(value?: ClientInput | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'email': value['email'],
    };
}

