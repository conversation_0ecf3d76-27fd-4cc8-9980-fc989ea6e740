/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface LinkedCRMEntity
 */
export interface LinkedCRMEntity {
    /**
     * 
     * @type {string}
     * @memberof LinkedCRMEntity
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof LinkedCRMEntity
     */
    name: string;
}

/**
 * Check if a given object implements the LinkedCRMEntity interface.
 */
export function instanceOfLinkedCRMEntity(value: object): value is LinkedCRMEntity {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function LinkedCRMEntityFromJSON(json: any): LinkedCRMEntity {
    return LinkedCRMEntityFromJSONTyped(json, false);
}

export function LinkedCRMEntityFromJSONTyped(json: any, ignoreDiscriminator: boolean): LinkedCRMEntity {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function LinkedCRMEntityToJSON(value?: LinkedCRMEntity | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

