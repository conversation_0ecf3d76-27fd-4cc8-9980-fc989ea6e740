/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ZeplynKind } from './ZeplynKind';
import {
    ZeplynKindFromJSON,
    ZeplynKindFromJSONTyped,
    ZeplynKindToJSON,
} from './ZeplynKind';

/**
 * 
 * @export
 * @interface EventParticipant
 */
export interface EventParticipant {
    /**
     * 
     * @type {string}
     * @memberof EventParticipant
     */
    id?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EventParticipant
     */
    zeplynUuid?: string | null;
    /**
     * 
     * @type {Z<PERSON>lynKind}
     * @memberof EventParticipant
     */
    zeplynKind?: ZeplynKind | null;
    /**
     * 
     * @type {string}
     * @memberof EventParticipant
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EventParticipant
     */
    emailAddress: string;
}



/**
 * Check if a given object implements the EventParticipant interface.
 */
export function instanceOfEventParticipant(value: object): value is EventParticipant {
    if (!('emailAddress' in value) || value['emailAddress'] === undefined) return false;
    return true;
}

export function EventParticipantFromJSON(json: any): EventParticipant {
    return EventParticipantFromJSONTyped(json, false);
}

export function EventParticipantFromJSONTyped(json: any, ignoreDiscriminator: boolean): EventParticipant {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'zeplynUuid': json['zeplyn_uuid'] == null ? undefined : json['zeplyn_uuid'],
        'zeplynKind': json['zeplyn_kind'] == null ? undefined : ZeplynKindFromJSON(json['zeplyn_kind']),
        'name': json['name'] == null ? undefined : json['name'],
        'emailAddress': json['email_address'],
    };
}

export function EventParticipantToJSON(value?: EventParticipant | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'zeplyn_uuid': value['zeplynUuid'],
        'zeplyn_kind': ZeplynKindToJSON(value['zeplynKind']),
        'name': value['name'],
        'email_address': value['emailAddress'],
    };
}

