/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface MailtoResponse
 */
export interface MailtoResponse {
    /**
     * 
     * @type {string}
     * @memberof MailtoResponse
     */
    mailtoLink: string;
}

/**
 * Check if a given object implements the MailtoResponse interface.
 */
export function instanceOfMailtoResponse(value: object): value is MailtoResponse {
    if (!('mailtoLink' in value) || value['mailtoLink'] === undefined) return false;
    return true;
}

export function MailtoResponseFromJSON(json: any): MailtoResponse {
    return MailtoResponseFromJSONTyped(json, false);
}

export function MailtoResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): MailtoResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'mailtoLink': json['mailto_link'],
    };
}

export function MailtoResponseToJSON(value?: MailtoResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'mailto_link': value['mailtoLink'],
    };
}

