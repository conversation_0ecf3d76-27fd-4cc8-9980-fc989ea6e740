/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CreateTaskResponse
 */
export interface CreateTaskResponse {
    /**
     * 
     * @type {string}
     * @memberof CreateTaskResponse
     */
    taskUuid: string;
}

/**
 * Check if a given object implements the CreateTaskResponse interface.
 */
export function instanceOfCreateTaskResponse(value: object): value is CreateTaskResponse {
    if (!('taskUuid' in value) || value['taskUuid'] === undefined) return false;
    return true;
}

export function CreateTaskResponseFromJSON(json: any): CreateTaskResponse {
    return CreateTaskResponseFromJSONTyped(json, false);
}

export function CreateTaskResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateTaskResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'taskUuid': json['task_uuid'],
    };
}

export function CreateTaskResponseToJSON(value?: CreateTaskResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'task_uuid': value['taskUuid'],
    };
}

