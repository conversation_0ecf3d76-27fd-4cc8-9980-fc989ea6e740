/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PlanUser
 */
export interface PlanUser {
    /**
     * 
     * @type {string}
     * @memberof PlanUser
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof PlanUser
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof PlanUser
     */
    email: string;
}

/**
 * Check if a given object implements the PlanUser interface.
 */
export function instanceOfPlanUser(value: object): value is PlanUser {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    return true;
}

export function PlanUserFromJSON(json: any): PlanUser {
    return PlanUserFromJSONTyped(json, false);
}

export function PlanUserFromJSONTyped(json: any, ignoreDiscriminator: boolean): PlanUser {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'email': json['email'],
    };
}

export function PlanUserToJSON(value?: PlanUser | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'email': value['email'],
    };
}

