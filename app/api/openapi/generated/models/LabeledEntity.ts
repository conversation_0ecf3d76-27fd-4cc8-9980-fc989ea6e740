/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface LabeledEntity
 */
export interface LabeledEntity {
    /**
     * 
     * @type {string}
     * @memberof LabeledEntity
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof LabeledEntity
     */
    label: string;
}

/**
 * Check if a given object implements the LabeledEntity interface.
 */
export function instanceOfLabeledEntity(value: object): value is LabeledEntity {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function LabeledEntityFromJSON(json: any): LabeledEntity {
    return LabeledEntityFromJSONTyped(json, false);
}

export function LabeledEntityFromJSONTyped(json: any, ignoreDiscriminator: boolean): LabeledEntity {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
    };
}

export function LabeledEntityToJSON(value?: LabeledEntity | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
    };
}

