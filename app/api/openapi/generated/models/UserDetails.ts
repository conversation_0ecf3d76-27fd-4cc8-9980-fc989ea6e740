/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { UserLicenseType } from './UserLicenseType';
import {
    UserLicenseTypeFromJSON,
    UserLicenseTypeFromJSONTyped,
    UserLicenseTypeToJSON,
} from './UserLicenseType';

/**
 * 
 * @export
 * @interface UserDetails
 */
export interface UserDetails {
    /**
     * 
     * @type {string}
     * @memberof UserDetails
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof UserDetails
     */
    email: string;
    /**
     * 
     * @type {boolean}
     * @memberof UserDetails
     */
    isActive: boolean;
    /**
     * 
     * @type {string}
     * @memberof UserDetails
     */
    status: string;
    /**
     * 
     * @type {string}
     * @memberof UserDetails
     */
    firstName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDetails
     */
    lastName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDetails
     */
    orgId?: string | null;
    /**
     * 
     * @type {UserLicenseType}
     * @memberof UserDetails
     */
    licenseType: UserLicenseType;
}



/**
 * Check if a given object implements the UserDetails interface.
 */
export function instanceOfUserDetails(value: object): value is UserDetails {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('isActive' in value) || value['isActive'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('licenseType' in value) || value['licenseType'] === undefined) return false;
    return true;
}

export function UserDetailsFromJSON(json: any): UserDetails {
    return UserDetailsFromJSONTyped(json, false);
}

export function UserDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'email': json['email'],
        'isActive': json['is_active'],
        'status': json['status'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'orgId': json['org_id'] == null ? undefined : json['org_id'],
        'licenseType': UserLicenseTypeFromJSON(json['license_type']),
    };
}

export function UserDetailsToJSON(value?: UserDetails | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'email': value['email'],
        'is_active': value['isActive'],
        'status': value['status'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'org_id': value['orgId'],
        'license_type': UserLicenseTypeToJSON(value['licenseType']),
    };
}

