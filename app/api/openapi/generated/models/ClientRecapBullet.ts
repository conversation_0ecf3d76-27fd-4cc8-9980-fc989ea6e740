/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ClientRecapBullet
 */
export interface ClientRecapBullet {
    /**
     * 
     * @type {string}
     * @memberof ClientRecapBullet
     */
    text: string;
    /**
     * 
     * @type {Array<number>}
     * @memberof ClientRecapBullet
     */
    references: Array<number>;
}

/**
 * Check if a given object implements the ClientRecapBullet interface.
 */
export function instanceOfClientRecapBullet(value: object): value is ClientRecapBullet {
    if (!('text' in value) || value['text'] === undefined) return false;
    if (!('references' in value) || value['references'] === undefined) return false;
    return true;
}

export function ClientRecapBulletFromJSON(json: any): ClientRecapBullet {
    return ClientRecapBulletFromJSONTyped(json, false);
}

export function ClientRecapBulletFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapBullet {
    if (json == null) {
        return json;
    }
    return {
        
        'text': json['text'],
        'references': json['references'],
    };
}

export function ClientRecapBulletToJSON(value?: ClientRecapBullet | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'text': value['text'],
        'references': value['references'],
    };
}

