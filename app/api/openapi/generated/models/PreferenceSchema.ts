/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { UISchema } from './UISchema';
import {
    UISchemaFromJSON,
    UISchemaFromJSONTyped,
    UISchemaToJSON,
} from './UISchema';

/**
 * 
 * @export
 * @interface PreferenceSchema
 */
export interface PreferenceSchema {
    /**
     * Title of the preference object
     * @type {string}
     * @memberof PreferenceSchema
     */
    title: string;
    /**
     * JSON schema for the preference object
     * @type {{ [key: string]: any; }}
     * @memberof PreferenceSchema
     */
    jsonSchema: { [key: string]: any; };
    /**
     * UI schema for the preference object
     * @type {UISchema}
     * @memberof PreferenceSchema
     */
    uiSchema: UISchema;
    /**
     * The current preference values
     * @type {{ [key: string]: any; }}
     * @memberof PreferenceSchema
     */
    data: { [key: string]: any; };
}

/**
 * Check if a given object implements the PreferenceSchema interface.
 */
export function instanceOfPreferenceSchema(value: object): value is PreferenceSchema {
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('jsonSchema' in value) || value['jsonSchema'] === undefined) return false;
    if (!('uiSchema' in value) || value['uiSchema'] === undefined) return false;
    if (!('data' in value) || value['data'] === undefined) return false;
    return true;
}

export function PreferenceSchemaFromJSON(json: any): PreferenceSchema {
    return PreferenceSchemaFromJSONTyped(json, false);
}

export function PreferenceSchemaFromJSONTyped(json: any, ignoreDiscriminator: boolean): PreferenceSchema {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
        'jsonSchema': json['json_schema'],
        'uiSchema': UISchemaFromJSON(json['ui_schema']),
        'data': json['data'],
    };
}

export function PreferenceSchemaToJSON(value?: PreferenceSchema | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'title': value['title'],
        'json_schema': value['jsonSchema'],
        'ui_schema': UISchemaToJSON(value['uiSchema']),
        'data': value['data'],
    };
}

