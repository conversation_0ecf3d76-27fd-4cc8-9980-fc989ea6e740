/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PreferenceSchema } from './PreferenceSchema';
import {
    PreferenceSchemaFromJSON,
    PreferenceSchemaFromJSONTyped,
    PreferenceSchemaToJSON,
} from './PreferenceSchema';

/**
 * 
 * @export
 * @interface PreferencesResponse
 */
export interface PreferencesResponse {
    /**
     * User's preferences
     * @type {Array<PreferenceSchema>}
     * @memberof PreferencesResponse
     */
    userPreferences: Array<PreferenceSchema>;
    /**
     * Organization-level preferences (if the user has access to these preferences).
     * @type {Array<PreferenceSchema>}
     * @memberof PreferencesResponse
     */
    orgPreferences: Array<PreferenceSchema>;
}

/**
 * Check if a given object implements the PreferencesResponse interface.
 */
export function instanceOfPreferencesResponse(value: object): value is PreferencesResponse {
    if (!('userPreferences' in value) || value['userPreferences'] === undefined) return false;
    if (!('orgPreferences' in value) || value['orgPreferences'] === undefined) return false;
    return true;
}

export function PreferencesResponseFromJSON(json: any): PreferencesResponse {
    return PreferencesResponseFromJSONTyped(json, false);
}

export function PreferencesResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): PreferencesResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'userPreferences': ((json['user_preferences'] as Array<any>).map(PreferenceSchemaFromJSON)),
        'orgPreferences': ((json['org_preferences'] as Array<any>).map(PreferenceSchemaFromJSON)),
    };
}

export function PreferencesResponseToJSON(value?: PreferencesResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'user_preferences': ((value['userPreferences'] as Array<any>).map(PreferenceSchemaToJSON)),
        'org_preferences': ((value['orgPreferences'] as Array<any>).map(PreferenceSchemaToJSON)),
    };
}

