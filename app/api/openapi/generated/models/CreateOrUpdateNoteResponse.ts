/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON>eplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CreateOrUpdateNoteResponse
 */
export interface CreateOrUpdateNoteResponse {
    /**
     * 
     * @type {string}
     * @memberof CreateOrUpdateNoteResponse
     */
    noteId: string;
    /**
     * 
     * @type {boolean}
     * @memberof CreateOrUpdateNoteResponse
     */
    completed: boolean;
    /**
     * 
     * @type {string}
     * @memberof CreateOrUpdateNoteResponse
     */
    botId?: string | null;
}

/**
 * Check if a given object implements the CreateOrUpdateNoteResponse interface.
 */
export function instanceOfCreateOrUpdateNoteResponse(value: object): value is CreateOrUpdateNoteResponse {
    if (!('noteId' in value) || value['noteId'] === undefined) return false;
    if (!('completed' in value) || value['completed'] === undefined) return false;
    return true;
}

export function CreateOrUpdateNoteResponseFromJSON(json: any): CreateOrUpdateNoteResponse {
    return CreateOrUpdateNoteResponseFromJSONTyped(json, false);
}

export function CreateOrUpdateNoteResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateOrUpdateNoteResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'noteId': json['note_id'],
        'completed': json['completed'],
        'botId': json['bot_id'] == null ? undefined : json['bot_id'],
    };
}

export function CreateOrUpdateNoteResponseToJSON(value?: CreateOrUpdateNoteResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'note_id': value['noteId'],
        'completed': value['completed'],
        'bot_id': value['botId'],
    };
}

