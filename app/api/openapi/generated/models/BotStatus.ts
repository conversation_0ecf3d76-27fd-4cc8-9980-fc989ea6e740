/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const BotStatus = {
    Unknown: 'unknown',
    NotCreated: 'not_created',
    Scheduled: 'scheduled',
    InWaitingRoom: 'in_waiting_room',
    InCallNotRecording: 'in_call_not_recording',
    InCallRecording: 'in_call_recording',
    CallEnded: 'call_ended',
    RecordingDone: 'recording_done',
    Error: 'error'
} as const;
export type BotStatus = typeof BotStatus[keyof typeof BotStatus];


export function instanceOfBotStatus(value: any): boolean {
    for (const key in BotStatus) {
        if (Object.prototype.hasOwnProperty.call(BotStatus, key)) {
            if (BotStatus[key as keyof typeof BotStatus] === value) {
                return true;
            }
        }
    }
    return false;
}

export function BotStatusFromJSON(json: any): BotStatus {
    return BotStatusFromJSONTyped(json, false);
}

export function BotStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): BotStatus {
    return json as BotStatus;
}

export function BotStatusToJSON(value?: BotStatus | null): any {
    return value as any;
}

