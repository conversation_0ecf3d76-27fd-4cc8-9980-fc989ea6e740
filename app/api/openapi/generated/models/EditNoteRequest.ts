/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ActionItemUpdate } from './ActionItemUpdate';
import {
    ActionItemUpdateFromJSON,
    ActionItemUpdateFromJSONTyped,
    ActionItemUpdateToJSON,
} from './ActionItemUpdate';
import type { AttendeeInfo } from './AttendeeInfo';
import {
    AttendeeInfoFromJSON,
    AttendeeInfoFromJSONTyped,
    AttendeeInfoToJSON,
} from './AttendeeInfo';
import type { ClientInput } from './ClientInput';
import {
    ClientInputFromJSON,
    ClientInputFromJSONTyped,
    ClientInputToJSON,
} from './ClientInput';
import type { Summary } from './Summary';
import {
    SummaryFromJSON,
    SummaryFromJSONTyped,
    SummaryToJSON,
} from './Summary';

/**
 * Request model for editing a note. Fields that are None indicate no changes requested,
 * while empty lists/values indicate explicit clearing of that field.
 * @export
 * @interface EditNoteRequest
 */
export interface EditNoteRequest {
    /**
     * 
     * @type {string}
     * @memberof EditNoteRequest
     */
    meetingName?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof EditNoteRequest
     */
    advisorNotes?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof EditNoteRequest
     */
    keyTakeaways?: Array<string> | null;
    /**
     * 
     * @type {Array<ActionItemUpdate>}
     * @memberof EditNoteRequest
     */
    actionItems?: Array<ActionItemUpdate> | null;
    /**
     * 
     * @type {ClientInput}
     * @memberof EditNoteRequest
     */
    client?: ClientInput | null;
    /**
     * 
     * @type {Summary}
     * @memberof EditNoteRequest
     */
    summary?: Summary | null;
    /**
     * 
     * @type {Array<AttendeeInfo>}
     * @memberof EditNoteRequest
     */
    attendees?: Array<AttendeeInfo> | null;
}

/**
 * Check if a given object implements the EditNoteRequest interface.
 */
export function instanceOfEditNoteRequest(value: object): value is EditNoteRequest {
    return true;
}

export function EditNoteRequestFromJSON(json: any): EditNoteRequest {
    return EditNoteRequestFromJSONTyped(json, false);
}

export function EditNoteRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): EditNoteRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'meetingName': json['meetingName'] == null ? undefined : json['meetingName'],
        'advisorNotes': json['advisorNotes'] == null ? undefined : json['advisorNotes'],
        'keyTakeaways': json['keyTakeaways'] == null ? undefined : json['keyTakeaways'],
        'actionItems': json['actionItems'] == null ? undefined : ((json['actionItems'] as Array<any>).map(ActionItemUpdateFromJSON)),
        'client': json['client'] == null ? undefined : ClientInputFromJSON(json['client']),
        'summary': json['summary'] == null ? undefined : SummaryFromJSON(json['summary']),
        'attendees': json['attendees'] == null ? undefined : ((json['attendees'] as Array<any>).map(AttendeeInfoFromJSON)),
    };
}

export function EditNoteRequestToJSON(value?: EditNoteRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'meetingName': value['meetingName'],
        'advisorNotes': value['advisorNotes'],
        'keyTakeaways': value['keyTakeaways'],
        'actionItems': value['actionItems'] == null ? undefined : ((value['actionItems'] as Array<any>).map(ActionItemUpdateToJSON)),
        'client': ClientInputToJSON(value['client']),
        'summary': SummaryToJSON(value['summary']),
        'attendees': value['attendees'] == null ? undefined : ((value['attendees'] as Array<any>).map(AttendeeInfoToJSON)),
    };
}

