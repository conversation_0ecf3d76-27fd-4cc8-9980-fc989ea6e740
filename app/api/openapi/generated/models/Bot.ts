/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { BotMeetingType } from './BotMeetingType';
import {
    BotMeetingTypeFromJSON,
    BotMeetingTypeFromJSONTyped,
    BotMeetingTypeToJSON,
} from './BotMeetingType';
import type { BotStatus } from './BotStatus';
import {
    BotStatusFromJSON,
    BotStatusFromJSONTyped,
    BotStatusToJSON,
} from './BotStatus';

/**
 * 
 * @export
 * @interface Bot
 */
export interface Bot {
    /**
     * The UUID of the bot
     * @type {string}
     * @memberof Bot
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof Bot
     */
    platformId: string | null;
    /**
     * 
     * @type {string}
     * @memberof Bot
     */
    meetingLink: string | null;
    /**
     * The current status of the bot
     * @type {BotStatus}
     * @memberof Bot
     */
    status: BotStatus;
    /**
     * Whether the bot supports pausing and resuming recording
     * @type {boolean}
     * @memberof Bot
     */
    supportsPauseResume: boolean;
    /**
     * The type of meeting that the bot is in
     * @type {BotMeetingType}
     * @memberof Bot
     */
    meetingType: BotMeetingType;
}



/**
 * Check if a given object implements the Bot interface.
 */
export function instanceOfBot(value: object): value is Bot {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('platformId' in value) || value['platformId'] === undefined) return false;
    if (!('meetingLink' in value) || value['meetingLink'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('supportsPauseResume' in value) || value['supportsPauseResume'] === undefined) return false;
    if (!('meetingType' in value) || value['meetingType'] === undefined) return false;
    return true;
}

export function BotFromJSON(json: any): Bot {
    return BotFromJSONTyped(json, false);
}

export function BotFromJSONTyped(json: any, ignoreDiscriminator: boolean): Bot {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'platformId': json['platform_id'],
        'meetingLink': json['meeting_link'],
        'status': BotStatusFromJSON(json['status']),
        'supportsPauseResume': json['supports_pause_resume'],
        'meetingType': BotMeetingTypeFromJSON(json['meeting_type']),
    };
}

export function BotToJSON(value?: Bot | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'platform_id': value['platformId'],
        'meeting_link': value['meetingLink'],
        'status': BotStatusToJSON(value['status']),
        'supports_pause_resume': value['supportsPauseResume'],
        'meeting_type': BotMeetingTypeToJSON(value['meetingType']),
    };
}

