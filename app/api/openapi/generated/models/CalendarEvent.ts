/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { LinkedCRMEntity } from './LinkedCRMEntity';
import {
    LinkedCRMEntityFromJSON,
    LinkedCRMEntityFromJSONTyped,
    LinkedCRMEntityToJSON,
} from './LinkedCRMEntity';
import type { EventParticipant } from './EventParticipant';
import {
    EventParticipantFromJSON,
    EventParticipantFromJSONTyped,
    EventParticipantToJSON,
} from './EventParticipant';

/**
 * 
 * @export
 * @interface CalendarEvent
 */
export interface CalendarEvent {
    /**
     * 
     * @type {string}
     * @memberof CalendarEvent
     */
    provider: string;
    /**
     * 
     * @type {string}
     * @memberof CalendarEvent
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof CalendarEvent
     */
    userSpecificId: string;
    /**
     * 
     * @type {string}
     * @memberof CalendarEvent
     */
    title: string;
    /**
     * 
     * @type {Date}
     * @memberof CalendarEvent
     */
    startTime: Date;
    /**
     * 
     * @type {Date}
     * @memberof CalendarEvent
     */
    endTime: Date;
    /**
     * 
     * @type {boolean}
     * @memberof CalendarEvent
     */
    allDay: boolean;
    /**
     * 
     * @type {Array<EventParticipant>}
     * @memberof CalendarEvent
     */
    participants: Array<EventParticipant>;
    /**
     * 
     * @type {Array<string>}
     * @memberof CalendarEvent
     */
    meetingUrls: Array<string>;
    /**
     * 
     * @type {LinkedCRMEntity}
     * @memberof CalendarEvent
     */
    linkedCrmEntity?: LinkedCRMEntity | null;
}

/**
 * Check if a given object implements the CalendarEvent interface.
 */
export function instanceOfCalendarEvent(value: object): value is CalendarEvent {
    if (!('provider' in value) || value['provider'] === undefined) return false;
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('userSpecificId' in value) || value['userSpecificId'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('startTime' in value) || value['startTime'] === undefined) return false;
    if (!('endTime' in value) || value['endTime'] === undefined) return false;
    if (!('allDay' in value) || value['allDay'] === undefined) return false;
    if (!('participants' in value) || value['participants'] === undefined) return false;
    if (!('meetingUrls' in value) || value['meetingUrls'] === undefined) return false;
    return true;
}

export function CalendarEventFromJSON(json: any): CalendarEvent {
    return CalendarEventFromJSONTyped(json, false);
}

export function CalendarEventFromJSONTyped(json: any, ignoreDiscriminator: boolean): CalendarEvent {
    if (json == null) {
        return json;
    }
    return {
        
        'provider': json['provider'],
        'id': json['id'],
        'userSpecificId': json['user_specific_id'],
        'title': json['title'],
        'startTime': (new Date(json['start_time'])),
        'endTime': (new Date(json['end_time'])),
        'allDay': json['all_day'],
        'participants': ((json['participants'] as Array<any>).map(EventParticipantFromJSON)),
        'meetingUrls': json['meeting_urls'],
        'linkedCrmEntity': json['linked_crm_entity'] == null ? undefined : LinkedCRMEntityFromJSON(json['linked_crm_entity']),
    };
}

export function CalendarEventToJSON(value?: CalendarEvent | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'provider': value['provider'],
        'id': value['id'],
        'user_specific_id': value['userSpecificId'],
        'title': value['title'],
        'start_time': ((value['startTime']).toISOString()),
        'end_time': ((value['endTime']).toISOString()),
        'all_day': value['allDay'],
        'participants': ((value['participants'] as Array<any>).map(EventParticipantToJSON)),
        'meeting_urls': value['meetingUrls'],
        'linked_crm_entity': LinkedCRMEntityToJSON(value['linkedCrmEntity']),
    };
}

