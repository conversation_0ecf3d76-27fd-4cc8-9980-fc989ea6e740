/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const ClientRecapStatus = {
    Unknown: 'unknown',
    Created: 'created',
    Processing: 'processing',
    Processed: 'processed',
    Failed: 'failed'
} as const;
export type ClientRecapStatus = typeof ClientRecapStatus[keyof typeof ClientRecapStatus];


export function instanceOfClientRecapStatus(value: any): boolean {
    for (const key in ClientRecapStatus) {
        if (Object.prototype.hasOwnProperty.call(ClientRecapStatus, key)) {
            if (ClientRecapStatus[key as keyof typeof ClientRecapStatus] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ClientRecapStatusFromJSON(json: any): ClientRecapStatus {
    return ClientRecapStatusFromJSONTyped(json, false);
}

export function ClientRecapStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapStatus {
    return json as ClientRecapStatus;
}

export function ClientRecapStatusToJSON(value?: ClientRecapStatus | null): any {
    return value as any;
}

