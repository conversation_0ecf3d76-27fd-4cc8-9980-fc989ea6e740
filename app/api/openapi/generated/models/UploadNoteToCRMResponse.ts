/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CRMUploadTarget } from './CRMUploadTarget';
import {
    CRMUploadTargetFromJSON,
    CRMUploadTargetFromJSONTyped,
    CRMUploadTargetToJSON,
} from './CRMUploadTarget';

/**
 * 
 * @export
 * @interface UploadNoteToCRMResponse
 */
export interface UploadNoteToCRMResponse {
    /**
     * Indicates if user input is required before uploading the note to CRM
     * @type {boolean}
     * @memberof UploadNoteToCRMResponse
     */
    userInputRequired?: boolean;
    /**
     * 
     * @type {Array<CRMUploadTarget>}
     * @memberof UploadNoteToCRMResponse
     */
    noteSyncTargets?: Array<CRMUploadTarget> | null;
}

/**
 * Check if a given object implements the UploadNoteToCRMResponse interface.
 */
export function instanceOfUploadNoteToCRMResponse(value: object): value is UploadNoteToCRMResponse {
    return true;
}

export function UploadNoteToCRMResponseFromJSON(json: any): UploadNoteToCRMResponse {
    return UploadNoteToCRMResponseFromJSONTyped(json, false);
}

export function UploadNoteToCRMResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): UploadNoteToCRMResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'userInputRequired': json['user_input_required'] == null ? undefined : json['user_input_required'],
        'noteSyncTargets': json['note_sync_targets'] == null ? undefined : ((json['note_sync_targets'] as Array<any>).map(CRMUploadTargetFromJSON)),
    };
}

export function UploadNoteToCRMResponseToJSON(value?: UploadNoteToCRMResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'user_input_required': value['userInputRequired'],
        'note_sync_targets': value['noteSyncTargets'] == null ? undefined : ((value['noteSyncTargets'] as Array<any>).map(CRMUploadTargetToJSON)),
    };
}

