/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Utterance } from './Utterance';
import {
    UtteranceFromJSON,
    UtteranceFromJSONTyped,
    UtteranceToJSON,
} from './Utterance';

/**
 * 
 * @export
 * @interface Transcript
 */
export interface Transcript {
    /**
     * 
     * @type {Array<Utterance>}
     * @memberof Transcript
     */
    utterances?: Array<Utterance>;
}

/**
 * Check if a given object implements the Transcript interface.
 */
export function instanceOfTranscript(value: object): value is Transcript {
    return true;
}

export function TranscriptFromJSON(json: any): Transcript {
    return TranscriptFromJSONTyped(json, false);
}

export function TranscriptFromJSONTyped(json: any, ignoreDiscriminator: boolean): Transcript {
    if (json == null) {
        return json;
    }
    return {
        
        'utterances': json['utterances'] == null ? undefined : ((json['utterances'] as Array<any>).map(UtteranceFromJSON)),
    };
}

export function TranscriptToJSON(value?: Transcript | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'utterances': value['utterances'] == null ? undefined : ((value['utterances'] as Array<any>).map(UtteranceToJSON)),
    };
}

