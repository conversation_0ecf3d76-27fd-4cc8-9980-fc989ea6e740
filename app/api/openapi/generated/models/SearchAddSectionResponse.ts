/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * The response model for the add section to summary endpoint
 * @export
 * @interface SearchAddSectionResponse
 */
export interface SearchAddSectionResponse {
    /**
     * 
     * @type {number}
     * @memberof SearchAddSectionResponse
     */
    sectionIndex: number;
}

/**
 * Check if a given object implements the SearchAddSectionResponse interface.
 */
export function instanceOfSearchAddSectionResponse(value: object): value is SearchAddSectionResponse {
    if (!('sectionIndex' in value) || value['sectionIndex'] === undefined) return false;
    return true;
}

export function SearchAddSectionResponseFromJSON(json: any): SearchAddSectionResponse {
    return SearchAddSectionResponseFromJSONTyped(json, false);
}

export function SearchAddSectionResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchAddSectionResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'sectionIndex': json['section_index'],
    };
}

export function SearchAddSectionResponseToJSON(value?: SearchAddSectionResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'section_index': value['sectionIndex'],
    };
}

