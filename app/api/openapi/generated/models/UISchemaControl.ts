/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface UISchemaControl
 */
export interface UISchemaControl {
    /**
     * Type of the UI control
     * @type {string}
     * @memberof UISchemaControl
     */
    type: string;
    /**
     * Reference to the property in the JSON schema
     * @type {string}
     * @memberof UISchemaControl
     */
    scope: string;
}

/**
 * Check if a given object implements the UISchemaControl interface.
 */
export function instanceOfUISchemaControl(value: object): value is UISchemaControl {
    if (!('type' in value) || value['type'] === undefined) return false;
    if (!('scope' in value) || value['scope'] === undefined) return false;
    return true;
}

export function UISchemaControlFromJSON(json: any): UISchemaControl {
    return UISchemaControlFromJSONTyped(json, false);
}

export function UISchemaControlFromJSONTyped(json: any, ignoreDiscriminator: boolean): UISchemaControl {
    if (json == null) {
        return json;
    }
    return {
        
        'type': json['type'],
        'scope': json['scope'],
    };
}

export function UISchemaControlToJSON(value?: UISchemaControl | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'type': value['type'],
        'scope': value['scope'],
    };
}

