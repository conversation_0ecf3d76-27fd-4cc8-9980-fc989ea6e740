/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { MenuItemId } from './MenuItemId';
import {
    MenuItemIdFromJSON,
    MenuItemIdFromJSONTyped,
    MenuItemIdToJSON,
} from './MenuItemId';

/**
 * 
 * @export
 * @interface MenuItem
 */
export interface MenuItem {
    /**
     * 
     * @type {MenuItemId}
     * @memberof MenuItem
     */
    id: MenuItemId;
    /**
     * 
     * @type {string}
     * @memberof MenuItem
     */
    label: string;
    /**
     * 
     * @type {Array<MenuItem>}
     * @memberof MenuItem
     */
    items?: Array<MenuItem> | null;
}



/**
 * Check if a given object implements the MenuItem interface.
 */
export function instanceOfMenuItem(value: object): value is MenuItem {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function MenuItemFromJSON(json: any): MenuItem {
    return MenuItemFromJSONTyped(json, false);
}

export function MenuItemFromJSONTyped(json: any, ignoreDiscriminator: boolean): MenuItem {
    if (json == null) {
        return json;
    }
    return {
        
        'id': MenuItemIdFromJSON(json['id']),
        'label': json['label'],
        'items': json['items'] == null ? undefined : ((json['items'] as Array<any>).map(MenuItemFromJSON)),
    };
}

export function MenuItemToJSON(value?: MenuItem | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': MenuItemIdToJSON(value['id']),
        'label': value['label'],
        'items': value['items'] == null ? undefined : ((value['items'] as Array<any>).map(MenuItemToJSON)),
    };
}

