/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientRecapCategory } from './ClientRecapCategory';
import {
    ClientRecapCategoryFromJSON,
    ClientRecapCategoryFromJSONTyped,
    ClientRecapCategoryToJSON,
} from './ClientRecapCategory';
import type { ClientRecapReference } from './ClientRecapReference';
import {
    ClientRecapReferenceFromJSON,
    ClientRecapReferenceFromJSONTyped,
    ClientRecapReferenceToJSON,
} from './ClientRecapReference';

/**
 * 
 * @export
 * @interface ClientRecapOutput
 */
export interface ClientRecapOutput {
    /**
     * 
     * @type {Array<ClientRecapCategory>}
     * @memberof ClientRecapOutput
     */
    recap: Array<ClientRecapCategory>;
    /**
     * 
     * @type {Array<ClientRecapReference>}
     * @memberof ClientRecapOutput
     */
    references: Array<ClientRecapReference>;
    /**
     * 
     * @type {Date}
     * @memberof ClientRecapOutput
     */
    createdAt: Date;
}

/**
 * Check if a given object implements the ClientRecapOutput interface.
 */
export function instanceOfClientRecapOutput(value: object): value is ClientRecapOutput {
    if (!('recap' in value) || value['recap'] === undefined) return false;
    if (!('references' in value) || value['references'] === undefined) return false;
    if (!('createdAt' in value) || value['createdAt'] === undefined) return false;
    return true;
}

export function ClientRecapOutputFromJSON(json: any): ClientRecapOutput {
    return ClientRecapOutputFromJSONTyped(json, false);
}

export function ClientRecapOutputFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapOutput {
    if (json == null) {
        return json;
    }
    return {
        
        'recap': ((json['recap'] as Array<any>).map(ClientRecapCategoryFromJSON)),
        'references': ((json['references'] as Array<any>).map(ClientRecapReferenceFromJSON)),
        'createdAt': (new Date(json['created_at'])),
    };
}

export function ClientRecapOutputToJSON(value?: ClientRecapOutput | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'recap': ((value['recap'] as Array<any>).map(ClientRecapCategoryToJSON)),
        'references': ((value['references'] as Array<any>).map(ClientRecapReferenceToJSON)),
        'created_at': ((value['createdAt']).toISOString()),
    };
}

