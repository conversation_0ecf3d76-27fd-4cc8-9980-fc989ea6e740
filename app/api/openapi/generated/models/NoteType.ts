/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Enum matching the Note.NOTE_TYPE choices in Django model
 * @export
 */
export const NoteType = {
    MeetingRecording: 'meeting_recording',
    Image: 'image',
    VoiceMemo: 'voice_memo',
    Transcript: 'transcript'
} as const;
export type NoteType = typeof NoteType[keyof typeof NoteType];


export function instanceOfNoteType(value: any): boolean {
    for (const key in NoteType) {
        if (Object.prototype.hasOwnProperty.call(NoteType, key)) {
            if (NoteType[key as keyof typeof NoteType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function NoteTypeFromJSON(json: any): NoteType {
    return NoteTypeFromJSONTyped(json, false);
}

export function NoteTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): NoteType {
    return json as NoteType;
}

export function NoteTypeToJSON(value?: NoteType | null): any {
    return value as any;
}

