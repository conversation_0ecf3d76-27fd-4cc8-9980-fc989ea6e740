/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface RedtailStatusResponse
 */
export interface RedtailStatusResponse {
    /**
     * 
     * @type {boolean}
     * @memberof RedtailStatusResponse
     */
    active: boolean;
}

/**
 * Check if a given object implements the RedtailStatusResponse interface.
 */
export function instanceOfRedtailStatusResponse(value: object): value is RedtailStatusResponse {
    if (!('active' in value) || value['active'] === undefined) return false;
    return true;
}

export function RedtailStatusResponseFromJSON(json: any): RedtailStatusResponse {
    return RedtailStatusResponseFromJSONTyped(json, false);
}

export function RedtailStatusResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RedtailStatusResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'active': json['active'],
    };
}

export function RedtailStatusResponseToJSON(value?: RedtailStatusResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'active': value['active'],
    };
}

