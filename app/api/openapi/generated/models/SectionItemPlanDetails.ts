/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';
import type { OrganizationPlanDetails } from './OrganizationPlanDetails';
import {
    OrganizationPlanDetailsFromJSON,
    OrganizationPlanDetailsFromJSONTyped,
    OrganizationPlanDetailsToJSON,
} from './OrganizationPlanDetails';

/**
 * 
 * @export
 * @interface SectionItemPlanDetails
 */
export interface SectionItemPlanDetails {
    /**
     * 
     * @type {string}
     * @memberof SectionItemPlanDetails
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemPlanDetails
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemPlanDetails
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {string}
     * @memberof SectionItemPlanDetails
     */
    planName?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SectionItemPlanDetails
     */
    enabledFeatures?: Array<string>;
    /**
     * 
     * @type {OrganizationPlanDetails}
     * @memberof SectionItemPlanDetails
     */
    organizationPlanDetails?: OrganizationPlanDetails | null;
}



/**
 * Check if a given object implements the SectionItemPlanDetails interface.
 */
export function instanceOfSectionItemPlanDetails(value: object): value is SectionItemPlanDetails {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemPlanDetailsFromJSON(json: any): SectionItemPlanDetails {
    return SectionItemPlanDetailsFromJSONTyped(json, false);
}

export function SectionItemPlanDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemPlanDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'planName': json['plan_name'] == null ? undefined : json['plan_name'],
        'enabledFeatures': json['enabled_features'] == null ? undefined : json['enabled_features'],
        'organizationPlanDetails': json['organization_plan_details'] == null ? undefined : OrganizationPlanDetailsFromJSON(json['organization_plan_details']),
    };
}

export function SectionItemPlanDetailsToJSON(value?: SectionItemPlanDetails | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'plan_name': value['planName'],
        'enabled_features': value['enabledFeatures'],
        'organization_plan_details': OrganizationPlanDetailsToJSON(value['organizationPlanDetails']),
    };
}

