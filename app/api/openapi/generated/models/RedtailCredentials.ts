/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface RedtailCredentials
 */
export interface RedtailCredentials {
    /**
     * 
     * @type {string}
     * @memberof RedtailCredentials
     */
    username: string;
    /**
     * 
     * @type {string}
     * @memberof RedtailCredentials
     */
    password: string;
}

/**
 * Check if a given object implements the RedtailCredentials interface.
 */
export function instanceOfRedtailCredentials(value: object): value is RedtailCredentials {
    if (!('username' in value) || value['username'] === undefined) return false;
    if (!('password' in value) || value['password'] === undefined) return false;
    return true;
}

export function RedtailCredentialsFromJSON(json: any): RedtailCredentials {
    return RedtailCredentialsFromJSONTyped(json, false);
}

export function RedtailCredentialsFromJSONTyped(json: any, ignoreDiscriminator: boolean): RedtailCredentials {
    if (json == null) {
        return json;
    }
    return {
        
        'username': json['username'],
        'password': json['password'],
    };
}

export function RedtailCredentialsToJSON(value?: RedtailCredentials | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'username': value['username'],
        'password': value['password'],
    };
}

