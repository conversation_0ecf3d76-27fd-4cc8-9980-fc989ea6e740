/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SaveRequest
 */
export interface SaveRequest {
    /**
     * 
     * @type {string}
     * @memberof SaveRequest
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SaveRequest
     */
    companyName?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SaveRequest
     */
    msCalendarAutojoin?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof SaveRequest
     */
    googleCalendarAutojoin?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof SaveRequest
     */
    calendarLookahead?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SaveRequest
     */
    calendarShowMeetingsWithoutUrls?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof SaveRequest
     */
    bentoTagsEnabled?: boolean | null;
}

/**
 * Check if a given object implements the SaveRequest interface.
 */
export function instanceOfSaveRequest(value: object): value is SaveRequest {
    return true;
}

export function SaveRequestFromJSON(json: any): SaveRequest {
    return SaveRequestFromJSONTyped(json, false);
}

export function SaveRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SaveRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'] == null ? undefined : json['name'],
        'companyName': json['companyName'] == null ? undefined : json['companyName'],
        'msCalendarAutojoin': json['msCalendarAutojoin'] == null ? undefined : json['msCalendarAutojoin'],
        'googleCalendarAutojoin': json['googleCalendarAutojoin'] == null ? undefined : json['googleCalendarAutojoin'],
        'calendarLookahead': json['calendarLookahead'] == null ? undefined : json['calendarLookahead'],
        'calendarShowMeetingsWithoutUrls': json['calendarShowMeetingsWithoutUrls'] == null ? undefined : json['calendarShowMeetingsWithoutUrls'],
        'bentoTagsEnabled': json['bentoTagsEnabled'] == null ? undefined : json['bentoTagsEnabled'],
    };
}

export function SaveRequestToJSON(value?: SaveRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'companyName': value['companyName'],
        'msCalendarAutojoin': value['msCalendarAutojoin'],
        'googleCalendarAutojoin': value['googleCalendarAutojoin'],
        'calendarLookahead': value['calendarLookahead'],
        'calendarShowMeetingsWithoutUrls': value['calendarShowMeetingsWithoutUrls'],
        'bentoTagsEnabled': value['bentoTagsEnabled'],
    };
}

