/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';

/**
 * 
 * @export
 * @interface SectionItemTextField
 */
export interface SectionItemTextField {
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextField
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextField
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemTextField
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextField
     */
    placeholder?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextField
     */
    value?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemTextField
     */
    disabled?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemTextField
     */
    readonly?: boolean | null;
}



/**
 * Check if a given object implements the SectionItemTextField interface.
 */
export function instanceOfSectionItemTextField(value: object): value is SectionItemTextField {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemTextFieldFromJSON(json: any): SectionItemTextField {
    return SectionItemTextFieldFromJSONTyped(json, false);
}

export function SectionItemTextFieldFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemTextField {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'placeholder': json['placeholder'] == null ? undefined : json['placeholder'],
        'value': json['value'] == null ? undefined : json['value'],
        'disabled': json['disabled'] == null ? undefined : json['disabled'],
        'readonly': json['readonly'] == null ? undefined : json['readonly'],
    };
}

export function SectionItemTextFieldToJSON(value?: SectionItemTextField | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'placeholder': value['placeholder'],
        'value': value['value'],
        'disabled': value['disabled'],
        'readonly': value['readonly'],
    };
}

