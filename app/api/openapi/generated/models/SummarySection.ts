/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SummarySection
 */
export interface SummarySection {
    /**
     * 
     * @type {string}
     * @memberof SummarySection
     */
    topic: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof SummarySection
     */
    bullets: Array<string>;
}

/**
 * Check if a given object implements the SummarySection interface.
 */
export function instanceOfSummarySection(value: object): value is SummarySection {
    if (!('topic' in value) || value['topic'] === undefined) return false;
    if (!('bullets' in value) || value['bullets'] === undefined) return false;
    return true;
}

export function SummarySectionFromJSON(json: any): SummarySection {
    return SummarySectionFromJSONTyped(json, false);
}

export function SummarySectionFromJSONTyped(json: any, ignoreDiscriminator: boolean): SummarySection {
    if (json == null) {
        return json;
    }
    return {
        
        'topic': json['topic'],
        'bullets': json['bullets'],
    };
}

export function SummarySectionToJSON(value?: SummarySection | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'topic': value['topic'],
        'bullets': value['bullets'],
    };
}

