/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';

/**
 * 
 * @export
 * @interface SectionItemBooleanField
 */
export interface SectionItemBooleanField {
    /**
     * 
     * @type {string}
     * @memberof SectionItemBooleanField
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemBooleanField
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemBooleanField
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemBooleanField
     */
    value?: boolean | null;
}



/**
 * Check if a given object implements the SectionItemBooleanField interface.
 */
export function instanceOfSectionItemBooleanField(value: object): value is SectionItemBooleanField {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemBooleanFieldFromJSON(json: any): SectionItemBooleanField {
    return SectionItemBooleanFieldFromJSONTyped(json, false);
}

export function SectionItemBooleanFieldFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemBooleanField {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'value': json['value'] == null ? undefined : json['value'],
    };
}

export function SectionItemBooleanFieldToJSON(value?: SectionItemBooleanField | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'value': value['value'],
    };
}

