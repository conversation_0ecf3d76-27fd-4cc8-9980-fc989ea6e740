/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const FollowUpStatus = {
    Created: 'created',
    Processing: 'processing',
    Completed: 'completed',
    Failed: 'failed',
    Unknown: 'unknown'
} as const;
export type FollowUpStatus = typeof FollowUpStatus[keyof typeof FollowUpStatus];


export function instanceOfFollowUpStatus(value: any): boolean {
    for (const key in FollowUpStatus) {
        if (Object.prototype.hasOwnProperty.call(FollowUpStatus, key)) {
            if (FollowUpStatus[key as keyof typeof FollowUpStatus] === value) {
                return true;
            }
        }
    }
    return false;
}

export function FollowUpStatusFromJSON(json: any): FollowUpStatus {
    return FollowUpStatusFromJSONTyped(json, false);
}

export function FollowUpStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): FollowUpStatus {
    return json as FollowUpStatus;
}

export function FollowUpStatusToJSON(value?: FollowUpStatus | null): any {
    return value as any;
}

