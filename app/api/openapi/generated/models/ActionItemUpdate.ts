/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ActionType } from './ActionType';
import {
    ActionTypeFromJSON,
    ActionTypeFromJSONTyped,
    ActionTypeToJSON,
} from './ActionType';
import type { ActionItem } from './ActionItem';
import {
    ActionItemFromJSON,
    ActionItemFromJSONTyped,
    ActionItemToJSON,
} from './ActionItem';

/**
 * 
 * @export
 * @interface ActionItemUpdate
 */
export interface ActionItemUpdate {
    /**
     * 
     * @type {ActionType}
     * @memberof ActionItemUpdate
     */
    action: ActionType;
    /**
     * 
     * @type {ActionItem}
     * @memberof ActionItemUpdate
     */
    item: ActionItem;
}



/**
 * Check if a given object implements the ActionItemUpdate interface.
 */
export function instanceOfActionItemUpdate(value: object): value is ActionItemUpdate {
    if (!('action' in value) || value['action'] === undefined) return false;
    if (!('item' in value) || value['item'] === undefined) return false;
    return true;
}

export function ActionItemUpdateFromJSON(json: any): ActionItemUpdate {
    return ActionItemUpdateFromJSONTyped(json, false);
}

export function ActionItemUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionItemUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'action': ActionTypeFromJSON(json['action']),
        'item': ActionItemFromJSON(json['item']),
    };
}

export function ActionItemUpdateToJSON(value?: ActionItemUpdate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'action': ActionTypeToJSON(value['action']),
        'item': ActionItemToJSON(value['item']),
    };
}

