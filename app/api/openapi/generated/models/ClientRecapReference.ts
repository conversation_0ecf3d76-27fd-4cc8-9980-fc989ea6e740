/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ClientRecapReference
 */
export interface ClientRecapReference {
    /**
     * 
     * @type {string}
     * @memberof ClientRecapReference
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof ClientRecapReference
     */
    source: string;
    /**
     * 
     * @type {string}
     * @memberof ClientRecapReference
     */
    link: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientRecapReference
     */
    hoverText?: string | null;
}

/**
 * Check if a given object implements the ClientRecapReference interface.
 */
export function instanceOfClientRecapReference(value: object): value is ClientRecapReference {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('source' in value) || value['source'] === undefined) return false;
    if (!('link' in value) || value['link'] === undefined) return false;
    return true;
}

export function ClientRecapReferenceFromJSON(json: any): ClientRecapReference {
    return ClientRecapReferenceFromJSONTyped(json, false);
}

export function ClientRecapReferenceFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapReference {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'source': json['source'],
        'link': json['link'],
        'hoverText': json['hover_text'] == null ? undefined : json['hover_text'],
    };
}

export function ClientRecapReferenceToJSON(value?: ClientRecapReference | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'source': value['source'],
        'link': value['link'],
        'hover_text': value['hoverText'],
    };
}

