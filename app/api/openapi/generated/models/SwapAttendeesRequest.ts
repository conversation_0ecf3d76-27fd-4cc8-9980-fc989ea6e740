/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SwapPair } from './SwapPair';
import {
    SwapPairFromJSON,
    SwapPairFromJSONTyped,
    SwapPairToJSON,
} from './SwapPair';

/**
 * 
 * @export
 * @interface SwapAttendeesRequest
 */
export interface SwapAttendeesRequest {
    /**
     * UUID of the note
     * @type {string}
     * @memberof SwapAttendeesRequest
     */
    noteId: string;
    /**
     * List of alias swaps to perform
     * @type {Array<SwapPair>}
     * @memberof SwapAttendeesRequest
     */
    swaps: Array<SwapPair>;
}

/**
 * Check if a given object implements the SwapAttendeesRequest interface.
 */
export function instanceOfSwapAttendeesRequest(value: object): value is SwapAttendeesRequest {
    if (!('noteId' in value) || value['noteId'] === undefined) return false;
    if (!('swaps' in value) || value['swaps'] === undefined) return false;
    return true;
}

export function SwapAttendeesRequestFromJSON(json: any): SwapAttendeesRequest {
    return SwapAttendeesRequestFromJSONTyped(json, false);
}

export function SwapAttendeesRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SwapAttendeesRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'noteId': json['note_id'],
        'swaps': ((json['swaps'] as Array<any>).map(SwapPairFromJSON)),
    };
}

export function SwapAttendeesRequestToJSON(value?: SwapAttendeesRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'note_id': value['noteId'],
        'swaps': ((value['swaps'] as Array<any>).map(SwapPairToJSON)),
    };
}

