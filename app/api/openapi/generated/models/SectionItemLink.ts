/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';

/**
 * 
 * @export
 * @interface SectionItemLink
 */
export interface SectionItemLink {
    /**
     * 
     * @type {string}
     * @memberof SectionItemLink
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemLink
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemLink
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {string}
     * @memberof SectionItemLink
     */
    description?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemLink
     */
    text?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemLink
     */
    appTag?: string | null;
}



/**
 * Check if a given object implements the SectionItemLink interface.
 */
export function instanceOfSectionItemLink(value: object): value is SectionItemLink {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemLinkFromJSON(json: any): SectionItemLink {
    return SectionItemLinkFromJSONTyped(json, false);
}

export function SectionItemLinkFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemLink {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'description': json['description'] == null ? undefined : json['description'],
        'text': json['text'] == null ? undefined : json['text'],
        'appTag': json['appTag'] == null ? undefined : json['appTag'],
    };
}

export function SectionItemLinkToJSON(value?: SectionItemLink | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'description': value['description'],
        'text': value['text'],
        'appTag': value['appTag'],
    };
}

