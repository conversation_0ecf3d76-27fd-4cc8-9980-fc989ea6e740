/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AdditionalRecapData } from './AdditionalRecapData';
import {
    AdditionalRecapDataFromJSON,
    AdditionalRecapDataFromJSONTyped,
    AdditionalRecapDataToJSON,
} from './AdditionalRecapData';

/**
 * 
 * @export
 * @interface BodyClientGenerateClientRecap
 */
export interface BodyClientGenerateClientRecap {
    /**
     * 
     * @type {string}
     * @memberof BodyClientGenerateClientRecap
     */
    callbackUrl?: string | null;
    /**
     * 
     * @type {Array<AdditionalRecapData>}
     * @memberof BodyClientGenerateClientRecap
     */
    additionalData?: Array<AdditionalRecapData> | null;
}

/**
 * Check if a given object implements the BodyClientGenerateClientRecap interface.
 */
export function instanceOfBodyClientGenerateClientRecap(value: object): value is BodyClientGenerateClientRecap {
    return true;
}

export function BodyClientGenerateClientRecapFromJSON(json: any): BodyClientGenerateClientRecap {
    return BodyClientGenerateClientRecapFromJSONTyped(json, false);
}

export function BodyClientGenerateClientRecapFromJSONTyped(json: any, ignoreDiscriminator: boolean): BodyClientGenerateClientRecap {
    if (json == null) {
        return json;
    }
    return {
        
        'callbackUrl': json['callback_url'] == null ? undefined : json['callback_url'],
        'additionalData': json['additional_data'] == null ? undefined : ((json['additional_data'] as Array<any>).map(AdditionalRecapDataFromJSON)),
    };
}

export function BodyClientGenerateClientRecapToJSON(value?: BodyClientGenerateClientRecap | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'callback_url': value['callbackUrl'],
        'additional_data': value['additionalData'] == null ? undefined : ((value['additionalData'] as Array<any>).map(AdditionalRecapDataToJSON)),
    };
}

