/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const NoteAudioSource = {
    Mic: 'mic',
    Phone: 'phone',
    VideoCall: 'video_call'
} as const;
export type NoteAudioSource = typeof NoteAudioSource[keyof typeof NoteAudioSource];


export function instanceOfNoteAudioSource(value: any): boolean {
    for (const key in NoteAudioSource) {
        if (Object.prototype.hasOwnProperty.call(NoteAudioSource, key)) {
            if (NoteAudioSource[key as keyof typeof NoteAudioSource] === value) {
                return true;
            }
        }
    }
    return false;
}

export function NoteAudioSourceFromJSON(json: any): NoteAudioSource {
    return NoteAudioSourceFromJSONTyped(json, false);
}

export function NoteAudioSourceFromJSONTyped(json: any, ignoreDiscriminator: boolean): NoteAudioSource {
    return json as NoteAudioSource;
}

export function NoteAudioSourceToJSON(value?: NoteAudioSource | null): any {
    return value as any;
}

