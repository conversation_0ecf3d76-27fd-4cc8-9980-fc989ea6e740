/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';
import type { LabeledEntity } from './LabeledEntity';
import {
    LabeledEntityFromJSON,
    LabeledEntityFromJSONTyped,
    LabeledEntityToJSON,
} from './LabeledEntity';
import type { SectionItemIntegrationCard } from './SectionItemIntegrationCard';
import {
    SectionItemIntegrationCardFromJSON,
    SectionItemIntegrationCardFromJSONTyped,
    SectionItemIntegrationCardToJSON,
} from './SectionItemIntegrationCard';

/**
 * 
 * @export
 * @interface SectionItemIntegrationCards
 */
export interface SectionItemIntegrationCards {
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCards
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCards
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemIntegrationCards
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {Array<LabeledEntity>}
     * @memberof SectionItemIntegrationCards
     */
    filters?: Array<LabeledEntity> | null;
    /**
     * 
     * @type {Array<SectionItemIntegrationCard>}
     * @memberof SectionItemIntegrationCards
     */
    cards?: Array<SectionItemIntegrationCard> | null;
}



/**
 * Check if a given object implements the SectionItemIntegrationCards interface.
 */
export function instanceOfSectionItemIntegrationCards(value: object): value is SectionItemIntegrationCards {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemIntegrationCardsFromJSON(json: any): SectionItemIntegrationCards {
    return SectionItemIntegrationCardsFromJSONTyped(json, false);
}

export function SectionItemIntegrationCardsFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemIntegrationCards {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'filters': json['filters'] == null ? undefined : ((json['filters'] as Array<any>).map(LabeledEntityFromJSON)),
        'cards': json['cards'] == null ? undefined : ((json['cards'] as Array<any>).map(SectionItemIntegrationCardFromJSON)),
    };
}

export function SectionItemIntegrationCardsToJSON(value?: SectionItemIntegrationCards | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'filters': value['filters'] == null ? undefined : ((value['filters'] as Array<any>).map(LabeledEntityToJSON)),
        'cards': value['cards'] == null ? undefined : ((value['cards'] as Array<any>).map(SectionItemIntegrationCardToJSON)),
    };
}

