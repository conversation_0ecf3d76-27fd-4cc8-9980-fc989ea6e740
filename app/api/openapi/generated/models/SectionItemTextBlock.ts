/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';

/**
 * 
 * @export
 * @interface SectionItemTextBlock
 */
export interface SectionItemTextBlock {
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextBlock
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextBlock
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemTextBlock
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {string}
     * @memberof SectionItemTextBlock
     */
    details?: string | null;
}



/**
 * Check if a given object implements the SectionItemTextBlock interface.
 */
export function instanceOfSectionItemTextBlock(value: object): value is SectionItemTextBlock {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemTextBlockFromJSON(json: any): SectionItemTextBlock {
    return SectionItemTextBlockFromJSONTyped(json, false);
}

export function SectionItemTextBlockFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemTextBlock {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'details': json['details'] == null ? undefined : json['details'],
    };
}

export function SectionItemTextBlockToJSON(value?: SectionItemTextBlock | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'details': value['details'],
    };
}

