/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const BotMeetingType = {
    PhoneCall: 'phone_call',
    VideoCall: 'video_call'
} as const;
export type BotMeetingType = typeof BotMeetingType[keyof typeof BotMeetingType];


export function instanceOfBotMeetingType(value: any): boolean {
    for (const key in BotMeetingType) {
        if (Object.prototype.hasOwnProperty.call(BotMeetingType, key)) {
            if (BotMeetingType[key as keyof typeof BotMeetingType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function BotMeetingTypeFromJSON(json: any): BotMeetingType {
    return BotMeetingTypeFromJSONTyped(json, false);
}

export function BotMeetingTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): BotMeetingType {
    return json as BotMeetingType;
}

export function BotMeetingTypeToJSON(value?: BotMeetingType | null): any {
    return value as any;
}

