/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ApiRoutersCrmClientResponse } from './ApiRoutersCrmClientResponse';
import {
    ApiRoutersCrmClientResponseFromJSON,
    ApiRoutersCrmClientResponseFromJSONTyped,
    ApiRoutersCrmClientResponseToJSON,
} from './ApiRoutersCrmClientResponse';

/**
 * 
 * @export
 * @interface ClientListResponse
 */
export interface ClientListResponse {
    /**
     * 
     * @type {boolean}
     * @memberof ClientListResponse
     */
    clientSelectionEnabled: boolean;
    /**
     * 
     * @type {Array<ApiRoutersCrmClientResponse>}
     * @memberof ClientListResponse
     */
    clients: Array<ApiRoutersCrmClientResponse>;
    /**
     * 
     * @type {string}
     * @memberof ClientListResponse
     */
    crmSystem: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientListResponse
     */
    nextPageToken?: string | null;
}

/**
 * Check if a given object implements the ClientListResponse interface.
 */
export function instanceOfClientListResponse(value: object): value is ClientListResponse {
    if (!('clientSelectionEnabled' in value) || value['clientSelectionEnabled'] === undefined) return false;
    if (!('clients' in value) || value['clients'] === undefined) return false;
    if (!('crmSystem' in value) || value['crmSystem'] === undefined) return false;
    return true;
}

export function ClientListResponseFromJSON(json: any): ClientListResponse {
    return ClientListResponseFromJSONTyped(json, false);
}

export function ClientListResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientListResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'clientSelectionEnabled': json['client_selection_enabled'],
        'clients': ((json['clients'] as Array<any>).map(ApiRoutersCrmClientResponseFromJSON)),
        'crmSystem': json['crm_system'],
        'nextPageToken': json['next_page_token'] == null ? undefined : json['next_page_token'],
    };
}

export function ClientListResponseToJSON(value?: ClientListResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'client_selection_enabled': value['clientSelectionEnabled'],
        'clients': ((value['clients'] as Array<any>).map(ApiRoutersCrmClientResponseToJSON)),
        'crm_system': value['crmSystem'],
        'next_page_token': value['nextPageToken'],
    };
}

