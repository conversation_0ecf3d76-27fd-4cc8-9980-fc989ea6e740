/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { MeetingCategory } from './MeetingCategory';
import {
    MeetingCategoryFromJSON,
    MeetingCategoryFromJSONTyped,
    MeetingCategoryToJSON,
} from './MeetingCategory';

/**
 * 
 * @export
 * @interface MeetingType
 */
export interface MeetingType {
    /**
     * 
     * @type {string}
     * @memberof MeetingType
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof MeetingType
     */
    name: string;
    /**
     * 
     * @type {MeetingCategory}
     * @memberof MeetingType
     */
    category: MeetingCategory;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingType
     */
    isShared: boolean;
}



/**
 * Check if a given object implements the MeetingType interface.
 */
export function instanceOfMeetingType(value: object): value is MeetingType {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('category' in value) || value['category'] === undefined) return false;
    if (!('isShared' in value) || value['isShared'] === undefined) return false;
    return true;
}

export function MeetingTypeFromJSON(json: any): MeetingType {
    return MeetingTypeFromJSONTyped(json, false);
}

export function MeetingTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): MeetingType {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'category': MeetingCategoryFromJSON(json['category']),
        'isShared': json['is_shared'],
    };
}

export function MeetingTypeToJSON(value?: MeetingType | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'category': MeetingCategoryToJSON(value['category']),
        'is_shared': value['isShared'],
    };
}

