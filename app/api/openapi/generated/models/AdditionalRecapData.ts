/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface AdditionalRecapData
 */
export interface AdditionalRecapData {
    /**
     * The source of the additional recap data, e.g., CRM system name.
     * @type {string}
     * @memberof AdditionalRecapData
     */
    source: string;
    /**
     * The actual content.
     * @type {string}
     * @memberof AdditionalRecapData
     */
    content: string;
    /**
     * 
     * @type {Date}
     * @memberof AdditionalRecapData
     */
    creationDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof AdditionalRecapData
     */
    link?: string | null;
}

/**
 * Check if a given object implements the AdditionalRecapData interface.
 */
export function instanceOfAdditionalRecapData(value: object): value is AdditionalRecapData {
    if (!('source' in value) || value['source'] === undefined) return false;
    if (!('content' in value) || value['content'] === undefined) return false;
    return true;
}

export function AdditionalRecapDataFromJSON(json: any): AdditionalRecapData {
    return AdditionalRecapDataFromJSONTyped(json, false);
}

export function AdditionalRecapDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): AdditionalRecapData {
    if (json == null) {
        return json;
    }
    return {
        
        'source': json['source'],
        'content': json['content'],
        'creationDate': json['creation_date'] == null ? undefined : (new Date(json['creation_date'])),
        'link': json['link'] == null ? undefined : json['link'],
    };
}

export function AdditionalRecapDataToJSON(value?: AdditionalRecapData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'source': value['source'],
        'content': value['content'],
        'creation_date': value['creationDate'] == null ? undefined : ((value['creationDate'] as any).toISOString()),
        'link': value['link'],
    };
}

