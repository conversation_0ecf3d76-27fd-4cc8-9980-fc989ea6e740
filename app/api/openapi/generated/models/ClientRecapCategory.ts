/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientRecapBullet } from './ClientRecapBullet';
import {
    ClientRecapBulletFromJSON,
    ClientRecapBulletFromJSONTyped,
    ClientRecapBulletToJSON,
} from './ClientRecapBullet';

/**
 * 
 * @export
 * @interface ClientRecapCategory
 */
export interface ClientRecapCategory {
    /**
     * 
     * @type {string}
     * @memberof ClientRecapCategory
     */
    topic: string;
    /**
     * 
     * @type {Array<ClientRecapBullet>}
     * @memberof ClientRecapCategory
     */
    bullets: Array<ClientRecapBullet>;
}

/**
 * Check if a given object implements the ClientRecapCategory interface.
 */
export function instanceOfClientRecapCategory(value: object): value is ClientRecapCategory {
    if (!('topic' in value) || value['topic'] === undefined) return false;
    if (!('bullets' in value) || value['bullets'] === undefined) return false;
    return true;
}

export function ClientRecapCategoryFromJSON(json: any): ClientRecapCategory {
    return ClientRecapCategoryFromJSONTyped(json, false);
}

export function ClientRecapCategoryFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapCategory {
    if (json == null) {
        return json;
    }
    return {
        
        'topic': json['topic'],
        'bullets': ((json['bullets'] as Array<any>).map(ClientRecapBulletFromJSON)),
    };
}

export function ClientRecapCategoryToJSON(value?: ClientRecapCategory | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'topic': value['topic'],
        'bullets': ((value['bullets'] as Array<any>).map(ClientRecapBulletToJSON)),
    };
}

