/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ActionItem
 */
export interface ActionItem {
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    content: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    status: string;
}

/**
 * Check if a given object implements the ActionItem interface.
 */
export function instanceOfActionItem(value: object): value is ActionItem {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('content' in value) || value['content'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function ActionItemFromJSON(json: any): ActionItem {
    return ActionItemFromJSONTyped(json, false);
}

export function ActionItemFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionItem {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'content': json['content'],
        'status': json['status'],
    };
}

export function ActionItemToJSON(value?: ActionItem | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'content': value['content'],
        'status': value['status'],
    };
}

