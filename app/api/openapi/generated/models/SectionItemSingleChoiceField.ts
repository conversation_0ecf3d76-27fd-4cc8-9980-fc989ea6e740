/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';
import type { LabeledEntity } from './LabeledEntity';
import {
    LabeledEntityFromJSON,
    LabeledEntityFromJSONTyped,
    LabeledEntityToJSON,
} from './LabeledEntity';

/**
 * 
 * @export
 * @interface SectionItemSingleChoiceField
 */
export interface SectionItemSingleChoiceField {
    /**
     * 
     * @type {string}
     * @memberof SectionItemSingleChoiceField
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemSingleChoiceField
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemSingleChoiceField
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {Array<LabeledEntity>}
     * @memberof SectionItemSingleChoiceField
     */
    options?: Array<LabeledEntity> | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemSingleChoiceField
     */
    value?: string | null;
}



/**
 * Check if a given object implements the SectionItemSingleChoiceField interface.
 */
export function instanceOfSectionItemSingleChoiceField(value: object): value is SectionItemSingleChoiceField {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemSingleChoiceFieldFromJSON(json: any): SectionItemSingleChoiceField {
    return SectionItemSingleChoiceFieldFromJSONTyped(json, false);
}

export function SectionItemSingleChoiceFieldFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemSingleChoiceField {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'options': json['options'] == null ? undefined : ((json['options'] as Array<any>).map(LabeledEntityFromJSON)),
        'value': json['value'] == null ? undefined : json['value'],
    };
}

export function SectionItemSingleChoiceFieldToJSON(value?: SectionItemSingleChoiceField | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'options': value['options'] == null ? undefined : ((value['options'] as Array<any>).map(LabeledEntityToJSON)),
        'value': value['value'],
    };
}

