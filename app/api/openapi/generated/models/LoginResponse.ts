/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { UserDetails } from './UserDetails';
import {
    UserDetailsFromJSON,
    UserDetailsFromJSONTyped,
    UserDetailsToJSON,
} from './UserDetails';

/**
 * 
 * @export
 * @interface LoginResponse
 */
export interface LoginResponse {
    /**
     * 
     * @type {UserDetails}
     * @memberof LoginResponse
     */
    userProfile: UserDetails;
    /**
     * 
     * @type {string}
     * @memberof LoginResponse
     */
    refreshToken: string;
    /**
     * 
     * @type {string}
     * @memberof LoginResponse
     */
    accessToken: string;
}

/**
 * Check if a given object implements the LoginResponse interface.
 */
export function instanceOfLoginResponse(value: object): value is LoginResponse {
    if (!('userProfile' in value) || value['userProfile'] === undefined) return false;
    if (!('refreshToken' in value) || value['refreshToken'] === undefined) return false;
    if (!('accessToken' in value) || value['accessToken'] === undefined) return false;
    return true;
}

export function LoginResponseFromJSON(json: any): LoginResponse {
    return LoginResponseFromJSONTyped(json, false);
}

export function LoginResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): LoginResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'userProfile': UserDetailsFromJSON(json['user_profile']),
        'refreshToken': json['refresh_token'],
        'accessToken': json['access_token'],
    };
}

export function LoginResponseToJSON(value?: LoginResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'user_profile': UserDetailsToJSON(value['userProfile']),
        'refresh_token': value['refreshToken'],
        'access_token': value['accessToken'],
    };
}

