/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const UserLicenseType = {
    Advisor: 'advisor',
    Csa: 'csa',
    Staff: 'staff'
} as const;
export type UserLicenseType = typeof UserLicenseType[keyof typeof UserLicenseType];


export function instanceOfUserLicenseType(value: any): boolean {
    for (const key in UserLicenseType) {
        if (Object.prototype.hasOwnProperty.call(UserLicenseType, key)) {
            if (UserLicenseType[key as keyof typeof UserLicenseType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function UserLicenseTypeFromJSON(json: any): UserLicenseType {
    return UserLicenseTypeFromJSONTyped(json, false);
}

export function UserLicenseTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserLicenseType {
    return json as UserLicenseType;
}

export function UserLicenseTypeToJSON(value?: UserLicenseType | null): any {
    return value as any;
}

