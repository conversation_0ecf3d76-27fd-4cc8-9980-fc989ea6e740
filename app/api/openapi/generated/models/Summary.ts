/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SummarySection } from './SummarySection';
import {
    SummarySectionFromJSON,
    SummarySectionFromJSONTyped,
    SummarySectionToJSON,
} from './SummarySection';

/**
 * 
 * @export
 * @interface Summary
 */
export interface Summary {
    /**
     * 
     * @type {Array<SummarySection>}
     * @memberof Summary
     */
    sections?: Array<SummarySection>;
}

/**
 * Check if a given object implements the Summary interface.
 */
export function instanceOfSummary(value: object): value is Summary {
    return true;
}

export function SummaryFromJSON(json: any): Summary {
    return SummaryFromJSONTyped(json, false);
}

export function SummaryFromJSONTyped(json: any, ignoreDiscriminator: boolean): Summary {
    if (json == null) {
        return json;
    }
    return {
        
        'sections': json['sections'] == null ? undefined : ((json['sections'] as Array<any>).map(SummarySectionFromJSON)),
    };
}

export function SummaryToJSON(value?: Summary | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'sections': value['sections'] == null ? undefined : ((value['sections'] as Array<any>).map(SummarySectionToJSON)),
    };
}

