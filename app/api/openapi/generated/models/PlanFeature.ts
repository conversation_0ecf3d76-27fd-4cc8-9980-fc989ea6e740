/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PlanFeature
 */
export interface PlanFeature {
    /**
     * 
     * @type {string}
     * @memberof PlanFeature
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof PlanFeature
     */
    label: string;
    /**
     * 
     * @type {number}
     * @memberof PlanFeature
     */
    licenseCount: number;
    /**
     * 
     * @type {Array<string>}
     * @memberof PlanFeature
     */
    userUuids: Array<string>;
}

/**
 * Check if a given object implements the PlanFeature interface.
 */
export function instanceOfPlanFeature(value: object): value is PlanFeature {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    if (!('licenseCount' in value) || value['licenseCount'] === undefined) return false;
    if (!('userUuids' in value) || value['userUuids'] === undefined) return false;
    return true;
}

export function PlanFeatureFromJSON(json: any): PlanFeature {
    return PlanFeatureFromJSONTyped(json, false);
}

export function PlanFeatureFromJSONTyped(json: any, ignoreDiscriminator: boolean): PlanFeature {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'licenseCount': json['license_count'],
        'userUuids': json['user_uuids'],
    };
}

export function PlanFeatureToJSON(value?: PlanFeature | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'license_count': value['licenseCount'],
        'user_uuids': value['userUuids'],
    };
}

