/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const SectionItemFieldType = {
    TextBlock: 'text-block',
    TextField: 'text-field',
    SingleChoiceField: 'single-choice-field',
    MultiChoiceField: 'multi-choice-field',
    BooleanField: 'boolean-field',
    AcknowledgementField: 'acknowledgement-field',
    IntegrationCards: 'integration-cards',
    IntegrationCard: 'integration-card',
    Link: 'link',
    PlanDetails: 'plan-details'
} as const;
export type SectionItemFieldType = typeof SectionItemFieldType[keyof typeof SectionItemFieldType];


export function instanceOfSectionItemFieldType(value: any): boolean {
    for (const key in SectionItemFieldType) {
        if (Object.prototype.hasOwnProperty.call(SectionItemFieldType, key)) {
            if (SectionItemFieldType[key as keyof typeof SectionItemFieldType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function SectionItemFieldTypeFromJSON(json: any): SectionItemFieldType {
    return SectionItemFieldTypeFromJSONTyped(json, false);
}

export function SectionItemFieldTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemFieldType {
    return json as SectionItemFieldType;
}

export function SectionItemFieldTypeToJSON(value?: SectionItemFieldType | null): any {
    return value as any;
}

