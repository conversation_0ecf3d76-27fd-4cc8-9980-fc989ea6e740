/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Main sections that can be synced to CRM
 * @export
 */
export const CRMSyncSection = {
    MeetingDetails: 'meeting_details',
    Attendees: 'attendees',
    Tasks: 'tasks',
    KeyTakeaways: 'key_takeaways',
    AdvisorNotes: 'advisor_notes',
    Summary: 'summary',
    Keywords: 'keywords'
} as const;
export type CRMSyncSection = typeof CRMSyncSection[keyof typeof CRMSyncSection];


export function instanceOfCRMSyncSection(value: any): boolean {
    for (const key in CRMSyncSection) {
        if (Object.prototype.hasOwnProperty.call(CRMSyncSection, key)) {
            if (CRMSyncSection[key as keyof typeof CRMSyncSection] === value) {
                return true;
            }
        }
    }
    return false;
}

export function CRMSyncSectionFromJSON(json: any): CRMSyncSection {
    return CRMSyncSectionFromJSONTyped(json, false);
}

export function CRMSyncSectionFromJSONTyped(json: any, ignoreDiscriminator: boolean): CRMSyncSection {
    return json as CRMSyncSection;
}

export function CRMSyncSectionToJSON(value?: CRMSyncSection | null): any {
    return value as any;
}

