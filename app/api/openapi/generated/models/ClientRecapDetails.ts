/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientRecapStatus } from './ClientRecapStatus';
import {
    ClientRecapStatusFromJSON,
    ClientRecapStatusFromJSONTyped,
    ClientRecapStatusToJSON,
} from './ClientRecapStatus';
import type { ClientRecapInput } from './ClientRecapInput';
import {
    ClientRecapInputFromJSON,
    ClientRecapInputFromJSONTyped,
    ClientRecapInputToJSON,
} from './ClientRecapInput';

/**
 * 
 * @export
 * @interface ClientRecapDetails
 */
export interface ClientRecapDetails {
    /**
     * The status of the client recap generation process.
     * @type {ClientRecapStatus}
     * @memberof ClientRecapDetails
     */
    status: ClientRecapStatus;
    /**
     * 
     * @type {ClientRecapInput}
     * @memberof ClientRecapDetails
     */
    recap: ClientRecapInput | null;
}



/**
 * Check if a given object implements the ClientRecapDetails interface.
 */
export function instanceOfClientRecapDetails(value: object): value is ClientRecapDetails {
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('recap' in value) || value['recap'] === undefined) return false;
    return true;
}

export function ClientRecapDetailsFromJSON(json: any): ClientRecapDetails {
    return ClientRecapDetailsFromJSONTyped(json, false);
}

export function ClientRecapDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'status': ClientRecapStatusFromJSON(json['status']),
        'recap': ClientRecapInputFromJSON(json['recap']),
    };
}

export function ClientRecapDetailsToJSON(value?: ClientRecapDetails | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'status': ClientRecapStatusToJSON(value['status']),
        'recap': ClientRecapInputToJSON(value['recap']),
    };
}

