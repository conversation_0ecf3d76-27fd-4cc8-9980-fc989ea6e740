/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface Utterance
 */
export interface Utterance {
    /**
     * 
     * @type {string}
     * @memberof Utterance
     */
    speaker: string;
    /**
     * 
     * @type {string}
     * @memberof Utterance
     */
    start: string;
    /**
     * 
     * @type {string}
     * @memberof Utterance
     */
    end: string;
    /**
     * 
     * @type {string}
     * @memberof Utterance
     */
    text: string;
}

/**
 * Check if a given object implements the Utterance interface.
 */
export function instanceOfUtterance(value: object): value is Utterance {
    if (!('speaker' in value) || value['speaker'] === undefined) return false;
    if (!('start' in value) || value['start'] === undefined) return false;
    if (!('end' in value) || value['end'] === undefined) return false;
    if (!('text' in value) || value['text'] === undefined) return false;
    return true;
}

export function UtteranceFromJSON(json: any): Utterance {
    return UtteranceFromJSONTyped(json, false);
}

export function UtteranceFromJSONTyped(json: any, ignoreDiscriminator: boolean): Utterance {
    if (json == null) {
        return json;
    }
    return {
        
        'speaker': json['speaker'],
        'start': json['start'],
        'end': json['end'],
        'text': json['text'],
    };
}

export function UtteranceToJSON(value?: Utterance | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'speaker': value['speaker'],
        'start': value['start'],
        'end': value['end'],
        'text': value['text'],
    };
}

