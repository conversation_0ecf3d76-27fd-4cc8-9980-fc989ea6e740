/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ApiRoutersAttendeeClientResponse
 */
export interface ApiRoutersAttendeeClientResponse {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeClientResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeClientResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeClientResponse
     */
    crmId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeClientResponse
     */
    jobTitle?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeClientResponse
     */
    clientType?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeClientResponse
     */
    phoneNumber?: string;
}

/**
 * Check if a given object implements the ApiRoutersAttendeeClientResponse interface.
 */
export function instanceOfApiRoutersAttendeeClientResponse(value: object): value is ApiRoutersAttendeeClientResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function ApiRoutersAttendeeClientResponseFromJSON(json: any): ApiRoutersAttendeeClientResponse {
    return ApiRoutersAttendeeClientResponseFromJSONTyped(json, false);
}

export function ApiRoutersAttendeeClientResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersAttendeeClientResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'crmId': json['crm_id'] == null ? undefined : json['crm_id'],
        'jobTitle': json['job_title'] == null ? undefined : json['job_title'],
        'clientType': json['client_type'] == null ? undefined : json['client_type'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
    };
}

export function ApiRoutersAttendeeClientResponseToJSON(value?: ApiRoutersAttendeeClientResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'crm_id': value['crmId'],
        'job_title': value['jobTitle'],
        'client_type': value['clientType'],
        'phone_number': value['phoneNumber'],
    };
}

