/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { MeetingType } from './MeetingType';
import {
    MeetingTypeFromJSON,
    MeetingTypeFromJSONTyped,
    MeetingTypeToJSON,
} from './MeetingType';

/**
 * 
 * @export
 * @interface MeetingTypesResponse
 */
export interface MeetingTypesResponse {
    /**
     * 
     * @type {Array<MeetingType>}
     * @memberof MeetingTypesResponse
     */
    meetingTypes: Array<MeetingType>;
    /**
     * 
     * @type {string}
     * @memberof MeetingTypesResponse
     */
    defaultMeetingType: string | null;
}

/**
 * Check if a given object implements the MeetingTypesResponse interface.
 */
export function instanceOfMeetingTypesResponse(value: object): value is MeetingTypesResponse {
    if (!('meetingTypes' in value) || value['meetingTypes'] === undefined) return false;
    if (!('defaultMeetingType' in value) || value['defaultMeetingType'] === undefined) return false;
    return true;
}

export function MeetingTypesResponseFromJSON(json: any): MeetingTypesResponse {
    return MeetingTypesResponseFromJSONTyped(json, false);
}

export function MeetingTypesResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): MeetingTypesResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'meetingTypes': ((json['meeting_types'] as Array<any>).map(MeetingTypeFromJSON)),
        'defaultMeetingType': json['default_meeting_type'],
    };
}

export function MeetingTypesResponseToJSON(value?: MeetingTypesResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'meeting_types': ((value['meetingTypes'] as Array<any>).map(MeetingTypeToJSON)),
        'default_meeting_type': value['defaultMeetingType'],
    };
}

