/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { UISchemaControl } from './UISchemaControl';
import {
    UISchemaControlFromJSON,
    UISchemaControlFromJSONTyped,
    UISchemaControlToJSON,
} from './UISchemaControl';

/**
 * 
 * @export
 * @interface UISchema
 */
export interface UISchema {
    /**
     * Type of the UI schema
     * @type {string}
     * @memberof UISchema
     */
    type: string;
    /**
     * Elements of the UI schema
     * @type {Array<UISchemaControl>}
     * @memberof UISchema
     */
    elements: Array<UISchemaControl>;
}

/**
 * Check if a given object implements the UISchema interface.
 */
export function instanceOfUISchema(value: object): value is UISchema {
    if (!('type' in value) || value['type'] === undefined) return false;
    if (!('elements' in value) || value['elements'] === undefined) return false;
    return true;
}

export function UISchemaFromJSON(json: any): UISchema {
    return UISchemaFromJSONTyped(json, false);
}

export function UISchemaFromJSONTyped(json: any, ignoreDiscriminator: boolean): UISchema {
    if (json == null) {
        return json;
    }
    return {
        
        'type': json['type'],
        'elements': ((json['elements'] as Array<any>).map(UISchemaControlFromJSON)),
    };
}

export function UISchemaToJSON(value?: UISchema | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'type': value['type'],
        'elements': ((value['elements'] as Array<any>).map(UISchemaControlToJSON)),
    };
}

