/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ApiRoutersTaskModelsClient
 */
export interface ApiRoutersTaskModelsClient {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersTaskModelsClient
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersTaskModelsClient
     */
    name: string;
}

/**
 * Check if a given object implements the ApiRoutersTaskModelsClient interface.
 */
export function instanceOfApiRoutersTaskModelsClient(value: object): value is ApiRoutersTaskModelsClient {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function ApiRoutersTaskModelsClientFromJSON(json: any): ApiRoutersTaskModelsClient {
    return ApiRoutersTaskModelsClientFromJSONTyped(json, false);
}

export function ApiRoutersTaskModelsClientFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersTaskModelsClient {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
    };
}

export function ApiRoutersTaskModelsClientToJSON(value?: ApiRoutersTaskModelsClient | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
    };
}

