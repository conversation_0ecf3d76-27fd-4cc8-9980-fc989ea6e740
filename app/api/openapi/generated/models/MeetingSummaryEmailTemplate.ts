/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface MeetingSummaryEmailTemplate
 */
export interface MeetingSummaryEmailTemplate {
    /**
     * 
     * @type {string}
     * @memberof MeetingSummaryEmailTemplate
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof MeetingSummaryEmailTemplate
     */
    name: string;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingSummaryEmailTemplate
     */
    useHtml: boolean;
}

/**
 * Check if a given object implements the MeetingSummaryEmailTemplate interface.
 */
export function instanceOfMeetingSummaryEmailTemplate(value: object): value is MeetingSummaryEmailTemplate {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('useHtml' in value) || value['useHtml'] === undefined) return false;
    return true;
}

export function MeetingSummaryEmailTemplateFromJSON(json: any): MeetingSummaryEmailTemplate {
    return MeetingSummaryEmailTemplateFromJSONTyped(json, false);
}

export function MeetingSummaryEmailTemplateFromJSONTyped(json: any, ignoreDiscriminator: boolean): MeetingSummaryEmailTemplate {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'useHtml': json['use_html'],
    };
}

export function MeetingSummaryEmailTemplateToJSON(value?: MeetingSummaryEmailTemplate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'use_html': value['useHtml'],
    };
}

