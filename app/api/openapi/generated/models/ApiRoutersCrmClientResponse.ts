/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ApiRoutersCrmClientResponse
 */
export interface ApiRoutersCrmClientResponse {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersCrmClientResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersCrmClientResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersCrmClientResponse
     */
    type: string;
}

/**
 * Check if a given object implements the ApiRoutersCrmClientResponse interface.
 */
export function instanceOfApiRoutersCrmClientResponse(value: object): value is ApiRoutersCrmClientResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('type' in value) || value['type'] === undefined) return false;
    return true;
}

export function ApiRoutersCrmClientResponseFromJSON(json: any): ApiRoutersCrmClientResponse {
    return ApiRoutersCrmClientResponseFromJSONTyped(json, false);
}

export function ApiRoutersCrmClientResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersCrmClientResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'type': json['type'],
    };
}

export function ApiRoutersCrmClientResponseToJSON(value?: ApiRoutersCrmClientResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'type': value['type'],
    };
}

