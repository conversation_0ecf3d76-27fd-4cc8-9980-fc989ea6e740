/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const Category = {
    Client: 'client',
    Internal: 'internal',
    Debrief: 'debrief'
} as const;
export type Category = typeof Category[keyof typeof Category];


export function instanceOfCategory(value: any): boolean {
    for (const key in Category) {
        if (Object.prototype.hasOwnProperty.call(Category, key)) {
            if (Category[key as keyof typeof Category] === value) {
                return true;
            }
        }
    }
    return false;
}

export function CategoryFromJSON(json: any): Category {
    return CategoryFromJSONTyped(json, false);
}

export function CategoryFromJSONTyped(json: any, ignoreDiscriminator: boolean): Category {
    return json as Category;
}

export function CategoryToJSON(value?: Category | null): any {
    return value as any;
}

