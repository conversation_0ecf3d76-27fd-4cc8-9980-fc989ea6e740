/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface BodyCalendarUpdateAutojoin
 */
export interface BodyCalendarUpdateAutojoin {
    /**
     * The UUID of the scheduled event to update.
     * @type {string}
     * @memberof BodyCalendarUpdateAutojoin
     */
    scheduledEventUuid: string;
    /**
     * Whether to enable or disable auto join.
     * @type {boolean}
     * @memberof BodyCalendarUpdateAutojoin
     */
    autoJoin: boolean;
}

/**
 * Check if a given object implements the BodyCalendarUpdateAutojoin interface.
 */
export function instanceOfBodyCalendarUpdateAutojoin(value: object): value is BodyCalendarUpdateAutojoin {
    if (!('scheduledEventUuid' in value) || value['scheduledEventUuid'] === undefined) return false;
    if (!('autoJoin' in value) || value['autoJoin'] === undefined) return false;
    return true;
}

export function BodyCalendarUpdateAutojoinFromJSON(json: any): BodyCalendarUpdateAutojoin {
    return BodyCalendarUpdateAutojoinFromJSONTyped(json, false);
}

export function BodyCalendarUpdateAutojoinFromJSONTyped(json: any, ignoreDiscriminator: boolean): BodyCalendarUpdateAutojoin {
    if (json == null) {
        return json;
    }
    return {
        
        'scheduledEventUuid': json['scheduled_event_uuid'],
        'autoJoin': json['auto_join'],
    };
}

export function BodyCalendarUpdateAutojoinToJSON(value?: BodyCalendarUpdateAutojoin | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'scheduled_event_uuid': value['scheduledEventUuid'],
        'auto_join': value['autoJoin'],
    };
}

