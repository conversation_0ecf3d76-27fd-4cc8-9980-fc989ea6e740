/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionDetailsDataInner } from './SectionDetailsDataInner';
import {
    SectionDetailsDataInnerFromJSON,
    SectionDetailsDataInnerFromJSONTyped,
    SectionDetailsDataInnerToJSON,
} from './SectionDetailsDataInner';

/**
 * 
 * @export
 * @interface SectionDetails
 */
export interface SectionDetails {
    /**
     * 
     * @type {string}
     * @memberof SectionDetails
     */
    label: string;
    /**
     * 
     * @type {Array<SectionDetailsDataInner>}
     * @memberof SectionDetails
     */
    data: Array<SectionDetailsDataInner>;
    /**
     * 
     * @type {boolean}
     * @memberof SectionDetails
     */
    showSaveButton?: boolean;
}

/**
 * Check if a given object implements the SectionDetails interface.
 */
export function instanceOfSectionDetails(value: object): value is SectionDetails {
    if (!('label' in value) || value['label'] === undefined) return false;
    if (!('data' in value) || value['data'] === undefined) return false;
    return true;
}

export function SectionDetailsFromJSON(json: any): SectionDetails {
    return SectionDetailsFromJSONTyped(json, false);
}

export function SectionDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'label': json['label'],
        'data': ((json['data'] as Array<any>).map(SectionDetailsDataInnerFromJSON)),
        'showSaveButton': json['showSaveButton'] == null ? undefined : json['showSaveButton'],
    };
}

export function SectionDetailsToJSON(value?: SectionDetails | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'label': value['label'],
        'data': ((value['data'] as Array<any>).map(SectionDetailsDataInnerToJSON)),
        'showSaveButton': value['showSaveButton'],
    };
}

