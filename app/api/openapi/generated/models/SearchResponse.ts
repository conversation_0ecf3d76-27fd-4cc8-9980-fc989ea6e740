/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SummarySection } from './SummarySection';
import {
    SummarySectionFromJSON,
    SummarySectionFromJSONTyped,
    SummarySectionToJSON,
} from './SummarySection';

/**
 * The response model for both note and client search operations
 * @export
 * @interface SearchResponse
 */
export interface SearchResponse {
    /**
     * 
     * @type {SummarySection}
     * @memberof SearchResponse
     */
    answer: SummarySection;
    /**
     * 
     * @type {string}
     * @memberof SearchResponse
     */
    searchQueryId: string;
}

/**
 * Check if a given object implements the SearchResponse interface.
 */
export function instanceOfSearchResponse(value: object): value is SearchResponse {
    if (!('answer' in value) || value['answer'] === undefined) return false;
    if (!('searchQueryId' in value) || value['searchQueryId'] === undefined) return false;
    return true;
}

export function SearchResponseFromJSON(json: any): SearchResponse {
    return SearchResponseFromJSONTyped(json, false);
}

export function SearchResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'answer': SummarySectionFromJSON(json['answer']),
        'searchQueryId': json['search_query_id'],
    };
}

export function SearchResponseToJSON(value?: SearchResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'answer': SummarySectionToJSON(value['answer']),
        'search_query_id': value['searchQueryId'],
    };
}

