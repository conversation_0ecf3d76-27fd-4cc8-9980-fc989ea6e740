/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CRMSyncItemSelection } from './CRMSyncItemSelection';
import {
    CRMSyncItemSelectionFromJSON,
    CRMSyncItemSelectionFromJSONTyped,
    CRMSyncItemSelectionToJSON,
} from './CRMSyncItemSelection';

/**
 * 
 * @export
 * @interface UploadNoteToCRMRequest
 */
export interface UploadNoteToCRMRequest {
    /**
     * 
     * @type {{ [key: string]: CRMSyncItemSelection; }}
     * @memberof UploadNoteToCRMRequest
     */
    syncItems?: { [key: string]: CRMSyncItemSelection; } | null;
}

/**
 * Check if a given object implements the UploadNoteToCRMRequest interface.
 */
export function instanceOfUploadNoteToCRMRequest(value: object): value is UploadNoteToCRMRequest {
    return true;
}

export function UploadNoteToCRMRequestFromJSON(json: any): UploadNoteToCRMRequest {
    return UploadNoteToCRMRequestFromJSONTyped(json, false);
}

export function UploadNoteToCRMRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): UploadNoteToCRMRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'syncItems': json['sync_items'] == null ? undefined : (mapValues(json['sync_items'], CRMSyncItemSelectionFromJSON)),
    };
}

export function UploadNoteToCRMRequestToJSON(value?: UploadNoteToCRMRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'sync_items': value['syncItems'] == null ? undefined : (mapValues(value['syncItems'], CRMSyncItemSelectionToJSON)),
    };
}

