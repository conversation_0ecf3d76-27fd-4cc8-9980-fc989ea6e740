/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const AttendeeType = {
    Client: 'client',
    User: 'user',
    Unknown: 'unknown'
} as const;
export type AttendeeType = typeof AttendeeType[keyof typeof AttendeeType];


export function instanceOfAttendeeType(value: any): boolean {
    for (const key in AttendeeType) {
        if (Object.prototype.hasOwnProperty.call(AttendeeType, key)) {
            if (AttendeeType[key as keyof typeof AttendeeType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function AttendeeTypeFromJSON(json: any): AttendeeType {
    return AttendeeTypeFromJSONTyped(json, false);
}

export function AttendeeTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): AttendeeType {
    return json as AttendeeType;
}

export function AttendeeTypeToJSON(value?: AttendeeType | null): any {
    return value as any;
}

