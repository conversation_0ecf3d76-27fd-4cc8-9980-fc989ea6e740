/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Represents selection state for a sync section
 * @export
 * @interface CRMSyncItemSelection
 */
export interface CRMSyncItemSelection {
    /**
     * 
     * @type {boolean}
     * @memberof CRMSyncItemSelection
     */
    includeSection?: boolean;
    /**
     * 
     * @type {Array<string>}
     * @memberof CRMSyncItemSelection
     */
    includedItems?: Array<string> | null;
}

/**
 * Check if a given object implements the CRMSyncItemSelection interface.
 */
export function instanceOfCRMSyncItemSelection(value: object): value is CRMSyncItemSelection {
    return true;
}

export function CRMSyncItemSelectionFromJSON(json: any): CRMSyncItemSelection {
    return CRMSyncItemSelectionFromJSONTyped(json, false);
}

export function CRMSyncItemSelectionFromJSONTyped(json: any, ignoreDiscriminator: boolean): CRMSyncItemSelection {
    if (json == null) {
        return json;
    }
    return {
        
        'includeSection': json['include_section'] == null ? undefined : json['include_section'],
        'includedItems': json['included_items'] == null ? undefined : json['included_items'],
    };
}

export function CRMSyncItemSelectionToJSON(value?: CRMSyncItemSelection | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'include_section': value['includeSection'],
        'included_items': value['includedItems'],
    };
}

