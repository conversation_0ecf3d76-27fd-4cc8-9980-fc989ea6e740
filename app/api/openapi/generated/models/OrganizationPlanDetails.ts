/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PlanFeature } from './PlanFeature';
import {
    PlanFeatureFromJSON,
    PlanFeatureFromJSONTyped,
    PlanFeatureToJSON,
} from './PlanFeature';
import type { PlanUser } from './PlanUser';
import {
    PlanUserFromJSON,
    PlanUserFromJSONTyped,
    PlanUserToJSON,
} from './PlanUser';

/**
 * 
 * @export
 * @interface OrganizationPlanDetails
 */
export interface OrganizationPlanDetails {
    /**
     * 
     * @type {string}
     * @memberof OrganizationPlanDetails
     */
    planTerm: OrganizationPlanDetailsPlanTermEnum;
    /**
     * 
     * @type {number}
     * @memberof OrganizationPlanDetails
     */
    meetingsAllowedPerTerm: number;
    /**
     * 
     * @type {number}
     * @memberof OrganizationPlanDetails
     */
    meetingsUsedThisTerm: number;
    /**
     * 
     * @type {Array<PlanFeature>}
     * @memberof OrganizationPlanDetails
     */
    features: Array<PlanFeature>;
    /**
     * 
     * @type {Array<PlanUser>}
     * @memberof OrganizationPlanDetails
     */
    users?: Array<PlanUser>;
}


/**
 * @export
 */
export const OrganizationPlanDetailsPlanTermEnum = {
    Monthly: 'monthly',
    Yearly: 'yearly'
} as const;
export type OrganizationPlanDetailsPlanTermEnum = typeof OrganizationPlanDetailsPlanTermEnum[keyof typeof OrganizationPlanDetailsPlanTermEnum];


/**
 * Check if a given object implements the OrganizationPlanDetails interface.
 */
export function instanceOfOrganizationPlanDetails(value: object): value is OrganizationPlanDetails {
    if (!('planTerm' in value) || value['planTerm'] === undefined) return false;
    if (!('meetingsAllowedPerTerm' in value) || value['meetingsAllowedPerTerm'] === undefined) return false;
    if (!('meetingsUsedThisTerm' in value) || value['meetingsUsedThisTerm'] === undefined) return false;
    if (!('features' in value) || value['features'] === undefined) return false;
    return true;
}

export function OrganizationPlanDetailsFromJSON(json: any): OrganizationPlanDetails {
    return OrganizationPlanDetailsFromJSONTyped(json, false);
}

export function OrganizationPlanDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): OrganizationPlanDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'planTerm': json['plan_term'],
        'meetingsAllowedPerTerm': json['meetings_allowed_per_term'],
        'meetingsUsedThisTerm': json['meetings_used_this_term'],
        'features': ((json['features'] as Array<any>).map(PlanFeatureFromJSON)),
        'users': json['users'] == null ? undefined : ((json['users'] as Array<any>).map(PlanUserFromJSON)),
    };
}

export function OrganizationPlanDetailsToJSON(value?: OrganizationPlanDetails | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'plan_term': value['planTerm'],
        'meetings_allowed_per_term': value['meetingsAllowedPerTerm'],
        'meetings_used_this_term': value['meetingsUsedThisTerm'],
        'features': ((value['features'] as Array<any>).map(PlanFeatureToJSON)),
        'users': value['users'] == null ? undefined : ((value['users'] as Array<any>).map(PlanUserToJSON)),
    };
}

