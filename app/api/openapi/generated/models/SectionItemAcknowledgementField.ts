/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';

/**
 * 
 * @export
 * @interface SectionItemAcknowledgementField
 */
export interface SectionItemAcknowledgementField {
    /**
     * 
     * @type {string}
     * @memberof SectionItemAcknowledgementField
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemAcknowledgementField
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemAcknowledgementField
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemAcknowledgementField
     */
    value?: boolean | null;
}



/**
 * Check if a given object implements the SectionItemAcknowledgementField interface.
 */
export function instanceOfSectionItemAcknowledgementField(value: object): value is SectionItemAcknowledgementField {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemAcknowledgementFieldFromJSON(json: any): SectionItemAcknowledgementField {
    return SectionItemAcknowledgementFieldFromJSONTyped(json, false);
}

export function SectionItemAcknowledgementFieldFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemAcknowledgementField {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'value': json['value'] == null ? undefined : json['value'],
    };
}

export function SectionItemAcknowledgementFieldToJSON(value?: SectionItemAcknowledgementField | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'value': value['value'],
    };
}

