/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientRecapStatus } from './ClientRecapStatus';
import {
    ClientRecapStatusFromJSON,
    ClientRecapStatusFromJSONTyped,
    ClientRecapStatusToJSON,
} from './ClientRecapStatus';

/**
 * 
 * @export
 * @interface ClientRecapResponse
 */
export interface ClientRecapResponse {
    /**
     * 
     * @type {ClientRecapStatus}
     * @memberof ClientRecapResponse
     */
    status: ClientRecapStatus;
    /**
     * 
     * @type {string}
     * @memberof ClientRecapResponse
     */
    uuid?: string | null;
}



/**
 * Check if a given object implements the ClientRecapResponse interface.
 */
export function instanceOfClientRecapResponse(value: object): value is ClientRecapResponse {
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function ClientRecapResponseFromJSON(json: any): ClientRecapResponse {
    return ClientRecapResponseFromJSONTyped(json, false);
}

export function ClientRecapResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientRecapResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'status': ClientRecapStatusFromJSON(json['status']),
        'uuid': json['uuid'] == null ? undefined : json['uuid'],
    };
}

export function ClientRecapResponseToJSON(value?: ClientRecapResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'status': ClientRecapStatusToJSON(value['status']),
        'uuid': value['uuid'],
    };
}

