/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Enum (mostly) matching the Note.PROCESSING_STATUS choices in Django model
 * 
 * Besides the statuses in Note.PROCESSING_STATUS, this enum also includes an UNKNOWN status, which
 * matches an empty status on the Django model.
 * @export
 */
export const ProcessingStatus = {
    Uploaded: 'uploaded',
    Processed: 'processed',
    Missing: 'missing',
    Scheduled: 'scheduled',
    Finalized: 'finalized',
    Unknown: 'unknown'
} as const;
export type ProcessingStatus = typeof ProcessingStatus[keyof typeof ProcessingStatus];


export function instanceOfProcessingStatus(value: any): boolean {
    for (const key in ProcessingStatus) {
        if (Object.prototype.hasOwnProperty.call(ProcessingStatus, key)) {
            if (ProcessingStatus[key as keyof typeof ProcessingStatus] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ProcessingStatusFromJSON(json: any): ProcessingStatus {
    return ProcessingStatusFromJSONTyped(json, false);
}

export function ProcessingStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): ProcessingStatus {
    return json as ProcessingStatus;
}

export function ProcessingStatusToJSON(value?: ProcessingStatus | null): any {
    return value as any;
}

