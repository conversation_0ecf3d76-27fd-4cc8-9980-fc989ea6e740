/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ApiRoutersNoteModelsClient
 */
export interface ApiRoutersNoteModelsClient {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersNoteModelsClient
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersNoteModelsClient
     */
    name: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersNoteModelsClient
     */
    email: string | null;
}

/**
 * Check if a given object implements the ApiRoutersNoteModelsClient interface.
 */
export function instanceOfApiRoutersNoteModelsClient(value: object): value is ApiRoutersNoteModelsClient {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    return true;
}

export function ApiRoutersNoteModelsClientFromJSON(json: any): ApiRoutersNoteModelsClient {
    return ApiRoutersNoteModelsClientFromJSONTyped(json, false);
}

export function ApiRoutersNoteModelsClientFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersNoteModelsClient {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'email': json['email'],
    };
}

export function ApiRoutersNoteModelsClientToJSON(value?: ApiRoutersNoteModelsClient | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'email': value['email'],
    };
}

