/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const MeetingCategory = {
    Client: 'client',
    Internal: 'internal',
    Debrief: 'debrief'
} as const;
export type MeetingCategory = typeof MeetingCategory[keyof typeof MeetingCategory];


export function instanceOfMeetingCategory(value: any): boolean {
    for (const key in MeetingCategory) {
        if (Object.prototype.hasOwnProperty.call(MeetingCategory, key)) {
            if (MeetingCategory[key as keyof typeof MeetingCategory] === value) {
                return true;
            }
        }
    }
    return false;
}

export function MeetingCategoryFromJSON(json: any): MeetingCategory {
    return MeetingCategoryFromJSONTyped(json, false);
}

export function MeetingCategoryFromJSONTyped(json: any, ignoreDiscriminator: boolean): MeetingCategory {
    return json as MeetingCategory;
}

export function MeetingCategoryToJSON(value?: MeetingCategory | null): any {
    return value as any;
}

