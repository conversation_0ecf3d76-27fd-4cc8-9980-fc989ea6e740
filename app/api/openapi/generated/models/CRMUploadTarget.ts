/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Information about a CRM entity that can be used as a target for uploading data to a CRM.
 * @export
 * @interface CRMUploadTarget
 */
export interface CRMUploadTarget {
    /**
     * The identifier for the CRM target, typically a UUID or similar unique identifier.
     * @type {string}
     * @memberof CRMUploadTarget
     */
    id: string;
    /**
     * A user-friendly name for the CRM target, which can be displayed in the user selection UI.
     * @type {string}
     * @memberof CRMUploadTarget
     */
    name: string;
}

/**
 * Check if a given object implements the CRMUploadTarget interface.
 */
export function instanceOfCRMUploadTarget(value: object): value is CRMUploadTarget {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function CRMUploadTargetFromJSON(json: any): CRMUploadTarget {
    return CRMUploadTargetFromJSONTyped(json, false);
}

export function CRMUploadTargetFromJSONTyped(json: any, ignoreDiscriminator: boolean): CRMUploadTarget {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function CRMUploadTargetToJSON(value?: CRMUploadTarget | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

