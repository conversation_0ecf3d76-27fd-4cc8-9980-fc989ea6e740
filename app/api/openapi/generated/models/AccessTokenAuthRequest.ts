/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface AccessTokenAuthRequest
 */
export interface AccessTokenAuthRequest {
    /**
     * 
     * @type {string}
     * @memberof AccessTokenAuthRequest
     */
    accessToken: string;
}

/**
 * Check if a given object implements the AccessTokenAuthRequest interface.
 */
export function instanceOfAccessTokenAuthRequest(value: object): value is AccessTokenAuthRequest {
    if (!('accessToken' in value) || value['accessToken'] === undefined) return false;
    return true;
}

export function AccessTokenAuthRequestFromJSON(json: any): AccessTokenAuthRequest {
    return AccessTokenAuthRequestFromJSONTyped(json, false);
}

export function AccessTokenAuthRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): AccessTokenAuthRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'accessToken': json['access_token'],
    };
}

export function AccessTokenAuthRequestToJSON(value?: AccessTokenAuthRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'access_token': value['accessToken'],
    };
}

