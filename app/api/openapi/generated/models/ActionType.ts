/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const ActionType = {
    Delete: 'delete',
    Create: 'create',
    Keep: 'keep'
} as const;
export type ActionType = typeof ActionType[keyof typeof ActionType];


export function instanceOfActionType(value: any): boolean {
    for (const key in ActionType) {
        if (Object.prototype.hasOwnProperty.call(ActionType, key)) {
            if (ActionType[key as keyof typeof ActionType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ActionTypeFromJSON(json: any): ActionType {
    return ActionTypeFromJSONTyped(json, false);
}

export function ActionTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionType {
    return json as ActionType;
}

export function ActionTypeToJSON(value?: ActionType | null): any {
    return value as any;
}

