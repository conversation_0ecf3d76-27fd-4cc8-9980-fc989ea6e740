/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface AttendeeInfoLite
 */
export interface AttendeeInfoLite {
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfoLite
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfoLite
     */
    name: string;
}

/**
 * Check if a given object implements the AttendeeInfoLite interface.
 */
export function instanceOfAttendeeInfoLite(value: object): value is AttendeeInfoLite {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function AttendeeInfoLiteFromJSON(json: any): AttendeeInfoLite {
    return AttendeeInfoLiteFromJSONTyped(json, false);
}

export function AttendeeInfoLiteFromJSONTyped(json: any, ignoreDiscriminator: boolean): AttendeeInfoLite {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
    };
}

export function AttendeeInfoLiteToJSON(value?: AttendeeInfoLite | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
    };
}

