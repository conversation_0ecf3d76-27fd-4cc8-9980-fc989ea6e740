/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AttendeeType } from './AttendeeType';
import {
    AttendeeTypeFromJSON,
    AttendeeTypeFromJSONTyped,
    AttendeeTypeToJSON,
} from './AttendeeType';

/**
 * 
 * @export
 * @interface AttendeeInfo
 */
export interface AttendeeInfo {
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfo
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfo
     */
    name: string;
    /**
     * 
     * @type {AttendeeType}
     * @memberof AttendeeInfo
     */
    type?: AttendeeType;
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfo
     */
    speakerTime?: string | null;
    /**
     * 
     * @type {number}
     * @memberof AttendeeInfo
     */
    speakerPercentage?: number | null;
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfo
     */
    speakerAlias?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfo
     */
    clientUuid?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AttendeeInfo
     */
    userUuid?: string | null;
}



/**
 * Check if a given object implements the AttendeeInfo interface.
 */
export function instanceOfAttendeeInfo(value: object): value is AttendeeInfo {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function AttendeeInfoFromJSON(json: any): AttendeeInfo {
    return AttendeeInfoFromJSONTyped(json, false);
}

export function AttendeeInfoFromJSONTyped(json: any, ignoreDiscriminator: boolean): AttendeeInfo {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'type': json['type'] == null ? undefined : AttendeeTypeFromJSON(json['type']),
        'speakerTime': json['speaker_time'] == null ? undefined : json['speaker_time'],
        'speakerPercentage': json['speaker_percentage'] == null ? undefined : json['speaker_percentage'],
        'speakerAlias': json['speaker_alias'] == null ? undefined : json['speaker_alias'],
        'clientUuid': json['client_uuid'] == null ? undefined : json['client_uuid'],
        'userUuid': json['user_uuid'] == null ? undefined : json['user_uuid'],
    };
}

export function AttendeeInfoToJSON(value?: AttendeeInfo | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'type': AttendeeTypeToJSON(value['type']),
        'speaker_time': value['speakerTime'],
        'speaker_percentage': value['speakerPercentage'],
        'speaker_alias': value['speakerAlias'],
        'client_uuid': value['clientUuid'],
        'user_uuid': value['userUuid'],
    };
}

