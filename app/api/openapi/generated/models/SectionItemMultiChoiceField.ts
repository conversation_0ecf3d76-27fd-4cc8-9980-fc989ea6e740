/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';
import type { LabeledEntity } from './LabeledEntity';
import {
    LabeledEntityFromJSON,
    LabeledEntityFromJSONTyped,
    LabeledEntityToJSON,
} from './LabeledEntity';

/**
 * 
 * @export
 * @interface SectionItemMultiChoiceField
 */
export interface SectionItemMultiChoiceField {
    /**
     * 
     * @type {string}
     * @memberof SectionItemMultiChoiceField
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemMultiChoiceField
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemMultiChoiceField
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {Array<LabeledEntity>}
     * @memberof SectionItemMultiChoiceField
     */
    options?: Array<LabeledEntity> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SectionItemMultiChoiceField
     */
    value?: Array<string> | null;
}



/**
 * Check if a given object implements the SectionItemMultiChoiceField interface.
 */
export function instanceOfSectionItemMultiChoiceField(value: object): value is SectionItemMultiChoiceField {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionItemMultiChoiceFieldFromJSON(json: any): SectionItemMultiChoiceField {
    return SectionItemMultiChoiceFieldFromJSONTyped(json, false);
}

export function SectionItemMultiChoiceFieldFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemMultiChoiceField {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'options': json['options'] == null ? undefined : ((json['options'] as Array<any>).map(LabeledEntityFromJSON)),
        'value': json['value'] == null ? undefined : json['value'],
    };
}

export function SectionItemMultiChoiceFieldToJSON(value?: SectionItemMultiChoiceField | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'options': value['options'] == null ? undefined : ((value['options'] as Array<any>).map(LabeledEntityToJSON)),
        'value': value['value'],
    };
}

