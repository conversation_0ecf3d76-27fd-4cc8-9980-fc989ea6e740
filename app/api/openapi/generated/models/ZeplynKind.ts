/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const ZeplynKind = {
    User: 'user',
    Client: 'client'
} as const;
export type ZeplynKind = typeof ZeplynKind[keyof typeof ZeplynKind];


export function instanceOfZeplynKind(value: any): boolean {
    for (const key in ZeplynKind) {
        if (Object.prototype.hasOwnProperty.call(<PERSON><PERSON>lynK<PERSON>, key)) {
            if (<PERSON><PERSON>lynKind[key as keyof typeof ZeplynKind] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ZeplynKindFromJSON(json: any): ZeplynKind {
    return Z<PERSON>lynKindFromJSONTyped(json, false);
}

export function <PERSON><PERSON>lynKindFromJSONTyped(json: any, ignoreDiscriminator: boolean): ZeplynKind {
    return json as Z<PERSON>lynK<PERSON>;
}

export function Z<PERSON>lynKindToJSON(value?: ZeplynKind | null): any {
    return value as any;
}

