/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SwapPair
 */
export interface SwapPair {
    /**
     * Current speaker alias (Name)
     * @type {string}
     * @memberof SwapPair
     */
    currentAlias: string;
    /**
     * New speaker alias (UUID)
     * @type {string}
     * @memberof SwapPair
     */
    newAlias: string;
}

/**
 * Check if a given object implements the SwapPair interface.
 */
export function instanceOfSwapPair(value: object): value is SwapPair {
    if (!('currentAlias' in value) || value['currentAlias'] === undefined) return false;
    if (!('newAlias' in value) || value['newAlias'] === undefined) return false;
    return true;
}

export function SwapPairFromJSON(json: any): SwapPair {
    return SwapPairFromJSONTyped(json, false);
}

export function SwapPairFromJSONTyped(json: any, ignoreDiscriminator: boolean): SwapPair {
    if (json == null) {
        return json;
    }
    return {
        
        'currentAlias': json['current_alias'],
        'newAlias': json['new_alias'],
    };
}

export function SwapPairToJSON(value?: SwapPair | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'current_alias': value['currentAlias'],
        'new_alias': value['newAlias'],
    };
}

