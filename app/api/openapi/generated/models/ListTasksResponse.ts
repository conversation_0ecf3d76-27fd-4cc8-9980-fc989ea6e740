/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { TaskResponse } from './TaskResponse';
import {
    TaskResponseFromJSON,
    TaskResponseFromJSONTyped,
    TaskResponseToJSON,
} from './TaskResponse';

/**
 * 
 * @export
 * @interface ListTasksResponse
 */
export interface ListTasksResponse {
    /**
     * 
     * @type {Array<TaskResponse>}
     * @memberof ListTasksResponse
     */
    data: Array<TaskResponse>;
}

/**
 * Check if a given object implements the ListTasksResponse interface.
 */
export function instanceOfListTasksResponse(value: object): value is ListTasksResponse {
    if (!('data' in value) || value['data'] === undefined) return false;
    return true;
}

export function ListTasksResponseFromJSON(json: any): ListTasksResponse {
    return ListTasksResponseFromJSONTyped(json, false);
}

export function ListTasksResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListTasksResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'data': ((json['data'] as Array<any>).map(TaskResponseFromJSON)),
    };
}

export function ListTasksResponseToJSON(value?: ListTasksResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'data': ((value['data'] as Array<any>).map(TaskResponseToJSON)),
    };
}

