/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ApiRoutersAttendeeClientResponse } from './ApiRoutersAttendeeClientResponse';
import {
    ApiRoutersAttendeeClientResponseFromJSON,
    ApiRoutersAttendeeClientResponseFromJSONTyped,
    ApiRoutersAttendeeClientResponseToJSON,
} from './ApiRoutersAttendeeClientResponse';
import type { UserResponse } from './UserResponse';
import {
    UserResponseFromJSON,
    UserResponseFromJSONTyped,
    UserResponseToJSON,
} from './UserResponse';

/**
 * 
 * @export
 * @interface ListAttendeesResponse
 */
export interface ListAttendeesResponse {
    /**
     * 
     * @type {Array<UserResponse>}
     * @memberof ListAttendeesResponse
     */
    users: Array<UserResponse>;
    /**
     * 
     * @type {Array<ApiRoutersAttendeeClientResponse>}
     * @memberof ListAttendeesResponse
     */
    clients: Array<ApiRoutersAttendeeClientResponse>;
}

/**
 * Check if a given object implements the ListAttendeesResponse interface.
 */
export function instanceOfListAttendeesResponse(value: object): value is ListAttendeesResponse {
    if (!('users' in value) || value['users'] === undefined) return false;
    if (!('clients' in value) || value['clients'] === undefined) return false;
    return true;
}

export function ListAttendeesResponseFromJSON(json: any): ListAttendeesResponse {
    return ListAttendeesResponseFromJSONTyped(json, false);
}

export function ListAttendeesResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListAttendeesResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'users': ((json['users'] as Array<any>).map(UserResponseFromJSON)),
        'clients': ((json['clients'] as Array<any>).map(ApiRoutersAttendeeClientResponseFromJSON)),
    };
}

export function ListAttendeesResponseToJSON(value?: ListAttendeesResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'users': ((value['users'] as Array<any>).map(UserResponseToJSON)),
        'clients': ((value['clients'] as Array<any>).map(ApiRoutersAttendeeClientResponseToJSON)),
    };
}

