import { NavLink, useFetcher, useParams } from "react-router";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { cn } from "~/@shadcn/utils";
import { ApiRoutersTaskModelsClient } from "~/api/openapi/generated";
import { AfterHydration } from "~/utils/hydration";

type TaskRowProps = {
  uuid: string;
  completed: boolean;
  title: string;
  dueAt?: Date | string | null;
  selectedAssignee: ApiRoutersTaskModelsClient | null;
  to: string;
};
const formatDate = (date: Date) => {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

export const TaskRow = ({
  uuid,
  completed,
  title,
  dueAt,
  selectedAssignee,
  to,
}: TaskRowProps) => {
  const { id } = useParams();
  const { submit } = useFetcher();

  const isActive = id === uuid;

  return (
    <tr
      className={cn(
        "border-b border-gray-200 hover:bg-gray-100",
        isActive && "bg-[#FFF8F2]"
      )}
    >
      <td className="w-5 p-3">
        <Checkbox
          className="top-[2px] m-0"
          checked={completed}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            submit(null, {
              method: "post",
              action: `/tasks/${uuid}/toggle-complete`,
            });
          }}
        />
      </td>
      <td className="min-w-40 max-w-40 overflow-hidden text-ellipsis p-3 text-xs sm:text-sm md:min-w-56 md:max-w-56">
        <NavLink to={to} className="block text-blue-600 hover:underline">
          <div
            className={cn(
              "overflow-hidden text-ellipsis whitespace-nowrap",
              completed && "line-through"
            )}
          >
            {title}
          </div>
        </NavLink>
      </td>
      <td className="hidden overflow-hidden text-ellipsis whitespace-nowrap p-3 text-sm sm:table-cell sm:text-xs">
        {selectedAssignee?.name || "Unassigned"}
      </td>
      <td className="w-24 overflow-hidden text-ellipsis whitespace-nowrap p-3 text-xs  sm:text-sm">
        <AfterHydration>
          {dueAt ? formatDate(new Date(dueAt)) : "No due date"}
        </AfterHydration>
      </td>
    </tr>
  );
};
