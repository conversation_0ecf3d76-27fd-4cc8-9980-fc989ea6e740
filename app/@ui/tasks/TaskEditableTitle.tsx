import { useState } from "react";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { PageTitleInput } from "~/@ui/layout/LayoutV2";
import { Typography } from "~/@ui/Typography";
import { isValidTitle } from "~/utils/validation";

// Exports
type TaskEditableTitleProps = {
  id: string;
  name: string;
  completed?: boolean;
  title: string;
  onChange?: (nextTitle: string) => void;
};
export const TaskEditableTitle = ({
  id,
  name,
  completed,
  title,
  onChange,
}: TaskEditableTitleProps) => {
  const [error, setError] = useState<boolean>(false);
  return (
    <div className="flex w-full flex-col">
      <div className="flex gap-2 self-stretch">
        {completed && (
          <Checkbox
            defaultChecked={completed}
            id={`${id}.checkbox`}
            name={`${id}.checkbox`}
            className="mt-2"
          />
        )}
        <PageTitleInput
          id={id}
          name={name}
          required
          color={error ? "error" : "default"}
          defaultValue={title}
          autoFocus
          onChange={(event) => {
            const value = event.currentTarget.value;
            setError(!isValidTitle(value));
            onChange?.(event.currentTarget.value);
          }}
          placeholder="Add a task title"
        />
      </div>
      {error && (
        <Typography variant="body2" color="error">
          A task title is required
        </Typography>
      )}
    </div>
  );
};
