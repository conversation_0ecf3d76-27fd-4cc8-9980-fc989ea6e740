import React, { useEffect, useState } from "react";
import { DatePicker } from "~/@ui/DatePicker";
import { TimePicker } from "~/@ui/TimePicker";

// Exports
type TaskDueAtCardProps = {
  dueAt?: Date | null;
  inputName?: string;
  onAccept?: (value: Date | null) => void;
};

export const TaskDueAtCard = ({
  dueAt,
  inputName = "dueAt",
  onAccept,
}: TaskDueAtCardProps) => {
  const [unsavedDueAt, setUnsavedDueAt] = useState<Date | null>(() =>
    dueAt ? new Date(dueAt) : null
  );

  useEffect(() => onAccept?.(unsavedDueAt), [onAccept, unsavedDueAt]);

  const setLocalDate = (nextDate?: Date, keepTime = false) => {
    if (!nextDate) {
      // Clear date or set it to today with current time
      const now = new Date();
      const newDate = keepTime
        ? now
        : new Date(now.getFullYear(), now.getMonth(), now.getDate());
      setUnsavedDueAt(newDate);
      return;
    }

    const current = unsavedDueAt || new Date();

    const mergedTimestamp = keepTime
      ? new Date(
          current.getFullYear(),
          current.getMonth(),
          current.getDate(),
          nextDate.getHours(),
          nextDate.getMinutes(),
          nextDate.getSeconds()
        )
      : new Date(
          nextDate.getFullYear(),
          nextDate.getMonth(),
          nextDate.getDate(),
          current.getHours(),
          current.getMinutes(),
          current.getSeconds()
        );

    setUnsavedDueAt(mergedTimestamp);
  };

  return (
    <React.Fragment>
      <DatePicker
        id="taskDatePicker"
        name="taskDatePicker"
        label="Due date"
        date={unsavedDueAt ?? undefined}
        onSelect={(nextDate) => setLocalDate(nextDate, false)}
      />
      <TimePicker
        id="taskTimePicker"
        name="taskTimePicker"
        label="Due time"
        date={unsavedDueAt ?? undefined}
        onSelect={(nextTime) => setLocalDate(nextTime, true)}
      />
      {unsavedDueAt && (
        <input
          name={inputName}
          type="hidden"
          value={unsavedDueAt.toISOString()}
        />
      )}
    </React.Fragment>
  );
};
