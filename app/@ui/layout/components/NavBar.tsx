import { Link, NavLink } from "react-router";
import {
  ChartNoAxesCombined,
  House,
  ListChecks,
  LogOut,
  NotepadText,
  Settings,
  Users,
} from "lucide-react";
import { cva } from "class-variance-authority";

import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { useFlag } from "~/context/flags";
import { CompanyIcon } from "~/@ui/assets/CompanyIcon";
import OnboardingMenu from "./OnboardingMenu";

const NavBar = () => {
  const isClientViewEnabled = !!useFlag("EnableClientView");
  const isAdvisorHubEnabled = !!useFlag("EnableDashboardFeature");
  const isInsightsDashboardEnabled = !!useFlag(
    "EnablePracticeInsightsDashboard"
  );
  const isOnboardingEnabled = !!useFlag("EnableInAppTutorials");

  const primaryMenuItems = getPrimaryMenuItems(
    isAdvisorHubEnabled,
    isClientViewEnabled,
    isInsightsDashboardEnabled
  );
  const secondaryMenuItems = getSecondaryMenuItems();

  return (
    <nav className="hidden w-16 flex-col items-center bg-muted py-2 md:flex">
      {/* logo */}
      <Link to="/">
        <CompanyIcon className="h-10 w-10" />
      </Link>

      <div className="mt-6 flex flex-col gap-2">
        {primaryMenuItems.map(({ label, path, tooltip, icon: Icon }) => {
          return (
            <Tooltip key={label}>
              <TooltipTrigger asChild>
                <NavLink
                  to={path}
                  className={navLinkVariants({ variant: "desktop-primary" })}
                >
                  <Icon />
                  <span className="text-xs md:hidden">{label}</span>
                </NavLink>
              </TooltipTrigger>
              <TooltipContent side="right">{tooltip}</TooltipContent>
            </Tooltip>
          );
        })}
      </div>

      <div className="mt-auto flex flex-col gap-2">
        {isOnboardingEnabled && <OnboardingMenu isV2 />}
        {secondaryMenuItems.map(
          ({ label, path, tooltip, icon: Icon, iconClass }) => {
            return (
              <Tooltip key={label}>
                <TooltipTrigger asChild>
                  <NavLink
                    to={path}
                    className={`${navLinkVariants({ variant: "desktop-secondary" })} text-primary-foreground_`}
                  >
                    <Icon className={iconClass} />
                    <span className="text-xs md:hidden">{label}</span>
                  </NavLink>
                </TooltipTrigger>
                <TooltipContent side="right">{tooltip}</TooltipContent>
              </Tooltip>
            );
          }
        )}
      </div>
    </nav>
  );
};

const navLinkVariants = cva(
  "inline-flex shrink-0 select-none items-center justify-center px-3 hover:bg-card [&.active]:bg-card [&.active]:text-card-foreground",
  {
    variants: {
      variant: {
        "desktop-primary": "h-12 w-16",
        "desktop-secondary": "h-12 w-16",
        mobile:
          "h-12 w-1/4 flex-col [&.active]:shadow-[0_0.25rem_0_0_hsl(var(--warning))_inset]",
      },
    },
  }
);

function getPrimaryMenuItems(
  isAdvisorHubEnabled: boolean,
  isClientViewEnabled: boolean,
  isInsightsDashboardEnabled: boolean
) {
  return [
    ...(isAdvisorHubEnabled
      ? [
          {
            label: "Hub",
            path: "/dashboard",
            tooltip: "Advisor Hub",
            icon: House,
          },
        ]
      : []),
    {
      label: "Notes",
      path: "/notes",
      tooltip: "Notes",
      icon: NotepadText,
    },
    {
      label: "Tasks",
      path: "/tasks",
      tooltip: "Tasks",
      icon: ListChecks,
    },
    ...(isClientViewEnabled
      ? [
          {
            label: "Clients",
            path: "/clients",
            tooltip: "Clients",
            icon: Users,
          },
        ]
      : []),
    ...(isInsightsDashboardEnabled
      ? [
          {
            label: "Insights",
            path: "/insights",
            tooltip: "Insights",
            icon: ChartNoAxesCombined,
          },
        ]
      : []),
  ];
}

function getSecondaryMenuItems() {
  return [
    {
      label: "Settings",
      path: "/settings",
      tooltip: "Settings",
      icon: Settings,
    },
    {
      label: "Logout",
      path: "/auth/logout",
      tooltip: "Logout",
      icon: LogOut,
      iconClass: "text-destructive",
    },
  ];
}

export default NavBar;
