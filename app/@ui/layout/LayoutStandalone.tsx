import { ReactNode } from "react";
import { Typography } from "~/@ui/Typography";
import { CompanyLogo } from "~/@ui/assets/CompanyLogo";
import { Link } from "~/@ui/Link";
import { cn } from "~/@shadcn/utils";

// Exports
type Props = { children: ReactNode; title: string };
const LayoutStandaloneV2 = ({ children, title }: Props) => (
  <div
    className={cn(
      "flex h-full w-full flex-col items-center overflow-y-auto",
      "lg:flex-row lg:overflow-y-visible"
    )}
  >
    {/* Header */}
    <header
      className={cn(
        "flex h-60 w-full shrink-0 flex-col items-center justify-end gap-8 bg-foreground px-5 py-8",
        "lg:h-full lg:w-1/2 lg:items-end lg:justify-center lg:gap-4 lg:px-12"
      )}
    >
      <div className="flex pr-6 lg:pr-0">
        <CompanyLogo className="h-[4rem] w-[12rem] lg:h-[6rem] lg:w-[18rem]" />
      </div>
      <Typography
        className="lg:mt-6 lg:hidden"
        color="secondary"
        variant="h2"
        asChild
      >
        <h1>{title}</h1>
      </Typography>
      <Typography
        className="hidden lg:block"
        color="secondary"
        variant="h2"
        asChild
      >
        <h1>AI assistant for financial advisors</h1>
      </Typography>
    </header>

    {/* Content */}
    <main
      className={cn(
        "flex w-full flex-grow flex-col items-center justify-between px-5",
        "lg:mt-auto lg:h-full lg:w-1/2 lg:items-start lg:justify-normal lg:overflow-y-auto lg:px-12"
      )}
    >
      <div
        className={cn(
          "flex w-full max-w-[32rem] flex-shrink-0 flex-grow flex-col py-8",
          "lg:my-auto lg:flex-grow-0"
        )}
      >
        <Typography className="hidden lg:mb-8 lg:block" variant="h1" asChild>
          <h1>{title}</h1>
        </Typography>
        {children}
      </div>
      {/* Footer */}
      <footer
        className={cn("flex w-full max-w-[32rem] flex-col py-8", "lg:mb-auto")}
      >
        <Typography className="text-center" color="secondary" variant="body2">
          By using Zeplyn you agree to the{" "}
          <Link href="https://www.zeplyn.ai/terms-and-conditions">
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link href="https://www.zeplyn.ai/privacy-and-policy">
            Privacy Policy
          </Link>
        </Typography>
      </footer>
    </main>
  </div>
);

export { LayoutStandaloneV2 as LayoutStandalone };
