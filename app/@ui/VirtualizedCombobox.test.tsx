import React from "react";
import { render, screen } from "@testing-library/react";
import { VirtualizedCombobox } from "./VirtualizedCombobox";

// Mock react-window for virtualization testing
vi.mock("react-window", () => ({
  FixedSizeList: ({ children, itemCount, itemSize }: any) => {
    const items = [];
    for (let i = 0; i < itemCount; i++) {
      items.push(
        <div key={i} data-testid={`virtualized-item-${i}`}>
          {children({
            index: i,
            style: { height: itemSize },
          })}
        </div>
      );
    }
    return <div data-testid="virtualized-list">{items}</div>;
  },
}));

describe("VirtualizedCombobox", () => {
  const mockOptions = [
    { value: "1", label: "Option 1", leftIcon: <span>🔥</span> },
    { value: "2", label: "Option 2" },
    { value: "3", label: "Option 3" },
    { value: "4", label: "Another Option" },
    { value: "5", label: "Last Option" },
  ];

  const mockLoadOptions = vi.fn();
  const mockOnChange = vi.fn();

  const defaultProps = {
    options: mockOptions,
    onChange: mockOnChange,
    loadOptions: mockLoadOptions,
  };

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders with placeholder text", () => {
    render(
      <VirtualizedCombobox {...defaultProps} placeholder="Select an option" />
    );

    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByText("Select an option")).toBeInTheDocument();
  });

  it("renders with selected object", () => {
    const selectedObject = { value: "1", label: "Option 1" };
    render(
      <VirtualizedCombobox {...defaultProps} selectedObject={selectedObject} />
    );

    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByText("Option 1")).toBeInTheDocument();
  });

  it("calls loadOptions on mount when open state changes", () => {
    render(<VirtualizedCombobox {...defaultProps} />);

    expect(mockLoadOptions).toBeDefined();
  });

  it("handles option selection through onChange prop", () => {
    render(<VirtualizedCombobox {...defaultProps} />);

    // Test that onChange function is properly passed
    expect(mockOnChange).toBeDefined();

    // We can test the component renders correctly with different selected states
    const button = screen.getByRole("combobox");
    expect(button).toBeInTheDocument();
  });

  it("renders left icon when provided", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        leftIcon={<span data-testid="left-icon">🔍</span>}
      />
    );

    expect(screen.getByTestId("left-icon")).toBeInTheDocument();
  });

  it("handles empty options array", () => {
    render(<VirtualizedCombobox {...defaultProps} options={[]} />);

    expect(screen.getByRole("combobox")).toBeInTheDocument();
  });

  it("applies custom className props", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        triggerClassName="custom-trigger"
        commandClassName="custom-command"
      />
    );

    expect(screen.getByRole("combobox")).toHaveClass("custom-trigger");
  });
});
