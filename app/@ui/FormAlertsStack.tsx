import { useMemo } from "react";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { z } from "zod";
import { Alert, AlertDescription } from "~/@shadcn/ui/alert";

// Types
const FormErrorsStruct = z.object({ errors: z.array(z.string()) });

// Exports
export const useFormErrors = (data: unknown) =>
  useMemo(() => {
    const result = FormErrorsStruct.safeParse(data);
    return result.success ? result.data.errors : undefined;
  }, [data]);

type Props = { errors?: string[] };
export const FormAlertsStack = ({ errors }: Props) => {
  if (!errors || errors.length === 0) return null;
  return (
    <div className="mb-4 flex flex-col gap-2">
      {errors.map((errorMessage) => (
        <Alert key={errorMessage} variant="destructive">
          <ExclamationTriangleIcon className="h-5 w-5 text-destructive" />
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      ))}
    </div>
  );
};
