import { Typography } from "~/@ui/Typography";

const EmailPreviewContent = ({
  email,
}: {
  email: {
    subject: string;
    body: string;
  };
}) => {
  return (
    <div className="space-y-4">
      <Typography variant="body2">
        <strong>Subject:</strong> {email.subject}
      </Typography>
      <Typography variant="body2">
        <strong>Body:</strong>
      </Typography>
      <div className="max-h-[60vh] overflow-auto rounded-md border bg-gray-50 p-4">
        <div
          className="prose max-w-none [&_a]:text-blue-600 [&_a]:underline hover:[&_a]:text-blue-800"
          dangerouslySetInnerHTML={{ __html: email.body }}
        />
      </div>
    </div>
  );
};

export default EmailPreviewContent;
