import { Slot } from "@radix-ui/react-slot";
import * as React from "react";
import { cn } from "~/@shadcn/utils";

// Exports
export type LinkProps = React.AnchorHTMLAttributes<HTMLAnchorElement> & {
  asChild?: boolean;
};
export const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(
  ({ className, children, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "a";
    return (
      <Comp
        ref={ref}
        className={cn(
          "text-sm font-semibold text-primary hover:underline",
          className
        )}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);
Link.displayName = "Link";
