import { ComponentProps } from "react";
import { useSubmit } from "react-router";
import { CircleCheck } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Spinner } from "~/@ui/assets/Spinner";

// Exports
type Props = ComponentProps<typeof Button> & {
  title?: string;
  formId?: string;
  tooltip?: string;
  loading?: boolean;
};
export const SaveButton = ({
  loading = false,
  title = "Save",
  formId,
  tooltip,
  onClick,
  disabled,
  ...props
}: Props) => {
  const submit = useSubmit();
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-block">
          <Button
            variant="success"
            size="default"
            disabled={disabled}
            onClick={(event) => {
              if (onClick) {
                onClick(event);
                return;
              }
              if (formId) {
                const formElem = document.getElementById(formId);
                if (formElem instanceof HTMLFormElement) {
                  submit(formElem);
                  return;
                }
              }
            }}
            {...props}
          >
            {title}
            {loading ? <Spinner /> : <CircleCheck />}
          </Button>
        </span>
      </TooltipTrigger>
      <TooltipContent>{tooltip ?? "Save"}</TooltipContent>
    </Tooltip>
  );
};
