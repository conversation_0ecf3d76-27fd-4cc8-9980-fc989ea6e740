import { But<PERSON> } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Link } from "react-router";
import { ComponentProps } from "react";
import { PlusIcon } from "lucide-react";

// Exports
type Props = {
  label?: string;
  to: ComponentProps<typeof Link>["to"];
  tooltip?: string;
};

export const CreateButton = ({ label, to, tooltip }: Props) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <Button variant="special" className="pr-3">
        <Link to={to} className="flex items-center gap-1">
          <PlusIcon />
          {label ?? "Create"}
        </Link>
      </Button>
    </TooltipTrigger>
    <TooltipContent>{tooltip ?? "Create"}</TooltipContent>
  </Tooltip>
);
