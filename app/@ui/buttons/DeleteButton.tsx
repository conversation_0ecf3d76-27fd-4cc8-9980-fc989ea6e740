import { But<PERSON> } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { TrashIcon } from "lucide-react";

// Exports
type Props = {
  onClick: () => void;
  tooltip?: string;
  disabled?: boolean;
};

export const DeleteButton = ({ onClick, tooltip, disabled }: Props) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <Button
        variant="ghost-destructive"
        size="default"
        onClick={onClick}
        disabled={disabled}
      >
        Delete
        <TrashIcon className="!h-5 !w-5" />
      </Button>
    </TooltipTrigger>
    <TooltipContent>{tooltip ?? "Delete"}</TooltipContent>
  </Tooltip>
);
