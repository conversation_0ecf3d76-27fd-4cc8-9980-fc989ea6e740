import { ComponentProps } from "react";
import { Link } from "react-router";
import { Edit2 } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

type Props = {
  to?: ComponentProps<typeof Link>["to"];
  tooltip?: string;
  disabled?: boolean;
  onClick?: () => void;
};

export const EditButton = ({ to, tooltip, disabled, onClick }: Props) => (
  <>
    {disabled ? (
      <Button variant="ghost" size="default" disabled={disabled}>
        Edit
        <Edit2 className="!h-5 !w-5" />
      </Button>
    ) : (
      <Tooltip>
        <TooltipTrigger asChild>
          {to ? (
            <Button variant="ghost" size="default" asChild>
              <Link to={to}>
                Edit
                <Edit2 className="!h-5 !w-5" />
              </Link>
            </Button>
          ) : (
            <Button variant="ghost" size="default" onClick={onClick}>
              Edit
              <Edit2 className="!h-5 !w-5" />
            </Button>
          )}
        </TooltipTrigger>
        <TooltipContent>{tooltip ?? "Edit"}</TooltipContent>
      </Tooltip>
    )}
  </>
);
