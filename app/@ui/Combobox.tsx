import * as React from "react";
import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Command,
  CommandInput,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "~/@shadcn/ui/command";
import { Popover, PopoverTrigger, PopoverContent } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { CheckIcon, CaretSortIcon } from "@radix-ui/react-icons";
import { Typography } from "~/@ui/Typography";

// Types
export type OptionType = {
  leftIcon?: React.ReactNode;
  label: string;
  value: string;
};

// Exports
type Props = {
  options: OptionType[];
  selected?: string;
  placeholder?: string;
  onChange: React.Dispatch<React.SetStateAction<string | undefined>>;
  commandClassName?: string;
  triggerClassName?: string;
  leftIcon?: React.ReactNode;
  searchOnLabel?: boolean;
  disabled?: boolean;
  modal?: boolean;
};
export const Combobox = ({
  options,
  selected,
  placeholder = "Select an item",
  onChange,
  commandClassName,
  triggerClassName,
  leftIcon,
  searchOnLabel = false,
  disabled = false,
  modal = false,
  ...props
}: Props) => {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen} modal={modal} {...props}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between gap-2 rounded-2xl p-3 text-base",
            "h-fit min-h-fit",
            triggerClassName
          )}
          disabled={disabled}
        >
          {leftIcon}
          {(() => {
            if (!selected) {
              return (
                <Typography
                  className="inline-flex grow"
                  color="secondary"
                  asChild
                >
                  <span>{placeholder}</span>
                </Typography>
              );
            }

            const selectedOption = options.find(
              ({ value }) => value === selected
            );
            if (!selectedOption) return null;

            return (
              <>
                <span className="inline-flex shrink-0 [&>svg]:h-6 [&>svg]:w-6">
                  {selectedOption.leftIcon}
                </span>
                <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
                  {selectedOption.label}
                </span>
              </>
            );
          })()}
          <CaretSortIcon className="h-6 w-6 shrink-0 self-baseline" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          "w-fit min-w-[var(--radix-popper-anchor-width)] p-0",
          commandClassName
        )}
      >
        <Command
          className={commandClassName}
          filter={(value, search) => {
            if (searchOnLabel) {
              const label =
                options.find((option) => option.value === value)?.label ??
                value;
              if (
                label.toLocaleLowerCase().includes(search.toLocaleLowerCase())
              )
                return 1;
              return 0;
            } else
              return value
                .toLocaleLowerCase()
                .includes(search.toLocaleLowerCase())
                ? 1
                : 0;
          }}
        >
          <CommandInput placeholder="Search..." />
          <CommandList>
            <CommandEmpty>No item found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  className="gap-2"
                  value={option.value}
                  onSelect={(currentValue) => {
                    if (disabled) {
                      return;
                    }
                    onChange(
                      currentValue === selected ? undefined : currentValue
                    );
                    setOpen(false);
                  }}
                >
                  {option.leftIcon ?? <span className="h-4 w-4 shrink-0" />}
                  <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
                    {option.label}
                  </span>
                  <CheckIcon
                    className={cn(
                      "ml-auto h-4 w-4 shrink-0",
                      selected === option.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
