import { render, screen } from "@testing-library/react";
import { AttendeeTag } from "./attendeeTags";
import { AttendeeInfo } from "~/api/openapi/generated";

describe("AttendeeTag", () => {
  it("renders AttendeeTag with valid type", () => {
    const attendee: AttendeeInfo = {
      uuid: "123",
      name: "<PERSON>",
      type: "client",
      speakerPercentage: 25,
    };
    render(<AttendeeTag {...attendee} />);
    expect(screen.getByText("John <PERSON> (25%)")).toBeInTheDocument();
  });

  it("renders AttendeeTag without speaker percentage", () => {
    const attendee: AttendeeInfo = {
      uuid: "123",
      name: "<PERSON>",
      type: "client",
    };
    render(<AttendeeTag {...attendee} />);
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    const icon = screen.getByTestId("ContactIcon");
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute("aria-label", "Client");
  });

  it("renders the correct icon for a user attendee ", () => {
    const attendee: AttendeeInfo = {
      uuid: "123",
      name: "John Doe",
      type: "user",
    };
    render(<AttendeeTag {...attendee} />);
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    const icon = screen.getByTestId("UserIcon");
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute("aria-label", "User");
  });

  it("renders the correct icon for an unknown attendee ", () => {
    const attendee: AttendeeInfo = {
      uuid: "123",
      name: "John Doe",
      type: "unknown",
    };
    render(<AttendeeTag {...attendee} />);
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    const icon = screen.getByTestId("HelpCircleIcon");
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute("aria-label", "Unknown attendee");
  });

  it("does not render AttendeeTag with invalid type", () => {
    const attendee: AttendeeInfo = {
      uuid: "123",
      name: "John Doe",
    };
    render(<AttendeeTag {...attendee} />);
    expect(screen.queryByText("John Doe")).not.toBeInTheDocument();
  });
});
