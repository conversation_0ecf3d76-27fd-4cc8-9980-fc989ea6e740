import { Clock } from "lucide-react";
import { ReactNode } from "react";

import { Typography } from "~/@ui/Typography";

type Props = {
  children: ReactNode;
  disabled?: boolean;
};
export const TimeStamp = ({ children, disabled = false }: Props) => (
  <span className="inline-flex shrink-0 items-center gap-2">
    <Clock
      className={disabled ? "text-secondary" : "text-foreground"}
      size={16}
    />
    <Typography variant="body2" color="secondary" asChild>
      <span>{children}</span>
    </Typography>
  </span>
);
