import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";
import { cn } from "~/@shadcn/utils";

export const typographyVariants = cva("", {
  variants: {
    variant: {
      default: "text-base font-normal",
      body1: "text-grey text-base font-thin",
      body2: "text-sm font-normal",
      body3: "text-sm font-semibold",
      h1: "text-3xl font-semibold",
      h2: "text-2xl font-normal",
      h3: "text-xl font-semibold leading-9",
      h4: "text-base font-semibold leading-5",
      h5: "text-base font-semibold",
      h6: "text-base font-semibold",
    },
    color: {
      default: "text-foreground",
      primary: "text-foreground",
      secondary: "text-secondary",
      error: "text-destructive",
      warning: "text-warning",
      success: "text-success",
    },
  },
  defaultVariants: {
    variant: "default",
    color: "default",
  },
});

export type TypographyProps = React.HTMLAttributes<HTMLParagraphElement> &
  VariantProps<typeof typographyVariants> & {
    asChild?: boolean;
  };
export const Typography = React.forwardRef<
  HTMLParagraphElement,
  TypographyProps
>(({ asChild = false, className, color, variant, ...props }, ref) => {
  const Comp = asChild ? Slot : "p";
  return (
    <Comp
      ref={ref}
      className={cn(typographyVariants({ color, variant }), className)}
      {...props}
    />
  );
});
Typography.displayName = "Typography";
