import {
  BoldItalicUnderlineToggles,
  CreateLink,
  headingsPlugin,
  InsertTable,
  linkDialogPlugin,
  linkPlugin,
  listsPlugin,
  ListsToggle,
  markdownShortcutPlugin,
  MDXEditor,
  Separator,
  tablePlugin,
  toolbarPlugin,
  UndoRedo,
} from "@mdxeditor/editor";
import { ClientOnly } from "remix-utils/client-only";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import "@mdxeditor/editor/style.css";
import "./MarkdownEditor.css";

const MarkdownEditor = ({
  markdown,
  onChange,
  readOnly,
  showToolbar,
}: {
  markdown: string;
  onChange?: (markdown: string) => void;
  readOnly?: boolean;
  showToolbar?: boolean;
  className?: string;
  contentEditableClassName?: string;
}) => {
  const plugins = [
    headingsPlugin(),
    tablePlugin(),
    listsPlugin(),
    linkPlugin(),
    linkDialogPlugin(),
    markdownShortcutPlugin(),
  ];
  if (showToolbar) {
    plugins.push(
      toolbarPlugin({
        toolbarContents: () => (
          <>
            <UndoRedo />
            <Separator />
            <BoldItalicUnderlineToggles />
            <Separator />
            <ListsToggle />
            <Separator />
            <CreateLink />
            <InsertTable />
          </>
        ),
      })
    );
  }
  return (
    <ClientOnly fallback={<Skeleton className="h-64 w-full" />}>
      {() => (
        <MDXEditor
          // WARNING: DON'T use `className` prop, since the same styles would otherwise get applied to .mdxeditor-popup-container as well
          contentEditableClassName="prose prose-slate font-sans"
          markdown={markdown}
          onChange={onChange}
          plugins={plugins}
          readOnly={readOnly}
        />
      )}
    </ClientOnly>
  );
};

export default MarkdownEditor;
