import { NavLink, LinkProps } from "react-router";
import { Calendar, ChevronRight, Hourglass, Mic, Video } from "lucide-react";
import { TimeStamp } from "~/@ui/TimeStamp";
import { AfterHydration } from "~/utils/hydration";
import { formatRelative } from "date-fns";
import { cn } from "~/@shadcn/utils";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/@shadcn/ui/card";
import { NoteResponse, ProcessingStatus } from "~/api/openapi/generated";
import { SerializeFrom } from "~/types/remix";
// Helpers
const getNoteIcon = (note: SerializeFrom<NoteResponse>) => {
  if (note.status === ProcessingStatus.Scheduled) return Calendar;
  if (note.noteType === "meeting_recording") return Video;
  return Mic;
};
const getFormattedTimestamp = (note: SerializeFrom<NoteResponse>) => {
  if (note.status === ProcessingStatus.Scheduled && note.scheduledStartTime) {
    const startTime = formatRelative(
      new Date(note.scheduledStartTime),
      new Date()
    );
    return `Scheduled for ${startTime}`;
  }

  const created = formatRelative(new Date(note.created), new Date());
  if (created.includes("at")) return `Created ${created}`;
  return `Created on ${created}`;
};

// Exports
type Props = LinkProps & {
  note: SerializeFrom<NoteResponse>;
  compact?: boolean;
};
export const NoteCard = ({
  note,
  to,
  compact = false,
  ...linkProps
}: Props) => {
  const disabled = note.status === ProcessingStatus.Uploaded;
  const Icon = getNoteIcon(note);
  return (
    <Card
      compact={compact}
      className={cn(
        "inline-flex min-w-0 grow flex-row items-center self-stretch hover:border-foreground hover:bg-accent",
        "[&.active]:border-transparent [&.active]:bg-warning-foreground/30 [&.active]:ring-2  [&.active]:ring-warning",
        disabled && "pointer-events-none opacity-50 shadow-none"
      )}
      // ⚠️ The whole Card is an anchor tag, so it must only contain inline
      // elements. This means no paragraphs, and all divs and buttons must be set
      // to display:inline or display:inline-flex.
      asChild
    >
      <NavLink
        // Set tabIndex to -1 to ensure tile is not focusable in disabled state
        tabIndex={disabled ? -1 : undefined}
        to={to}
        {...linkProps}
      >
        <CardHeader
          title={note.meetingName}
          className="grow overflow-hidden text-ellipsis whitespace-nowrap"
          asChild
        >
          <span>
            <CardTitle
              className="self-stretch overflow-hidden text-ellipsis whitespace-nowrap"
              variant={compact ? "default" : "h4"}
              asChild
            >
              <span>
                <Icon className="mr-1 inline h-6 w-6 text-primary" />
                <span className="align-middle">{note.meetingName}</span>
              </span>
            </CardTitle>
            {!compact && (
              <CardDescription
                className="inline-flex items-center gap-3 self-stretch"
                asChild
              >
                <span>
                  <TimeStamp disabled={disabled}>
                    <AfterHydration>
                      {getFormattedTimestamp(note)}
                    </AfterHydration>
                  </TimeStamp>
                </span>
              </CardDescription>
            )}
          </span>
        </CardHeader>

        {disabled && <Hourglass size={20} className="shrink-0 text-warning" />}
        {!disabled && <ChevronRight className="shrink-0" />}
      </NavLink>
    </Card>
  );
};
