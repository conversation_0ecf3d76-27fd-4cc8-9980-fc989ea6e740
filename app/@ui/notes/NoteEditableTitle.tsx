import { useState, useEffect, Dispatch } from "react";
import { cn } from "~/@shadcn/utils";
import { Typography } from "~/@ui/Typography";
import { EditableNoteActions } from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import { isValidTitle } from "~/utils/validation";

type NoteEditableTitleProps = {
  dispatch: Dispatch<EditableNoteActions>;
  title: string;
  disabled: boolean;
  onEdit: () => void;
};

export const NoteEditableTitle = ({
  dispatch,
  title,
  onEdit,
  disabled,
}: NoteEditableTitleProps) => {
  const [currentTitle, setCurrentTitle] = useState(title);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    setCurrentTitle(title);
  }, [title]);

  const handleBlur = () => {
    if (isValidTitle(currentTitle)) {
      dispatch({
        type: "updateMeetingName",
        nextMeetingName: currentTitle,
      });
      onEdit();
    } else {
      setError(true);
    }
  };

  const handleChange = (value: string) => {
    setError(false);
    setCurrentTitle(value);
    dispatch({
      type: "updateMeetingName",
      nextMeetingName: value,
    });
    onEdit();
  };

  return (
    <div className={cn("flex w-full flex-col py-2")}>
      {!disabled ? (
        <input
          autoFocus
          value={currentTitle}
          onBlur={handleBlur}
          onChange={(event) => handleChange(event.currentTarget.value)}
          placeholder="Add a note title"
          className={cn(
            "w-full border-none text-3xl font-semibold focus:outline-none",
            error ? "text-red-600" : "text-black"
          )}
        />
      ) : (
        <h2 className="text-3xl font-semibold" onClick={() => !disabled}>
          {currentTitle || "Add a note title"}
        </h2>
      )}
      {error && (
        <Typography variant="body2" color="error">
          A note title is required
        </Typography>
      )}
    </div>
  );
};
