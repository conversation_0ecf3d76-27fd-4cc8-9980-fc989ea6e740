import { cn } from "~/@shadcn/utils";

export type DividerProps = React.HTMLAttributes<HTMLDivElement> & {
  asChild?: boolean;
};
/*
──────────  children  ──────────
*/
export const Divider = ({ className, children, ...props }: DividerProps) => (
  <div
    className={cn(
      "flex shrink-0 whitespace-nowrap text-center text-secondary",
      children
        ? "before:mr-2 before:w-full before:self-center before:border-t before:border-border before:content-[''] after:ml-2 after:w-full after:self-center after:border-t after:border-border after:content-['']"
        : "w-full border-t border-border",
      className
    )}
    {...props}
  >
    {children}
  </div>
);
