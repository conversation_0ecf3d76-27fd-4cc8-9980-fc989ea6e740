export const RedtailLogo = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="43"
    height="43"
    viewBox="0 0 43 43"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21.2 42.4C9.5 42.4 0 32.9 0 21.2C0 9.5 9.5 0 21.2 0C32.9 0 42.4 9.5 42.4 21.2C42.4 32.9 32.9 42.4 21.2 42.4Z"
      fill="#C72127"
    />
    <path
      d="M10.281 33.2403C10.6326 33.2403 10.9841 33.123 11.2185 32.3018C11.4528 31.5979 12.1559 29.1343 12.3902 28.3131C12.6246 27.6092 13.2105 26.6706 14.0308 25.7321C14.2651 27.3745 14.4995 29.8381 14.7338 31.3632C14.851 31.8325 15.2025 32.0671 15.5541 32.0671C15.9056 32.0671 16.2572 31.7152 16.14 31.3632C16.14 31.0113 16.14 30.7767 16.0228 29.9555C16.0228 29.3689 16.14 26.3187 16.6087 24.9109C16.8431 24.207 18.0149 23.5032 18.4836 23.7378C19.0695 23.9724 21.8818 25.0282 23.0536 25.7321C23.6395 26.0841 24.2254 27.6092 24.4597 28.8996C24.6941 30.1901 25.0456 32.8883 25.3971 34.2961C25.5143 34.6481 25.7487 35 26.1002 35C26.4518 35 26.8033 34.6481 26.8033 34.2961C26.9205 32.5364 26.9205 29.1343 27.0377 27.2572C27.0377 26.6707 27.3892 26.436 27.8579 26.3187C28.4438 26.2014 28.561 26.6707 28.7953 27.3745C29.1469 28.7823 30.0843 32.6537 30.0843 32.6537C30.2015 33.0056 30.4359 33.3576 30.9046 33.3576C31.2561 33.3576 31.4905 33.0056 31.4905 32.6537C31.3733 31.2459 30.9046 28.4304 30.7874 26.3187C30.6702 25.2629 30.6702 23.7378 30.4359 22.5646C30.2015 20.8049 30.0843 20.6876 29.9671 19.5144C29.9671 19.1625 29.7328 18.224 29.9671 17.5201C30.2015 16.5816 31.1389 14.5872 31.3733 14.7045C31.9592 14.7045 32.7794 14.8219 33.3653 14.8219C34.1856 14.9392 34.7715 14.4699 35.0058 14.118C35.2402 13.8833 35.5917 13.2968 35.8261 12.9448C36.1776 12.4756 35.9433 12.2409 35.5917 12.1236C34.6543 11.889 32.8966 11.4197 32.0764 11.0678C31.2561 10.7158 31.2561 9.89463 29.85 9.89463C28.9125 9.89463 28.0923 10.1293 27.8579 10.2466C26.8033 8.8388 26.5689 10.3639 25.5143 11.4197C23.991 13.0621 24.2254 13.5314 26.9205 12.9448C26.5689 14.0007 26.2174 14.5872 25.6315 15.1738C24.6941 16.3469 23.7566 16.1123 22.702 16.6989C21.7646 17.2855 16.9602 16.9335 14.3823 17.6374C14.3823 17.6374 11.9215 16.6989 10.7497 13.766C9.57794 11.1851 10.1638 8.13491 10.1638 8.0176C10.0467 7.90028 9.22641 8.36954 8.75769 9.42537C8.17179 10.5985 8.28897 11.1851 7.93743 11.3024C7.5859 11.4197 7.35154 10.9505 7.11718 10.9505C7 10.9505 6.88282 11.6544 7.23436 12.4756C7.46872 13.1794 8.17179 13.6487 8.05461 14.0007C7.82025 14.3526 7.35154 14.0007 7.23436 14.118C7.11718 14.3526 7.70308 14.9392 8.28897 15.4084C8.87487 15.7604 9.57794 15.995 9.57794 16.3469C9.57794 16.6989 8.99205 16.4643 8.87487 16.6989C8.87487 16.9335 10.1638 17.6374 11.4528 17.9894C13.0933 18.3413 12.9761 18.5759 12.7418 19.1625C11.57 21.7434 10.281 28.7823 9.57794 32.3018C9.46077 32.8883 9.92948 33.2403 10.281 33.2403Z"
      fill="white"
    />
  </svg>
);
