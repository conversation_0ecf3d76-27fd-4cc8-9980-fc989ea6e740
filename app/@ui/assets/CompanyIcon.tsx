import { cn } from "~/@shadcn/utils";

export const CompanyIcon = ({
  className,
  ...props
}: React.ComponentProps<"svg">) => (
  <svg
    className={cn("h-9 w-9 p-1.5", className)}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12.7055 22.7532L20.3829 20.0537L16.7364 16.4371L21.7127 14.6649C22.8191 14.2862 23.6476 13.3357 23.9021 12.1971L23.9229 12.0503C24.1774 10.8474 23.7956 9.64698 22.871 8.83557C22.8191 8.78405 14.6405 0.695679 14.6405 0.695679L0 5.13398L4.5607 10.1647L3.47506 10.376C2.94263 10.4816 2.47514 10.734 2.11413 11.0921C1.81545 11.3883 1.59209 11.7567 1.46483 12.1791C1.16615 13.1064 1.43366 14.1085 2.12451 14.7937C2.12451 14.7937 8.98114 21.8285 9.29021 22.0912C10.2356 22.9233 11.5134 23.1783 12.7029 22.7558L12.7055 22.7532ZM16.4066 19.147L11.9731 20.7183C11.5472 20.8651 11.0901 20.7698 10.7498 20.4761C10.4096 20.1799 10.2616 19.7368 10.3681 19.2938C10.4642 18.8816 10.7498 18.5545 11.1446 18.418L14.482 17.2382L16.4066 19.147ZM3.9036 6.19783L14.0353 3.15053L19.17 8.24311L8.81492 11.6176L3.9036 6.19783ZM3.66985 13.2636C3.49844 13.0936 3.52181 12.9055 3.55298 12.8102C3.58415 12.7149 3.66985 12.5475 3.89321 12.4934L6.26446 12.04L8.13445 14.1059L20.2115 10.1828C20.6894 10.0256 21.0504 10.1725 21.2633 10.3193L21.614 10.667C21.762 10.8783 21.8789 11.1848 21.7854 11.5944L21.7646 11.7412C21.6685 12.1533 21.3724 12.4908 20.9776 12.6273L10.4096 16.3804C9.57071 16.687 8.90063 17.2871 8.51624 18.0677L3.66726 13.2584L3.66985 13.2636Z"
      fill="#4F6DDB"
    />
  </svg>
);
