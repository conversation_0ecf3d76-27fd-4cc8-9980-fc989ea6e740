export const CompanyLogo = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="192"
    height="64"
    viewBox="0 0 192 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M110.983 37.1045V40.6049H92V37.8389L105.102 21.8361H92.2232V18.3357H110.57V21.1016L97.469 37.1045H110.983Z"
      fill="white"
    />
    <path
      d="M128.377 33.3851H115.052C115.284 34.6349 115.894 35.627 116.881 36.3615C117.868 37.0916 119.092 37.461 120.555 37.461C122.419 37.461 123.96 36.8468 125.166 35.6142L127.295 38.0623C126.531 38.9728 125.565 39.6643 124.402 40.1281C123.234 40.5963 121.921 40.8282 120.457 40.8282C118.589 40.8282 116.95 40.4588 115.529 39.7158C114.108 38.9728 113.013 37.9377 112.236 36.6149C111.463 35.2877 111.077 33.7888 111.077 32.1138C111.077 30.4388 111.455 28.9699 112.206 27.6428C112.957 26.3157 114 25.2849 115.34 24.5419C116.675 23.7988 118.182 23.4295 119.856 23.4295C121.53 23.4295 122.989 23.7945 124.29 24.529C125.595 25.2591 126.613 26.2899 127.342 27.617C128.072 28.9442 128.441 30.4731 128.441 32.2126C128.441 32.4875 128.42 32.8826 128.377 33.3894V33.3851ZM116.628 27.7373C115.748 28.4889 115.211 29.4939 115.022 30.7437H124.655C124.483 29.5154 123.964 28.5189 123.097 27.7545C122.225 26.99 121.148 26.6077 119.856 26.6077C118.564 26.6077 117.508 26.9857 116.628 27.7373Z"
      fill="white"
    />
    <path
      d="M144.513 24.5075C145.818 25.229 146.84 26.2469 147.583 27.5612C148.325 28.8754 148.695 30.3915 148.695 32.1095C148.695 33.8275 148.325 35.3479 147.583 36.675C146.84 38.0021 145.818 39.0243 144.513 39.7459C143.208 40.4674 141.732 40.8282 140.079 40.8282C137.791 40.8282 135.979 40.0637 134.64 38.539V46.7767H130.665V23.6185H134.447V25.8475C135.104 25.0401 135.915 24.4388 136.881 24.0351C137.847 23.6313 138.911 23.4295 140.075 23.4295C141.727 23.4295 143.208 23.7902 144.509 24.5118L144.513 24.5075ZM143.243 35.962C144.187 34.9871 144.659 33.7029 144.659 32.1138C144.659 30.5247 144.187 29.2405 143.243 28.2656C142.298 27.2906 141.096 26.801 139.633 26.801C138.68 26.801 137.821 27.02 137.057 27.4538C136.293 27.8876 135.688 28.5104 135.245 29.3135C134.799 30.121 134.576 31.053 134.576 32.1138C134.576 33.1746 134.799 34.1066 135.245 34.9141C135.692 35.7215 136.293 36.34 137.057 36.7738C137.821 37.2076 138.68 37.4266 139.633 37.4266C141.096 37.4266 142.298 36.9413 143.243 35.962Z"
      fill="white"
    />
    <path d="M150.888 17H154.863V40.6049H150.888V17Z" fill="white" />
    <path
      d="M174.387 23.6185L166.437 41.975C165.694 43.8218 164.792 45.1188 163.732 45.8704C162.672 46.6221 161.388 47 159.886 47C159.036 47 158.199 46.8626 157.375 46.5877C156.546 46.3128 155.868 45.9306 155.34 45.4409L156.928 42.5161C157.31 42.8769 157.761 43.1646 158.28 43.3751C158.8 43.5856 159.323 43.6929 159.856 43.6929C160.556 43.6929 161.131 43.5125 161.59 43.1518C162.045 42.791 162.466 42.1854 162.848 41.3393L163.135 40.6693L155.726 23.6185H159.86L165.2 36.1854L170.575 23.6185H174.391H174.387Z"
      fill="white"
    />
    <path
      d="M190.06 25.302C191.352 26.5519 192 28.4073 192 30.8683V40.6048H188.025V31.3794C188.025 29.8933 187.677 28.7766 186.977 28.025C186.278 27.2734 185.282 26.8955 183.99 26.8955C182.526 26.8955 181.371 27.3378 180.525 28.214C179.675 29.0945 179.255 30.3615 179.255 32.015V40.6048H175.28V23.6184H179.062V25.8131C179.718 25.0272 180.547 24.4345 181.543 24.0307C182.539 23.627 183.663 23.4252 184.913 23.4252C187.055 23.4252 188.772 24.0522 190.064 25.302H190.06Z"
      fill="white"
    />
    <path
      d="M36.5284 63.4155L58.6008 55.6543L48.1172 45.2566L62.4239 40.1614C65.6048 39.0728 67.9868 36.34 68.7185 33.0667L68.7783 32.6446C69.51 29.1861 68.4124 25.735 65.7542 23.4022C65.6048 23.2541 42.0913 0 42.0913 0L0 12.7601L13.112 27.2236L9.9908 27.8308C8.46007 28.1345 7.11602 28.8602 6.07811 29.8896C5.21941 30.7413 4.57725 31.8003 4.21137 33.0149C3.35267 35.6809 4.12176 38.5618 6.10798 40.5317C6.10798 40.5317 25.8208 60.7568 26.7094 61.5122C29.4273 63.9043 33.1011 64.6375 36.521 63.4229L36.5284 63.4155ZM47.1689 53.0474L34.4227 57.565C33.1982 57.9871 31.884 57.7131 30.9058 56.8688C29.9276 56.0171 29.502 54.7434 29.8082 53.4696C30.0844 52.2846 30.9058 51.3441 32.0408 50.9516L41.6358 47.5598L47.1689 53.0474ZM11.2229 15.8187L40.3515 7.05769L55.1137 21.6989L25.3429 31.4004L11.2229 15.8187ZM10.5508 36.1327C10.058 35.6439 10.1252 35.1033 10.2148 34.8293C10.3044 34.5553 10.5508 34.0739 11.193 33.9184L18.0103 32.615L23.3866 38.5544L58.108 27.2754C59.4819 26.8237 60.5198 27.2458 61.1321 27.6679L62.1401 28.6677C62.5658 29.275 62.9018 30.1562 62.633 31.3338L62.5732 31.7559C62.2969 32.9408 61.4457 33.911 60.3107 34.3035L29.9276 45.0937C27.5158 45.9749 25.5893 47.7005 24.4842 49.9444L10.5434 36.1179L10.5508 36.1327Z"
      fill="#4F6DDB"
    />
  </svg>
);
