export const WebexIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 48 48"
    width="150px"
    height="150px"
    baseProfile="basic"
  >
    <linearGradient
      id="HSqeWdHcIFy332cgnzQtda"
      x1="25.503"
      x2=".665"
      y1="26.5"
      y2="26.5"
      gradientUnits="userSpaceOnUse"
    >
      <stop offset="0" stopColor="#0c63ad" />
      <stop offset="1" stopColor="#50e6ff" />
    </linearGradient>
    <path
      fill="url(#HSqeWdHcIFy332cgnzQtda)"
      d="M26.64,30.93l-2.5,5.55c-0.97,1.25-2.23,2.26-3.66,2.93c-1.43,0.7-2.99,1.08-4.58,1.12	c-6.6,0.13-9.67-5.81-9.67-5.81c-3.73-6.15-4.84-14.26-5.1-16.87c-0.07-0.3-0.12-0.61-0.13-0.92c-0.01-0.8,0.21-1.6,0.66-2.28	c0.38-0.7,0.96-1.27,1.66-1.66c0.75-0.36,1.58-0.54,2.42-0.52c0.78,0.03,1.55,0.25,2.22,0.65c0.51,0.31,0.95,0.71,1.31,1.18	c0.54,0.91,0.82,1.95,0.83,3.01c0.26,2.32,0.72,4.61,1.37,6.86c0.5,1.79,1.2,3.52,2.09,5.16c0.33,0.75,0.85,1.4,1.5,1.89l0.4,0.2	c1.05,0.2,1.63-0.66,2.03-1.37c0.52-1.24,0.98-2.5,1.37-3.79c0.06-0.26,0.13-0.46,0.2-0.72c0.13-0.45,0.24-0.91,0.32-1.37l2.1-5.63	L26.64,30.93z"
    />
    <radialGradient
      id="HSqeWdHcIFy332cgnzQtdb"
      cx="26.403"
      cy="22.574"
      r="21.814"
      gradientTransform="matrix(1 0 0 1.1255 0 -2.832)"
      gradientUnits="userSpaceOnUse"
    >
      <stop offset="0" stopColor="#185c37" />
      <stop offset=".661" stopColor="#30dc80" />
      <stop offset="1" stopColor="#5eee5c" />
    </radialGradient>
    <path
      fill="url(#HSqeWdHcIFy332cgnzQtdb)"
      d="M32.255,7.471c-1.604,0.011-3.187,0.368-4.64,1.046c-1.445,0.722-2.699,1.774-3.661,3.071	l-2.939,7.903l5.753,11.049l1.959-6.276l0.191-0.98c0.386-1.654,0.911-3.273,1.569-4.84c0.208-0.646,0.594-1.222,1.112-1.66	l0.066-0.066c0.473-0.233,1.04-0.157,1.436,0.191c0.245,0.17,0.447,0.394,0.589,0.656c0.199,0.257,0.324,0.523,0.523,0.78	c0.317,0.532,0.594,1.087,0.83,1.66c0.218,0.465,0.476,0.909,0.772,1.328c0.037,0,0.066,0.03,0.066,0.066	c0.447,0.483,0.979,0.879,1.569,1.17c0.58,0.314,1.233,0.472,1.893,0.457c0.604-0.005,1.202-0.114,1.768-0.324	c0.566-0.213,1.079-0.547,1.503-0.98c0.384-0.461,0.713-0.965,0.98-1.503c0.199-0.569,0.308-1.166,0.324-1.768	c0.001-0.651-0.132-1.295-0.39-1.893c0,0-1.768-4.574-4.84-6.865C36.853,8.256,34.588,7.473,32.255,7.471z"
    />
    <linearGradient
      id="HSqeWdHcIFy332cgnzQtdc"
      x1="1.591"
      x2="52.822"
      y1="27.9"
      y2="18.867"
      gradientUnits="userSpaceOnUse"
    >
      <stop offset="0" stopColor="#103f8d" />
      <stop offset=".494" stopColor="#2f64f7" />
      <stop offset="1" stopColor="#11408b" />
    </linearGradient>
    <path
      fill="url(#HSqeWdHcIFy332cgnzQtdc)"
      d="M15.063,7.471c-0.393,0.02-0.783,0.064-1.17,0.133c-2.979,0.603-5.578,2.404-7.189,4.981	c0.438,0.105,0.856,0.282,1.237,0.523c0.511,0.303,0.956,0.703,1.312,1.179c0.54,0.91,0.826,1.947,0.83,3.005	c0.274,2.27,0.713,4.517,1.312,6.724c0.625-2.424,1.664-4.721,3.071-6.79c0.164-0.246,0.387-0.449,0.647-0.589	c0.139-0.082,0.296-0.128,0.457-0.133c0.276-0.005,0.548,0.064,0.789,0.199c0.066,0,0.066,0.066,0.133,0.066	c0.21,0.201,0.404,0.417,0.581,0.647c0.347,0.636,0.655,1.293,0.921,1.967c0.523,1.436,0.98,3.262,0.98,3.262l1.303,5.23	c0.456,1.807,1.047,3.577,1.768,5.296c2.026,4.64,5.097,5.944,5.097,5.944c1.517,0.789,3.189,1.234,4.898,1.303	c1.627,0.077,3.246-0.26,4.707-0.98c1.603-0.801,3.017-1.933,4.151-3.32c0.683-0.932,1.293-1.914,1.826-2.939	c1.225-2.413,2.169-4.959,2.814-7.587c0.448-1.638,0.797-3.301,1.046-4.981c0.133-0.83,0.257-1.66,0.324-2.49	c0.144-0.779,0.121-1.58-0.066-2.349c-0.058-0.133-0.058-0.266-0.125-0.398c-0.398-0.968-1.112-1.772-2.026-2.283	c-0.657-0.441-1.434-0.669-2.225-0.656c-0.374,0.007-0.747,0.052-1.112,0.133c0.845,1.233,1.567,2.545,2.158,3.918	c0.258,0.598,0.391,1.242,0.39,1.893c-0.016,0.603-0.125,1.199-0.324,1.768c-0.267,0.537-0.595,1.042-0.98,1.503	c-0.404,0.456-0.923,0.794-1.503,0.98c-0.566,0.212-1.164,0.324-1.768,0.332c-0.809,0.015-1.605-0.214-2.283-0.656l-0.465,1.76	c-0.349,1.353-0.83,2.668-1.436,3.926c-0.324,0.772-0.715,1.514-1.17,2.216c-0.298,0.484-0.73,0.871-1.245,1.112	c-0.191,0.055-0.391,0.077-0.589,0.066c-0.212-0.005-0.418-0.075-0.589-0.199c-0.345-0.289-0.627-0.645-0.83-1.046	c-0.264-0.431-0.502-0.877-0.714-1.336c-0.39-0.98-1.046-3.395-1.046-3.395l-0.614-2.582l-0.656-2.548l-0.457-1.702	c-0.55-2.337-1.526-4.551-2.881-6.533c-1.318-1.877-3.19-3.293-5.354-4.051c-0.851-0.307-1.744-0.484-2.648-0.523H15.063z"
    />
  </svg>
);
