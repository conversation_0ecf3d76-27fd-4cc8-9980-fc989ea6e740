import React, { useState, useEffect, ChangeEvent } from "react";

interface MicrophoneSelectorProps {
  onDeviceChange: (deviceId: string) => void;
  selectedDevice: string;
  disabled?: boolean;
}

const MicrophoneSelector: React.FC<MicrophoneSelectorProps> = ({
  onDeviceChange,
  selectedDevice,
  disabled,
}) => {
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);

  useEffect(() => {
    const getDevices = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDevices = devices.filter(
        (device) => device.kind === "audioinput"
      );
      setDevices(audioDevices);
      if (audioDevices[0]) {
        onDeviceChange(audioDevices[0].deviceId);
      }
    };
    getDevices();
  }, [onDeviceChange]);

  const handleChange = (event: ChangeEvent<HTMLSelectElement>) => {
    onDeviceChange(event.target.value);
  };

  return (
    <div className="w-full">
      <label htmlFor="microphone">Select Microphone: </label>
      <select
        id="microphone"
        value={selectedDevice}
        onChange={handleChange}
        disabled={disabled}
      >
        {devices.map((device) => (
          <option key={device.deviceId} value={device.deviceId}>
            {device.label || `Microphone ${device.deviceId}`}
          </option>
        ))}
      </select>
    </div>
  );
};

export default MicrophoneSelector;
