import * as React from "react";
import { cn } from "~/@shadcn/utils";
import { Button } from "~/@shadcn/ui/button";

export type FabProps = Omit<React.ComponentProps<typeof Button>, "size"> & {
  size?: "icon-sm" | "icon" | "icon-lg";
};

export const Fab = React.forwardRef<HTMLButtonElement, FabProps>(
  ({ className, size = "icon", ...props }, ref) => (
    <Button
      ref={ref}
      size={size}
      className={cn("rounded-full", className)}
      {...props}
    />
  )
);
Fab.displayName = "Fab";
