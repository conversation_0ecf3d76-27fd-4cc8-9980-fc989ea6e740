import React from "react";

import { ProcessingStatus } from "~/api/openapi/generated";
import ScheduledCard from "./ScheduledCard";

const OnboardingScheduledCard = () => {
  return (
    <ScheduledCard
      toPrep={{ pathname: "/notes/create/intro_mock_uuid" }}
      toMeeting={{ pathname: "/notes/create/intro_mock_uuid" }}
      meetingName="Zeplyn Demo Meeting"
      scheduledStartTime={new Date()}
      attendees={[
        {
          uuid: "qwerty",
          name: "Demo User",
        },
      ]}
      active
      status={ProcessingStatus.Scheduled}
      id="onboarding-scheduled-card"
      prepButtonId="onboarding-prep-cta"
    />
  );
};

export default OnboardingScheduledCard;
