import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";
import { useUserAgent } from "~/context/userAgent";

const MeetingTabs = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  const { isMobile } = useUserAgent();

  return (
    <Tabs
      value={value}
      onValueChange={onChange}
      className="self-start"
      data-onboarding="meeting-tabs"
    >
      <TabsList>
        {tabs(!isMobile).map(({ label, value }) => (
          <TabsTrigger key={value} value={value}>
            {label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
};

const tabs = (isDesktop: boolean) => [
  {
    label: isDesktop ? "Today's Meetings" : "Today",
    value: "today",
  },
  {
    label: isDesktop ? "Upcoming Meetings" : "Upcoming",
    value: "upcoming",
  },
  {
    label: isDesktop ? "Past Meetings" : "Past",
    value: "past",
  },
];

export default MeetingTabs;
