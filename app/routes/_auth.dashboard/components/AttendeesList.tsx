import { Link } from "react-router";
import { Users } from "lucide-react";

import { Attendee } from "~/utils/notesUtils";

export const AttendeesList = ({
  attendees,
  className,
}: {
  attendees: Attendee[];
  className: string;
}) => {
  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      <Users className="!h-5 !w-5" />
      <div className="flex flex-wrap gap-1">
        {attendees.map((attendee, index) => (
          <span key={attendee.uuid}>
            {attendee.type === "client" ? (
              <Link
                to={`/clients/${attendee.uuid}`}
                className="underline"
                target="_blank"
              >
                {attendee.name}
              </Link>
            ) : (
              attendee.name
            )}
            {index < attendees.length - 1 && ", "}
          </span>
        ))}
      </div>
    </div>
  );
};
