import { type MetaFunction } from "react-router";
import { useNavigate, useOutletContext } from "react-router";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import { useEffect } from "react";
import { Typography } from "~/@ui/Typography";
import { isBefore, isToday, isThisQuarter, addDays } from "date-fns";
import { ListTasksResponse, TaskResponse } from "~/api/openapi/generated";

type TasksContextType = { tasks: ListTasksResponse };

const groupTasksByTime = (tasks: ListTasksResponse) => {
  const pastDue: TaskResponse[] = [];
  const dueToday: TaskResponse[] = [];
  const dueThisWeek: TaskResponse[] = [];
  const dueThisQuarter: TaskResponse[] = [];
  const noDueDate: TaskResponse[] = [];

  const now = new Date();
  const endOfWeek = addDays(now, 7);
  tasks.data.forEach((task) => {
    if (!task.dueDate) {
      noDueDate.push(task);
      return;
    }

    const dueDate = new Date(task.dueDate);

    if (isBefore(dueDate, now)) {
      pastDue.push(task);
    } else if (isToday(dueDate)) {
      dueToday.push(task);
    } else if (isBefore(dueDate, endOfWeek)) {
      dueThisWeek.push(task);
    } else if (isThisQuarter(dueDate)) {
      dueThisQuarter.push(task);
    } else {
      dueThisQuarter.push(task);
    }
  });

  return { pastDue, dueToday, dueThisWeek, dueThisQuarter, noDueDate };
};

const getFirstTask = (tasks: ListTasksResponse): TaskResponse | undefined => {
  const { pastDue, dueToday, dueThisWeek, dueThisQuarter, noDueDate } =
    groupTasksByTime(tasks);

  return (
    dueToday[0] ||
    dueThisWeek[0] ||
    dueThisQuarter[0] ||
    pastDue[0] ||
    noDueDate[0]
  );
};

// Helpers
const useRedirectToFirstTaskOnDesktop = (tasks: ListTasksResponse) => {
  const navigate = useNavigate();
  const { matchedBreakpoints } = useTailwindBreakpoints();
  const redirectToFirstTask = matchedBreakpoints.has("lg");

  const firstTask = getFirstTask(tasks);

  useEffect(() => {
    if (firstTask && redirectToFirstTask) {
      navigate(`/tasks/${firstTask.uuid}`);
    }
  }, [navigate, firstTask, redirectToFirstTask]);

  return { firstTask };
};

// Exports
export const meta: MetaFunction = () => [
  { title: "Tasks" },
  { name: "description", content: "View all tasks" },
];

const Route = () => {
  // On desktop, we "pre-select" the first task by redirecting to /tasks/:firstTaskId

  const { tasks } = useOutletContext<TasksContextType>();
  const { firstTask } = useRedirectToFirstTaskOnDesktop(tasks);

  return (
    <SidebarV2 header={<HeaderV2 />}>
      <div className="flex flex-col gap-3 self-stretch px-5">
        <Typography
          className="my-8 text-center"
          color="secondary"
          variant="body2"
        >
          {firstTask ? "Loading task..." : "Select a task to view its details."}
        </Typography>
      </div>
    </SidebarV2>
  );
};
export default Route;
