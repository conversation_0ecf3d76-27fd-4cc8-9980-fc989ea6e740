import { type LoaderFunctionArgs, redirect } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, PublicApisApi } from "~/api/openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { html } from "remix-utils/responses";

export async function loader({ request }: LoaderFunctionArgs) {
  await getUserSessionOrRedirect(request);
  const configParams = new Configuration(
    await configurationParameters(request)
  );

  try {
    return html(
      await new PublicApisApi(configParams).publicApisSwaggerDocument()
    );
  } catch (error) {
    return redirect("/dashboard");
  }
}
