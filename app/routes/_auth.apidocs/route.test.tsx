import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, PublicApisApi } from "~/api/openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { loader } from "./route";

vi.mock("~/auth/authenticator.server");
vi.mock("~/api/openapi/configParams");
vi.mock("~/api/openapi/generated");
vi.mock("react-router-dom");

describe("loader", () => {
  const mockRequest = new Request("http://localhost:3000");
  const mockConfigParams = { apiKey: "test" };
  const mockSwaggerDoc = "<html>swagger doc</html>";

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return HTML response when API call succeeds", async () => {
    const mockPublicApisSwaggerDocument = vi
      .fn()
      .mockResolvedValue(mockSwaggerDoc);
    const mockPublicApisApi = {
      publicApisSwaggerDocument: mockPublicApisSwaggerDocument,
    };

    vi.mocked(getUserSessionOrRedirect);
    vi.mocked(configurationParameters).mockResolvedValue(mockConfigParams);
    vi.mocked(PublicApisApi).mockImplementation(() => mockPublicApisApi as any);

    const result = await loader({
      request: mockRequest,
      params: {},
      context: {},
    });

    expect(getUserSessionOrRedirect).toHaveBeenCalledWith(mockRequest);
    expect(configurationParameters).toHaveBeenCalledWith(mockRequest);
    expect(PublicApisApi).toHaveBeenCalledWith(expect.any(Configuration));
    expect(mockPublicApisSwaggerDocument).toHaveBeenCalled();
    expect(await result.text()).toBe(mockSwaggerDoc);
  });

  it("should redirect to dashboard when API call fails", async () => {
    const mockError = new Error("API error");
    const mockPublicApisSwaggerDocument = vi.fn().mockRejectedValue(mockError);
    const mockPublicApisApi = {
      publicApisSwaggerDocument: mockPublicApisSwaggerDocument,
    };

    vi.mocked(getUserSessionOrRedirect);
    vi.mocked(configurationParameters).mockResolvedValue(mockConfigParams);
    vi.mocked(PublicApisApi).mockImplementation(() => mockPublicApisApi as any);

    const response = await loader({
      request: mockRequest,
      params: {},
      context: {},
    });

    expect(response.status).toBe(302); // Redirect status code
    expect(response.headers.get("Location")).toBe("/dashboard");
  });
});
