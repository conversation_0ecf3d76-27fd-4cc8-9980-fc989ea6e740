import { Typography } from "~/@ui/Typography";
import { TaskCard } from "~/@ui/tasks/TaskCard";
import { TaskResponse } from "~/api/openapi/generated";
import { SerializeFrom } from "~/types/remix";

export const TasksTab = ({
  tasks,
}: {
  tasks: SerializeFrom<TaskResponse>[];
}) => {
  const incompleteTasks = tasks.filter((task) => !task.completed);
  const completedTasks = tasks.filter((task) => task.completed);

  // Sort the tasks by due date within their respective groups
  incompleteTasks.sort((a, b) => {
    const aDueDate = a.dueDate ? new Date(a.dueDate).getTime() : 0;
    const bDueDate = b.dueDate ? new Date(b.dueDate).getTime() : 0;
    return aDueDate - bDueDate;
  });

  completedTasks.sort((a, b) => {
    const aDueDate = a.dueDate ? new Date(a.dueDate).getTime() : 0;
    const bDueDate = b.dueDate ? new Date(b.dueDate).getTime() : 0;
    return aDueDate - bDueDate;
  });

  return (
    <>
      {tasks.length > 0 ? (
        <>
          {incompleteTasks.length > 0 && (
            <>
              {incompleteTasks.map((task) => (
                <TaskCard
                  key={task.uuid}
                  compact
                  dueAt={
                    task.dueDate ? new Date(task.dueDate).toISOString() : null
                  }
                  to={`/tasks/${task.uuid}`}
                  uuid={task.uuid}
                  completed={false}
                  title={task.title}
                />
              ))}
            </>
          )}

          {completedTasks.length > 0 && (
            <>
              {completedTasks.map((task) => (
                <TaskCard
                  key={task.uuid}
                  compact
                  dueAt={
                    task.dueDate ? new Date(task.dueDate).toISOString() : null
                  }
                  to={`/tasks/${task.uuid}`}
                  uuid={task.uuid}
                  completed={true}
                  title={task.title}
                />
              ))}
            </>
          )}
        </>
      ) : (
        <Typography className="mt-4 self-stretch text-center" color="secondary">
          No Action items available.
        </Typography>
      )}
    </>
  );
};
