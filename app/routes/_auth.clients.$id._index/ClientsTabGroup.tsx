import { useState, type ReactNode } from "react";
import { z } from "zod";
import { ToggleGroup, ToggleGroupItem } from "~/@shadcn/ui/toggle-group";
import { cn } from "~/@shadcn/utils";
import { useFlag } from "~/context/flags";

// Types
const TabStruct = z.enum(["client-recap", "tasks", "notes"]);
type Tab = z.infer<typeof TabStruct>;

// Fragments
type TabContentsProps = {
  activeTab: string;
  children: ReactNode;
  value: string;
};
const TabContents = ({ activeTab, children, value }: TabContentsProps) => (
  <div className={cn("flex flex-col gap-2", value !== activeTab && "hidden")}>
    {children}
  </div>
);

// Exports
export const ClientTabGroup = ({
  tasksTab,
  notesTab,
  clientRecapTab,
}: {
  tasksTab: ReactNode;
  notesTab: ReactNode;
  clientRecapTab: ReactNode;
}) => {
  const isMeetingPrepEnabled = useFlag("EnableMeetingPrepExperience");
  const [activeTab, setActiveTab] = useState<Tab>(
    isMeetingPrepEnabled ? "client-recap" : "tasks"
  );
  const DemoClientMockDataEnabled = useFlag("DemoClientMockDataEnabled");

  return (
    <>
      <ToggleGroup
        className="justify-start p-0"
        value={activeTab}
        type="single"
        onValueChange={(value) => {
          const nextTab = TabStruct.safeParse(value);
          if (nextTab.success) setActiveTab(nextTab.data);
        }}
      >
        {isMeetingPrepEnabled && (
          <ToggleGroupItem value="client-recap" aria-label="Show client recap">
            Client Recap
          </ToggleGroupItem>
        )}
        {DemoClientMockDataEnabled && (
          <ToggleGroupItem value="tasks" aria-label="Show tasks">
            Tasks
          </ToggleGroupItem>
        )}
        <ToggleGroupItem value="notes" aria-label="Show notes">
          Notes
        </ToggleGroupItem>
      </ToggleGroup>

      {isMeetingPrepEnabled && (
        <TabContents activeTab={activeTab} value="client-recap">
          {clientRecapTab}
        </TabContents>
      )}
      {DemoClientMockDataEnabled && (
        <TabContents activeTab={activeTab} value="tasks">
          {tasksTab}
        </TabContents>
      )}
      <TabContents activeTab={activeTab} value="notes">
        {notesTab}
      </TabContents>
    </>
  );
};
