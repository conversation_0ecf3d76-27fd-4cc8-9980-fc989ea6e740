import { Typography } from "~/@ui/Typography";
import { NoteCardTab } from "~/@ui/clients/NoteCardTab";
import { ListNotesResponse } from "~/api/openapi/generated";

// Exports
export const NotesTab = ({ notes }: { notes: ListNotesResponse[] }) => {
  const sortedNotes = notes.sort(
    (a, b) => new Date(b.created).getTime() - new Date(a.created).getTime()
  );

  return (
    <>
      {sortedNotes.length > 0 ? (
        sortedNotes.map((note) => (
          <NoteCardTab
            key={note.uuid}
            note={note}
            to={{
              pathname: `/notes/${note.uuid}`,
            }}
          />
        ))
      ) : (
        <Typography className="mt-4 self-stretch text-center" color="secondary">
          Nothing here... yet.
        </Typography>
      )}
    </>
  );
};
