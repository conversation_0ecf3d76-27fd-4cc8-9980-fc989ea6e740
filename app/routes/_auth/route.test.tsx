import { v4 as uuidv4 } from "uuid";
import { describe, it, expect, vi } from "vitest";

import { fetchFlags } from "~/api/flags/flagFetcher.server";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";

import Route, { loader } from "./route";
import { screen, waitFor } from "@testing-library/react";
import { data, useNavigate } from "react-router";
import { datadogRum } from "@datadog/browser-rum";
import { useFlag } from "~/context/flags";
import userEvent from "@testing-library/user-event";
import { Button } from "~/@shadcn/ui/button";
import { datadogLogs } from "@datadog/browser-logs";
import { renderRouter } from "~/utils/testUtils";

describe("loader", () => {
  beforeEach(() => {
    vi.mock("~/auth/authenticator.server");
    vi.mock("~/api/flags/flagFetcher.server");
    vi.mock("@datadog/browser-rum");
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("loads user session and flags", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
      orgId: uuidv4().toString(),
    };

    const flags = {
      flags: { flag: { is_active: true, last_modified: "" } },
      samples: {},
      switches: {},
    };

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);
    vi.mocked(fetchFlags).mockResolvedValue(flags);

    const request = new Request("http://localhost:3000");
    const result = await loader({ request, params: {}, context: {} });

    expect(result).toEqual({
      appId: process.env.ZEPLYN_INTERCOM_WIDGET_ID,
      userSession,
      flags,
      userAgent: "",
      uiSettings: {},
    });
  });

  it("throws error if Intercom app ID is missing", async () => {
    vi.mocked(fetchFlags).mockResolvedValue({
      flags: {},
      samples: {},
      switches: {},
    });

    delete process.env.ZEPLYN_INTERCOM_WIDGET_ID;

    const request = new Request("http://localhost:3000");

    await expect(loader({ request, params: {}, context: {} })).rejects.toThrow(
      "Missing Intercom app ID"
    );
  });
});

describe("Route", () => {
  const userSession = {
    accessToken: "accessToken",
    email: "email",
    firstName: "firstName",
    lastName: "lastName",
    userId: uuidv4().toString(),
    orgId: uuidv4().toString(),
  };

  beforeEach(() => {
    vi.stubGlobal("Intercom", vi.fn());
    vi.mock("@datadog/browser-rum");
    vi.mock("@datadog/browser-logs");
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("passes flag information via a context provider", async () => {
    const FlagsTestComponent = () => {
      const isActive = useFlag("testFlag");
      return (
        <div>
          {isActive === undefined
            ? "Undefined"
            : isActive
              ? "Is active"
              : "Not active"}
        </div>
      );
    };

    const routes = [
      {
        Component: Route,
        hydrateFallbackElement: <div>Loading...</div>,
        loader() {
          return data({
            appId: "appId",
            userSession: userSession,
            flags: {
              flags: { testFlag: { is_active: true, last_modified: "" } },
              samples: {},
              switches: {},
            },
          });
        },
        children: [
          {
            path: "/",
            Component: FlagsTestComponent,
          },
        ],
      },
    ];

    renderRouter(routes);

    await screen.findByText("Is active");
  });

  it("sets the Datadog user correctly", async () => {
    const routes = [
      {
        Component: Route,
        hydrateFallbackElement: <div>Loading...</div>,
        loader() {
          return data({
            appId: "appId",
            userSession: userSession,
            flags: {
              flags: { testFlag: { is_active: true, last_modified: "" } },
              samples: {},
              switches: {},
            },
          });
        },
        children: [
          {
            path: "/",
            Component() {
              return <div>Hello</div>;
            },
          },
        ],
      },
    ];

    renderRouter(routes);

    await waitFor(() => {
      expect(datadogRum.setUser).toHaveBeenCalledWith({
        id: userSession.userId,
        organization_id: userSession.orgId,
      });
    });
    expect(datadogLogs.setUser).toHaveBeenCalledWith({
      id: userSession.userId,
      organization_id: userSession.orgId,
    });
  });

  it("updates the Datadog user correctly", async () => {
    const updatedUserSession = {
      ...userSession,
      userId: uuidv4().toString(),
      orgId: uuidv4().toString(),
    };

    const mockLoader = vi.fn();
    mockLoader.mockReturnValueOnce(
      data({
        appId: "appId",
        userSession: userSession,
        flags: {
          flags: { testFlag: { is_active: true, last_modified: "" } },
          samples: {},
          switches: {},
        },
      })
    );

    const routes = [
      {
        Component: Route,
        hydrateFallbackElement: <div>Loading...</div>,
        loader: mockLoader,
        children: [
          {
            path: "/",
            Component() {
              // Trigger a revalidation.
              const navigate = useNavigate();
              return (
                <Button onClick={(e) => navigate(".", { replace: true })}>
                  Revalidate
                </Button>
              );
            },
          },
        ],
      },
    ];

    renderRouter(routes);
    const user = userEvent.setup();
    const link = await screen.findByText("Revalidate");

    mockLoader.mockImplementation(() =>
      data({
        appId: "appId",
        userSession: updatedUserSession,
        flags: {
          flags: { testFlag: { is_active: true, last_modified: "" } },
          samples: {},
          switches: {},
        },
      })
    );

    await user.click(link);

    expect(datadogRum.setUser).toBeCalledWith({
      id: updatedUserSession.userId,
      organization_id: updatedUserSession.orgId,
    });
    expect(datadogRum.setUser).toBeCalledTimes(2);

    expect(datadogLogs.setUser).toHaveBeenCalledWith({
      id: userSession.userId,
      organization_id: userSession.orgId,
    });
    expect(datadogLogs.setUser).toBeCalledTimes(2);

    // If the data hasn't changed, the user should not be updated.
    await user.click(link);
    expect(datadogRum.setUser).toBeCalledTimes(2);
    expect(datadogLogs.setUser).toBeCalledTimes(2);
  });

  it("sets Intercom values properly", async () => {
    const mockIntercom = vi.fn();
    vi.stubGlobal("Intercom", mockIntercom);

    const routes = [
      {
        Component: Route,
        hydrateFallbackElement: <div>Loading...</div>,
        loader() {
          return data({
            appId: "appId",
            userSession: userSession,
            flags: {
              flags: { testFlag: { is_active: true, last_modified: "" } },
              samples: {},
              switches: {},
            },
          });
        },
        children: [
          {
            path: "/",
            Component() {
              return <div>Hello</div>;
            },
          },
        ],
      },
    ];

    renderRouter(routes);

    await waitFor(() =>
      expect(mockIntercom).toBeCalledWith("boot", {
        api_base: "https://api-iam.intercom.io",
        app_id: "appId",
        custom_launcher_selector: "#zeplyn-intercom-launcher",
        hide_default_launcher: false,
        name: `${userSession.firstName} ${userSession.lastName}`,
        email: userSession.email,
        user_id: userSession.userId,
      })
    );
  });
});
