# Routes

```
/                   # Home, redirects to /notes

/auth               # Redirects to /auth/login
  /login            # Login flow
  /logout           # Log the user out and immediately redirect to /auth/login
  /forgot-password  # Initiate password flow
  /reset-password   # Email password reset redirect route
  /dashboard        # View advisor hub / landing page

/notes              # View all notes
  /?q=:searchTerm   # Search within list of tasks
  /create           # Record new note
  /:id              # View note details

/tasks              # View all tasks
  /?q=:searchTerm   # Search within list of tasks
  /create           # Create new task
  /:id              # View task details

/settings/*         # App, user, integration settings
```
