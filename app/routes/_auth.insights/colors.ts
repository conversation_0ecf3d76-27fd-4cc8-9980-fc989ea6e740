import { ChartData } from "chart.js";

const darkSlateBlue = "#003F5C";
const amberYellow = "#FFA600";
const lighterSlateBlue = "#444E86";
const fuschia = "#DD5182";
const orange = "#FF6E54";
const purple = "#955196";
const pinkishRed = "#F95D6A";

// Colors to use when drawing bar charts.
const barColors = [
  darkSlateBlue,
  lighterSlateBlue,
  purple,
  fuschia,
  orange,
  amberYellow,
];

// Colors to use when drawing line and pie charts.
const pieAndLineColors = [
  darkSlateBlue,
  pinkishRed,
  amberYellow,
  lighterSlateBlue,
  fuschia,
  orange,
];

// Add bar colors to the chart data
export const withBarColors = (data: ChartData<"bar">) => {
  return {
    ...data,
    datasets: data.datasets.map((dataset) => ({
      ...dataset,
      backgroundColor: barColors,
      borderWidth: 0,
    })),
  };
};

// Add line colors to the chart data
export const withLineColors = (data: ChartData<"line">) => {
  return {
    ...data,
    datasets: data.datasets.map((dataset, i) => ({
      ...dataset,
      backgroundColor: pieAndLineColors[i % pieAndLineColors.length],
      borderColor: pieAndLineColors[i % pieAndLineColors.length],
      borderWidth: 1,
    })),
  };
};

// Add pie colors to the chart data
export const withPieColors = (data: ChartData<"pie">) => {
  return {
    ...data,
    datasets: data.datasets.map((dataset, i) => ({
      ...dataset,
      backgroundColor: pieAndLineColors,
      borderWidth: 0,
    })),
  };
};
