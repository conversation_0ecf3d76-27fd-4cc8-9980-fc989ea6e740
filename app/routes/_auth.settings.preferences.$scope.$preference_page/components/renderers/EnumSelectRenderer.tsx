import React from "react";
import {
  ControlProps,
  isEnumControl,
  isOneOfEnumControl,
  or,
  rankWith,
} from "@jsonforms/core";
import { withJsonFormsControlProps } from "@jsonforms/react";
import { CircleHelp } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "~/@shadcn/ui/select";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

/**
 * Renderer for enum controls using ShadCN Select component
 */
const EnumSelectRenderer = (props: ControlProps) => {
  const {
    data,
    handleChange,
    path,
    label,
    schema,
    required,
    id,
    enabled,
    errors,
    uischema,
  } = props;

  const hasDescription = schema.description && schema.description.length > 0;
  let options: string[] = [];
  let labels: string[] = [];
  if (schema.enum) {
    options = schema.enum;
    labels = uischema.options?.enumLabels || options;
  } else if (schema.oneOf) {
    options = schema.oneOf.map((option) => option.const);
    labels = schema.oneOf.map((option) => option.title || option.const);
  }

  // The ShadCN select component does not support an option whose value is an empty string,
  // so we replace the empty string with a special placeholder value "__default__".
  const optionToValue = (option: string) => {
    return option === "" ? "__default__" : option;
  };
  const valueToOption = (value: string) => {
    return value === "__default__" ? "" : value;
  };

  const onChange = (value: string) => {
    handleChange(path, valueToOption(value));
  };

  return (
    <div className="mt-6 flex flex-col">
      <div className="mb-2 flex items-center gap-2">
        <label htmlFor={id} className="font-medium">
          {label}
          {required && <span className="text-red-500">*</span>}
        </label>
        {hasDescription && (
          <Tooltip>
            <TooltipTrigger asChild>
              <CircleHelp
                className="h-4 w-4 cursor-help text-gray-400"
                data-testid="descriptionIcon"
              />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs text-sm">
              {schema.description}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
      <Select
        onValueChange={onChange}
        value={optionToValue(data) || ""}
        disabled={!enabled}
      >
        <SelectTrigger className="mt-1 w-80" id={id}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {options.map((option: string, index: number) => {
              const value = optionToValue(option);
              return (
                <SelectItem key={value} value={optionToValue(value)}>
                  {labels[index] || option}
                </SelectItem>
              );
            })}
          </SelectGroup>
        </SelectContent>
      </Select>
      {errors && (
        <div className="mt-2 text-xs font-normal text-red-500">{errors}</div>
      )}
    </div>
  );
};

export const enumSelectRendererTester = rankWith(
  20,
  or(isEnumControl, isOneOfEnumControl)
);

export const testable = {
  EnumSelectRenderer,
};

export default withJsonFormsControlProps(EnumSelectRenderer);
