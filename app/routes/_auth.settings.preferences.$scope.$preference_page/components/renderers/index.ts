import BooleanSwitchRenderer, {
  booleanSwitchRendererTester,
} from "./BooleanSwitchRenderer";
import StringTextFieldRenderer, {
  stringTextFieldRendererTester,
} from "./StringTextFieldRenderer";
import EnumSelectRenderer, {
  enumSelectRendererTester,
} from "./EnumSelectRenderer";
import {
  JsonFormsCellRendererRegistryEntry,
  JsonFormsRendererRegistryEntry,
} from "@jsonforms/core";
import Base64JPEGRenderer, {
  base64JPEGRendererTester,
} from "./Base64JPEGRenderer";
import TableArrayRenderer, {
  tableArrayControlTester,
} from "./ArrayTableRenderer";
import StringTextFieldCell, {
  stringTextFieldCellTester,
} from "./StringTextFieldCell";

/**
 * Custom renderers for JSONForms
 *
 * - BooleanSwitchRenderer: uses ShadCN Switch component for boolean fields
 * - StringTextFieldRenderer: uses styled input with tooltip for string fields
 * - EnumSelectRenderer: uses ShadCN Select dropdown for enum fields
 * - Base64JPEGRenderer: handles base64 JPEG image upload and display
 */
export const customRenderers: JsonFormsRendererRegistryEntry[] = [
  { tester: booleanSwitchRendererTester, renderer: BooleanSwitchRenderer },
  { tester: stringTextFieldRendererTester, renderer: StringTextFieldRenderer },
  { tester: enumSelectRendererTester, renderer: EnumSelectRenderer },
  { tester: base64JPEGRendererTester, renderer: Base64JPEGRenderer },
  { tester: tableArrayControlTester, renderer: TableArrayRenderer },
];

/**
 * Custom cell renderers for JSONForms
 */
export const customCells: JsonFormsCellRendererRegistryEntry[] = [
  {
    tester: stringTextFieldCellTester,
    cell: StringTextFieldCell,
  },
];
