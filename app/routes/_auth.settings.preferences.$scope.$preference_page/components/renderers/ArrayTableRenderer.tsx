// This code is based on
// https://github.com/eclipsesource/jsonforms/blob/8cdd5ef695886550442741331bf85b83dd5a27c0/packages/vanilla-renderers/src/complex/TableArrayControl.tsx
// Its original license is included below.

/*
    The MIT License
    
    Copyright (c) 2017-2019 EclipseSource Munich
    https://github.com/eclipsesource/jsonforms
    
    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:
    
    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.
    
    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
    THE SOFTWARE.
*/
import React from "react";
import {
  ArrayControlProps,
  ControlElement,
  createDefaultValue,
  Paths,
  RankedTester,
  Resolve,
  Test,
  encode,
  ArrayTranslations,
} from "@jsonforms/core";
import {
  DispatchCell,
  withArrayTranslationProps,
  withJsonFormsArrayControlProps,
  withTranslateProps,
} from "@jsonforms/react";
import {
  VanillaRendererProps,
  withVanillaControlProps,
} from "@jsonforms/vanilla-renderers";
import { Button } from "~/@shadcn/ui/button";
import { CircleHelp, Plus, Trash2 } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

const { or, isObjectArrayControl, isPrimitiveArrayControl, rankWith } = Test;

/**
 * Alternative tester for an array that also checks whether the 'table'
 * option is set.
 * @type {RankedTester}
 */
export const tableArrayControlTester: RankedTester = rankWith(
  10,
  or(isObjectArrayControl, isPrimitiveArrayControl)
);

class TableArrayControl extends React.Component<
  ArrayControlProps &
    VanillaRendererProps & { translations: ArrayTranslations },
  any
> {
  confirmDelete = (path: string, index: number) => {
    const p = path.substring(0, path.lastIndexOf("."));
    if (this.props.removeItems) {
      this.props.removeItems(p, [index])();
    }
  };

  render() {
    const {
      addItem,
      schema,
      arraySchema,
      rootSchema,
      path,
      data,
      visible,
      errors,
      label,
      translations,
      enabled,
    } = this.props;

    const createControlElement = (key?: string): ControlElement => ({
      type: "Control",
      label: false,
      scope: schema.type === "object" ? `#/properties/${key}` : "#",
    });

    const isValid = errors.length === 0;
    const hasDescription =
      arraySchema.description && arraySchema.description.length > 0;
    const properties = schema.properties || {};
    const propertyKeys = Object.keys(properties);
    const filteredPropertyKeys = propertyKeys.filter(
      (prop) => properties[prop]?.type !== "array"
    );

    return (
      <div className={`flex flex-col ${!visible ? "hidden" : ""}`}>
        <header className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <label className="font-medium">{label}</label>
            {hasDescription && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <CircleHelp className="h-4 w-4 cursor-help text-gray-400" />
                </TooltipTrigger>
                <TooltipContent className="max-w-xs text-sm">
                  {arraySchema.description}
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        </header>

        {!isValid && (
          <div className="mb-2 text-sm font-normal text-red-500">{errors}</div>
        )}

        <table className="w-full border-separate border-spacing-2">
          <tbody>
            {!data || !Array.isArray(data) || data.length === 0 ? (
              <tr>
                <td
                  colSpan={
                    propertyKeys.length > 0
                      ? filteredPropertyKeys.length + 2
                      : 3
                  }
                  className="text-center text-sm text-gray-500"
                >
                  Click the <strong>Add</strong> button to add a new item.
                </td>
              </tr>
            ) : (
              data.map((_child, index) => {
                const childPath = Paths.compose(path, `${index}`);

                return (
                  <tr key={childPath}>
                    {propertyKeys.length > 0 ? (
                      filteredPropertyKeys.map((prop) => {
                        const childPropPath = Paths.compose(
                          childPath,
                          prop.toString()
                        );
                        return (
                          <td key={childPropPath}>
                            <DispatchCell
                              schema={Resolve.schema(
                                schema,
                                `#/properties/${encode(prop)}`,
                                rootSchema
                              )}
                              uischema={createControlElement(encode(prop))}
                              path={childPath + "." + prop}
                            />
                          </td>
                        );
                      })
                    ) : (
                      <td key={Paths.compose(childPath, index.toString())}>
                        <DispatchCell
                          schema={schema}
                          uischema={createControlElement()}
                          path={childPath}
                        />
                      </td>
                    )}
                    <td className="flex justify-start">
                      <Button
                        variant="ghost-destructive"
                        size="icon-sm"
                        type="button"
                        disabled={!enabled}
                        aria-label={translations.removeAriaLabel}
                        onClick={() => {
                          if (
                            window.confirm(translations.deleteDialogMessage)
                          ) {
                            this.confirmDelete(childPath, index);
                          }
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                );
              })
            )}

            {/* Show the add button in the bottom row */}
            <tr key={"add-button-row"}>
              <td colSpan={3}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      disabled={!enabled}
                      onClick={
                        addItem &&
                        addItem(path, createDefaultValue(schema, rootSchema))
                      }
                      className="flex items-center"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{translations.addTooltip}</TooltipContent>
                </Tooltip>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }
}

export default withVanillaControlProps(
  withJsonFormsArrayControlProps(
    withTranslateProps(withArrayTranslationProps(TableArrayControl))
  )
);
