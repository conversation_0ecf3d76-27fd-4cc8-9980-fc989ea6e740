import React from "react";
import { CellProps, isStringControl, rankWith } from "@jsonforms/core";
import { withJsonFormsCellProps } from "@jsonforms/react";
import {
  VanillaRendererProps,
  withVanillaCellProps,
} from "@jsonforms/vanilla-renderers";

const StringTextFieldCell = (props: CellProps & VanillaRendererProps) => {
  const { data, handleChange, path, id, enabled, errors, uischema } = props;

  const placeholder = uischema.options?.placeholder || "";

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(path, e.target.value);
  };

  return (
    <div className="flex flex-col">
      <input
        type="text"
        id={id}
        value={data || ""}
        onChange={onChange}
        placeholder={placeholder}
        disabled={!enabled}
        className={`h-10 rounded-md border px-2 ${
          !enabled ? "opacity-50" : ""
        }`}
      />
      {errors && (
        <div className="mt-2 text-xs font-normal text-red-500">{errors}</div>
      )}
    </div>
  );
};

export const stringTextFieldCellTester = rankWith(10, isStringControl);

export default withJsonFormsCellProps(
  withVanillaCellProps(StringTextFieldCell)
);
