import React, { useCallback, useState, useRef } from "react";
import { ControlProps, rankWith, formatIs } from "@jsonforms/core";
import { withJsonFormsControlProps } from "@jsonforms/react";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { CircleHelp } from "lucide-react";

/**
 * Custom JSONForms renderer for handling base64-encoded images
 * This renderer will:
 * - Display base64-encoded images
 * - Allow selecting new images via file dialog
 * - Encode selected images to base64
 */
const Base64JPEGRenderer = ({
  data,
  handleChange,
  path,
  label,
  schema,
  required,
  errors,
  id,
}: ControlProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imageError, setImageError] = useState<string | null>(null);

  const handleImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];

      if (!file) return;

      if (!file.type.match(/image\/jpe?g/)) {
        setImageError("Only JPG images are allowed");
        return;
      }
      if (file.size > 2 * 1024 * 1024) {
        setImageError("Image size should not exceed 2MB");
        return;
      }
      setImageError(null);

      const reader = new FileReader();
      reader.onload = () => {
        const base64DataWithPrefix = reader.result as string;
        // Strip data URL prefix to get just the base64 data for the backend
        const base64Data = base64DataWithPrefix.replace(
          /^data:image\/jpe?g;base64,/,
          ""
        );
        handleChange(path, base64Data);
      };
      reader.onerror = () => setImageError("Failed to read the file");
      reader.readAsDataURL(file);
    },
    [handleChange, path]
  );

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const isValidBase64Image = (data: string) => {
    if (!data) return false;
    try {
      // Check if it's a string and contains valid base64 characters
      return typeof data === "string" && /^[A-Za-z0-9+/=]+$/.test(data.trim());
    } catch {
      return false;
    }
  };

  // Helper function to ensure data has the proper image prefix
  const getImageSrc = (data: string) => {
    if (!data) return "";
    // If it already has the prefix, use it as is
    if (data.startsWith("data:image")) {
      return data;
    }
    // Otherwise add the proper prefix
    return `data:image/jpeg;base64,${data}`;
  };
  const hasDescription = schema.description && schema.description.length > 0;

  return (
    <div className="my-5">
      <div className="mb-2 flex items-center gap-2">
        <label htmlFor={id} className="font-medium">
          {label}
          {required && <span className="text-red-500">*</span>}
        </label>
        {hasDescription && (
          <Tooltip>
            <TooltipTrigger asChild>
              <CircleHelp className="h-4 w-4 cursor-help text-gray-400" />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs text-sm">
              {schema.description}
            </TooltipContent>
          </Tooltip>
        )}
      </div>

      <div className="flex flex-col items-start">
        {isValidBase64Image(data) ? (
          <div className="mb-4 overflow-hidden rounded border border-gray-300">
            <img
              src={getImageSrc(data)}
              alt={label || "Image"}
              className="rounded"
              style={{ maxWidth: "100%", maxHeight: "200px" }}
            />
          </div>
        ) : (
          <div className="mb-4 flex items-start justify-center rounded border border-dashed border-gray-300 bg-gray-50 p-8 text-gray-500">
            No image selected
          </div>
        )}

        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg"
          onChange={handleImageSelect}
          className="hidden"
          id={`${id}-input`}
        />

        <button
          type="button"
          onClick={openFileDialog}
          className="rounded bg-gray-200 px-4 py-2 text-gray-800 transition duration-200 hover:bg-gray-300"
        >
          {data ? "Change Image" : "Select Image"}
        </button>

        {(errors || imageError) && (
          <div className="mt-2 text-xs font-normal text-red-500">
            {imageError || errors}
          </div>
        )}
      </div>
    </div>
  );
};

export const base64JPEGRendererTester = rankWith(20, formatIs("base64jpeg"));

export default withJsonFormsControlProps(Base64JPEGRenderer);
