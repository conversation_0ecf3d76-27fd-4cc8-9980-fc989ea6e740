import React from "react";
import { render, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { TooltipProvider } from "~/@shadcn/ui/tooltip";
import { testable, enumSelectRendererTester } from "./EnumSelectRenderer";
import { ControlProps } from "@jsonforms/core";

const EnumSelectRenderer = testable.EnumSelectRenderer;

const renderWithTooltipProvider = (component: React.ReactElement) => {
  return render(<TooltipProvider>{component}</TooltipProvider>);
};

describe("EnumSelectRenderer", () => {
  const mockHandleChange = vi.fn();

  const baseProps: ControlProps = {
    data: "",
    handleChange: mockHandleChange,
    path: "test.path",
    label: "Test Select",
    schema: { type: "string", enum: ["option1", "option2", "option3"] },
    required: false,
    id: "test-select",
    enabled: true,
    errors: "",
    visible: true,
    uischema: {
      type: "Control" as const,
      scope: "#/properties/test",
    },
    rootSchema: {},
    config: {},
  };

  beforeAll(() => {
    // Make the interactions with the select component work correctly.
    // cf https://github.com/radix-ui/primitives/issues/1822
    window.HTMLElement.prototype.hasPointerCapture = vi.fn();
    window.HTMLElement.prototype.scrollIntoView = vi.fn();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("rendering", () => {
    it("renders basic select with label", () => {
      renderWithTooltipProvider(<testable.EnumSelectRenderer {...baseProps} />);

      expect(screen.getByLabelText("Test Select")).toBeInTheDocument();
      expect(screen.getByRole("combobox")).toBeInTheDocument();
    });

    it("renders required indicator when required is true", () => {
      renderWithTooltipProvider(
        <EnumSelectRenderer {...baseProps} required={true} />
      );

      expect(screen.getByText("*")).toBeInTheDocument();
      expect(screen.getByText("*")).toHaveClass("text-red-500");
    });

    it("renders description tooltip icon when schema has description", () => {
      const propsWithDescription = {
        ...baseProps,
        schema: {
          ...baseProps.schema,
          description: "This is a helpful description",
        },
      };

      renderWithTooltipProvider(
        <EnumSelectRenderer {...propsWithDescription} />
      );

      expect(screen.getByTestId("descriptionIcon")).toBeInTheDocument();
    });

    it("does not render tooltip icon when schema has no description", () => {
      renderWithTooltipProvider(<EnumSelectRenderer {...baseProps} />);

      expect(screen.queryByTestId("descriptionIcon")).not.toBeInTheDocument();
    });

    it("renders error message when errors are present", () => {
      renderWithTooltipProvider(
        <EnumSelectRenderer {...baseProps} errors="This field is required" />
      );

      expect(screen.getByText("This field is required")).toBeInTheDocument();
      expect(screen.getByText("This field is required")).toHaveClass(
        "text-red-500"
      );
    });

    it("disables select when enabled is false", () => {
      renderWithTooltipProvider(
        <EnumSelectRenderer {...baseProps} enabled={false} />
      );

      expect(screen.getByRole("combobox")).toBeDisabled();
    });
  });

  describe("enum options", () => {
    it("renders enum options correctly when opened", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<EnumSelectRenderer {...baseProps} />);

      await user.click(screen.getByRole("combobox"));

      await screen.findByText("option1");
      expect(screen.getByText("option1")).toBeInTheDocument();
      expect(screen.getByText("option2")).toBeInTheDocument();
      expect(screen.getByText("option3")).toBeInTheDocument();
    });

    it("uses custom labels from uischema when provided", async () => {
      const user = userEvent.setup();
      const propsWithCustomLabels = {
        ...baseProps,
        uischema: {
          ...baseProps.uischema,
          options: {
            enumLabels: ["First Option", "Second Option", "Third Option"],
          },
        },
      };

      renderWithTooltipProvider(
        <EnumSelectRenderer {...propsWithCustomLabels} />
      );

      await user.click(screen.getByRole("combobox"));

      await screen.findByText("First Option");
      expect(screen.getByText("First Option")).toBeInTheDocument();
      expect(screen.getByText("Second Option")).toBeInTheDocument();
      expect(screen.getByText("Third Option")).toBeInTheDocument();
    });

    it("handles empty string option correctly", async () => {
      const user = userEvent.setup();
      const propsWithEmptyOption = {
        ...baseProps,
        schema: { type: "string", enum: ["", "option1", "option2"] },
        data: "",
      };

      renderWithTooltipProvider(
        <EnumSelectRenderer {...propsWithEmptyOption} />
      );

      await user.click(screen.getByRole("combobox"));

      const options = await screen.findAllByRole("option");
      expect(options).toHaveLength(3);
      expect(options[0]).toHaveTextContent(""); // First option is the empty string
    });

    it("handles empty string option with custom labels", async () => {
      const user = userEvent.setup();
      const propsWithEmptyOptionAndLabels = {
        ...baseProps,
        schema: { type: "string", enum: ["", "option1", "option2"] },
        uischema: {
          ...baseProps.uischema,
          options: {
            enumLabels: ["Default", "First Option", "Second Option"],
          },
        },
        data: "",
      };

      renderWithTooltipProvider(
        <EnumSelectRenderer {...propsWithEmptyOptionAndLabels} />
      );

      await user.click(screen.getByRole("combobox"));

      const options = await screen.findAllByRole("option");
      expect(options).toHaveLength(3);
      expect(options[0]).toHaveTextContent("Default");
      expect(options[1]).toHaveTextContent("First Option");
      expect(options[2]).toHaveTextContent("Second Option");
    });

    it("handles selecting an empty string option", async () => {
      const user = userEvent.setup();
      const propsWithEmptyOption = {
        ...baseProps,
        schema: { type: "string", enum: ["", "option1", "option2"] },
        data: "option1",
      };

      renderWithTooltipProvider(
        <EnumSelectRenderer {...propsWithEmptyOption} />
      );

      await user.click(screen.getByRole("combobox"));
      const options = await screen.findAllByRole("option");
      await user.click(options[0]!); // First option is the empty string

      expect(mockHandleChange).toHaveBeenCalledWith("test.path", "");
    });
  });

  describe("oneOf schema", () => {
    const oneOfProps = {
      ...baseProps,
      schema: {
        type: "string",
        oneOf: [
          { const: "value1", title: "Title 1" },
          { const: "value2", title: "Title 2" },
          { const: "value3" },
        ],
      },
    };

    it("renders oneOf options with titles", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<EnumSelectRenderer {...oneOfProps} />);

      await user.click(screen.getByRole("combobox"));

      await screen.findByText("Title 1");
      expect(screen.getByText("Title 2")).toBeInTheDocument();
      expect(screen.getByText("value3")).toBeInTheDocument();
    });
  });

  describe("value handling", () => {
    it("displays selected value correctly", () => {
      renderWithTooltipProvider(
        <EnumSelectRenderer {...baseProps} data="option2" />
      );

      // The value should be visible in the select trigger
      expect(screen.getByText("option2")).toBeInTheDocument();
    });

    it("calls handleChange with correct value when option is selected", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<EnumSelectRenderer {...baseProps} />);

      await user.click(screen.getByRole("combobox"));
      await user.click(await screen.findByText("option2"));

      expect(mockHandleChange).toHaveBeenCalledWith("test.path", "option2");
    });

    it("calls handleChange with empty string when empty option is selected", async () => {
      const user = userEvent.setup();
      const propsWithEmptyOption = {
        ...baseProps,
        schema: { type: "string", enum: ["", "option1", "option2"] },
        data: "option1",
      };

      renderWithTooltipProvider(
        <EnumSelectRenderer {...propsWithEmptyOption} />
      );

      await user.click(screen.getByRole("combobox"));
      const options = await screen.findAllByRole("option");
      await user.click(options[0]!); // First option is the empty string

      expect(mockHandleChange).toHaveBeenCalledWith("test.path", "");
    });
  });

  describe("tester", () => {
    const mockContext = {
      rootSchema: {},
      config: {},
    };

    it("ranks enum control correctly", () => {
      const enumSchema = { type: "string", enum: ["a", "b", "c"] };
      const uischema = { type: "Control" as const, scope: "#/properties/test" };

      const result = enumSelectRendererTester(
        uischema,
        enumSchema,
        mockContext
      );
      expect(result).toBe(20);
    });

    it("ranks oneOf enum control correctly", () => {
      const oneOfSchema = {
        type: "string",
        oneOf: [{ const: "a" }, { const: "b" }],
      };
      const uischema = { type: "Control" as const, scope: "#/properties/test" };

      const result = enumSelectRendererTester(
        uischema,
        oneOfSchema,
        mockContext
      );
      expect(result).toBe(20);
    });

    it("does not rank non-enum controls", () => {
      const stringSchema = { type: "string" };
      const uischema = { type: "Control" as const, scope: "#/properties/test" };

      const result = enumSelectRendererTester(
        uischema,
        stringSchema,
        mockContext
      );
      expect(result).toBe(-1);
    });
  });
});
