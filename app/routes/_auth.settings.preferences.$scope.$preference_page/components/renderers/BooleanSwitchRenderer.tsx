import React from "react";
import { ControlProps, isBooleanControl, rankWith } from "@jsonforms/core";
import { withJsonFormsControlProps } from "@jsonforms/react";
import { Switch } from "~/@shadcn/ui/switch";
import { CircleHelp } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

/**
 * Renderer for boolean controls using ShadCN Switch component
 */
const BooleanSwitchRenderer = (props: ControlProps) => {
  const {
    data,
    handleChange,
    path,
    label,
    schema,
    required,
    id,
    enabled,
    errors,
  } = props;

  const isChecked = !!data;
  const hasDescription = schema.description && schema.description.length > 0;

  const onChange = (checked: boolean) => {
    handleChange(path, checked);
  };

  return (
    <div className="mt-6 flex flex-col">
      <div className="mb-2 flex items-center gap-2">
        <label htmlFor={id} className="font-medium">
          {label}
          {required && <span className="text-red-500">*</span>}
        </label>
        {hasDescription && (
          <Tooltip>
            <TooltipTrigger asChild>
              <CircleHelp className="h-4 w-4 cursor-help text-gray-400" />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs text-sm">
              {schema.description}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
      <Switch
        id={id}
        checked={isChecked}
        onCheckedChange={onChange}
        disabled={!enabled}
        data-testid={label}
      />
      {errors && (
        <div className="mt-2 text-xs font-normal text-red-500">{errors}</div>
      )}
    </div>
  );
};

// This tester will be used to determine when to use this renderer
// It matches any boolean control with a high rank of 10
export const booleanSwitchRendererTester = rankWith(10, isBooleanControl);

export default withJsonFormsControlProps(BooleanSwitchRenderer);
