import React from "react";
import { ControlProps, isStringControl, rankWith } from "@jsonforms/core";
import { withJsonFormsControlProps } from "@jsonforms/react";
import { CircleHelp } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

const StringTextFieldRenderer = (props: ControlProps) => {
  const {
    data,
    handleChange,
    path,
    label,
    schema,
    required,
    id,
    enabled,
    errors,
    uischema,
  } = props;

  const hasDescription = schema.description && schema.description.length > 0;
  const placeholder = uischema.options?.placeholder || "";

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(path, e.target.value);
  };

  return (
    <div className="mt-6 flex flex-col">
      <div className="mb-2 flex items-center gap-2">
        <label htmlFor={id} className="font-medium">
          {label}
          {required && <span className="text-red-500">*</span>}
        </label>
        {hasDescription && (
          <Tooltip>
            <TooltipTrigger asChild>
              <CircleHelp className="h-4 w-4 cursor-help text-gray-400" />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs text-sm">
              {schema.description}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
      <input
        type="text"
        id={id}
        value={data || ""}
        onChange={onChange}
        placeholder={placeholder}
        disabled={!enabled}
        className={`mt-3 h-10 rounded-md border px-3 ${
          !enabled ? "opacity-50" : ""
        }`}
      />
      {errors && (
        <div className="mt-2 text-xs font-normal text-red-500">{errors}</div>
      )}
    </div>
  );
};

// This tester will be used to determine when to use this renderer
// It matches any string control with a high rank of 10
export const stringTextFieldRendererTester = rankWith(10, isStringControl);

export default withJsonFormsControlProps(StringTextFieldRenderer);
