/*
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Base64<PERSON>EG<PERSON>enderer from "./Base64JPEGRenderer";

// Mock a sample base64 image string
const sampleBase64Image =
  "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAALCAABAAEBAREA/8QAFAABAAAAAAAAAAAAAAAAAAAACv/EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAD8AVVX/2Q==";

// Mock props for the Base64JPEGRenderer component
const mockProps = {
  data: sampleBase64Image,
  handleChange: vi.fn(),
  path: "test",
  label: "Test Image",
  schema: { type: "string", format: "base64image" },
  required: false,
  errors: "",
  id: "test-id",
  visible: true,
  enabled: true,
  uischema: { type: "Control", scope: "#/properties/image" },
};

describe("Base64JPEGRenderer", () => {
  it("renders with a valid base64 image", () => {
    render(<Base64JPEGRenderer {...mockProps} />);

    // Check if the label is rendered
    expect(screen.getByText("Test Image")).toBeInTheDocument();

    // Check if the image is rendered
    const image = screen.getByAltText("Test Image");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", sampleBase64Image);

    // Check if the change button is rendered
    expect(screen.getByText("Change Image")).toBeInTheDocument();
  });

  it("renders without an image when no data is provided", () => {
    render(<Base64JPEGRenderer {...mockProps} data={undefined} />);

    // Check if the placeholder is shown
    expect(screen.getByText("No image selected")).toBeInTheDocument();

    // Check if the select button is rendered
    expect(screen.getByText("Select Image")).toBeInTheDocument();
  });

  it("displays errors when provided", () => {
    render(
      <Base64JPEGRenderer {...mockProps} errors="This field is required" />
    );

    // Check if the error message is displayed
    expect(screen.getByText("This field is required")).toBeInTheDocument();
  });

  it("has a hidden file input", () => {
    render(<Base64JPEGRenderer {...mockProps} />);

    // Check if the file input exists and is hidden
    const fileInput = screen.getByAcceptingFiles();
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).not.toBeVisible();

    // Check if it only accepts JPG images
    expect(fileInput).toHaveAttribute("accept", "image/jpeg,image/jpg");
  });

  it("clicking the button triggers the file dialog", async () => {
    render(<Base64JPEGRenderer {...mockProps} />);

    const fileInput = screen.getByAcceptingFiles();
    const button = screen.getByText("Change Image");

    // Mock the click method on the file input
    fileInput.click = vi.fn();

    // Click the button
    userEvent.click(button);

    // The file input's click method should have been called
    expect(fileInput.click).toHaveBeenCalled();
  });
});
*/
