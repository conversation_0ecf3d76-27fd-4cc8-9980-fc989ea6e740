import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import PreferenceForm, { getChangedData } from "./PreferenceForm";
import { PreferenceSchema } from "~/api/openapi/generated";

describe("PreferenceForm", () => {
  const notificationsSchema: PreferenceSchema = {
    title: "Notification Settings",
    jsonSchema: {
      title: "NotificationPreferences",
      type: "object",
      required: ["emailNotifications"],
      properties: {
        emailNotifications: {
          type: "boolean",
          title: "Email Notifications",
        },
        smsNotifications: {
          type: "boolean",
          title: "SMS Notifications",
        },
        notificationFrequency: {
          type: "string",
          enum: ["immediate", "daily", "weekly"],
          title: "Notification Frequency",
        },
      },
    },
    uiSchema: {
      type: "VerticalLayout",
      elements: [
        {
          type: "Control",
          scope: "#/properties/emailNotifications",
        },
        {
          type: "Control",
          scope: "#/properties/smsNotifications",
        },
        {
          type: "Control",
          scope: "#/properties/notificationFrequency",
        },
      ],
    },
    data: {
      emailNotifications: true,
      smsNotifications: false,
      notificationFrequency: "daily",
    },
  };

  const mockOnSubmit = vi.fn();

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders the form with the correct title", async () => {
    render(
      <PreferenceForm
        title={notificationsSchema.title}
        jsonSchema={notificationsSchema.jsonSchema}
        uiSchema={notificationsSchema.uiSchema}
        data={notificationsSchema.data}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText("Notification Settings")).toBeInTheDocument();
    expect(screen.getByText("Email Notifications")).toBeInTheDocument();
    expect(screen.getByText("SMS Notifications")).toBeInTheDocument();
    await waitFor(() => {
      expect(
        screen.getByRole("button", { name: /save changes/i })
      ).toBeEnabled();
    });
  });

  it("submits the form with updated data", async () => {
    const user = userEvent.setup();

    render(
      <PreferenceForm
        title={notificationsSchema.title}
        jsonSchema={notificationsSchema.jsonSchema}
        uiSchema={notificationsSchema.uiSchema}
        data={notificationsSchema.data}
        onSubmit={mockOnSubmit}
      />
    );

    const emailCheckbox = screen.getByTestId("Email Notifications");
    await user.click(emailCheckbox);
    await waitFor(() => {
      expect(emailCheckbox).not.toBeChecked();
    });

    const saveButton = screen.getByRole("button", { name: /save changes/i });
    await user.click(saveButton);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      schemaTitle:
        "title" in notificationsSchema.jsonSchema
          ? notificationsSchema.jsonSchema.title
          : "error-title-missing-from-jsonSchema",
      updatedValues: {
        emailNotifications: false,
      },
    });
  });

  it("submits the form with no updated data if the data is toggled to the original state", async () => {
    const user = userEvent.setup();

    render(
      <PreferenceForm
        title={notificationsSchema.title}
        jsonSchema={notificationsSchema.jsonSchema}
        uiSchema={notificationsSchema.uiSchema}
        data={notificationsSchema.data}
        onSubmit={mockOnSubmit}
      />
    );

    const emailCheckbox = screen.getByText("Email Notifications");
    await user.click(emailCheckbox);
    await user.click(emailCheckbox);

    const saveButton = screen.getByRole("button", { name: /save changes/i });
    await waitFor(() => {
      expect(saveButton).toBeEnabled();
    });
    await user.click(saveButton);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      schemaTitle:
        "title" in notificationsSchema.jsonSchema
          ? notificationsSchema.jsonSchema.title
          : "error-title-missing-from-jsonSchema",
      updatedValues: {},
    });
  });

  it("enables the save button with valid initial data", async () => {
    render(
      <PreferenceForm
        title={notificationsSchema.title}
        jsonSchema={notificationsSchema.jsonSchema}
        uiSchema={notificationsSchema.uiSchema}
        data={notificationsSchema.data}
        onSubmit={mockOnSubmit}
      />
    );

    const saveButton = screen.getByRole("button", { name: /save changes/i });
    await waitFor(() => {
      expect(saveButton).toBeEnabled();
    });
  });

  it("disables the save button by default", async () => {
    const user = userEvent.setup();

    render(
      <PreferenceForm
        title={notificationsSchema.title}
        jsonSchema={notificationsSchema.jsonSchema}
        uiSchema={notificationsSchema.uiSchema}
        data={{}}
        onSubmit={mockOnSubmit}
      />
    );

    const emailCheckbox = screen.getByText("Email Notifications");
    await user.click(emailCheckbox);

    const saveButton = screen.getByRole("button", { name: /save changes/i });

    await waitFor(() => {
      expect(saveButton).toBeEnabled();
    });
  });
});

describe("getChangedData", () => {
  it("handles empty objects", () => {
    const result = getChangedData({}, {});

    expect(result).toEqual({});
  });

  it("handles empty original object", () => {
    const original = {};
    const modified = { name: "Test", age: 30 };

    const result = getChangedData(original, modified);

    expect(result).toEqual({ name: "Test", age: 30 });
  });

  it("handles empty modified object", () => {
    const original = { name: "Test", age: 30 };
    const modified = {};

    const result = getChangedData(original, modified);

    expect(result).toEqual({});
  });

  it("returns an empty object when there are no changes", () => {
    const original = { name: "Test", age: 30 };
    const modified = { name: "Test", age: 30 };

    const result = getChangedData(original, modified);

    expect(result).toEqual({});
  });

  it("returns only changed values for simple objects", () => {
    const original = { name: "Test", age: 30, active: true };
    const modified = { name: "Test", age: 35, active: true };

    const result = getChangedData(original, modified);

    expect(result).toEqual({ age: 35 });
  });

  it("returns multiple changed values", () => {
    const original = { name: "Test", age: 30, active: true };
    const modified = { name: "Updated", age: 35, active: false };

    const result = getChangedData(original, modified);

    expect(result).toEqual({ name: "Updated", age: 35, active: false });
  });

  it("handles nested object changes", () => {
    const original = {
      user: { name: "Test", details: { age: 30 } },
      settings: { darkMode: true },
    };
    const modified = {
      user: { name: "Test", details: { age: 31 } },
      settings: { darkMode: true },
    };

    const result = getChangedData(original, modified);

    expect(result).toEqual({
      user: { details: { age: 31 } },
    });
  });

  it("handles complex nested object changes", () => {
    const original = {
      user: {
        name: "Test",
        details: {
          age: 30,
          address: {
            city: "New York",
            zipCode: "10001",
          },
        },
      },
      preferences: {
        notifications: {
          email: true,
          sms: false,
        },
      },
    };
    const modified = {
      user: {
        name: "Test",
        details: {
          age: 30,
          address: {
            city: "Boston",
            zipCode: "10001",
          },
        },
      },
      preferences: {
        notifications: {
          email: true,
          sms: true,
        },
      },
    };

    const result = getChangedData(original, modified);

    expect(result).toEqual({
      user: {
        details: {
          address: {
            city: "Boston",
          },
        },
      },
      preferences: {
        notifications: {
          sms: true,
        },
      },
    });
  });

  it("handles arrays in objects correctly", () => {
    const original = {
      tags: ["react", "typescript"],
      settings: { theme: "light" },
    };
    const modified = {
      tags: ["react", "typescript", "redux"],
      settings: { theme: "dark" },
    };

    const result = getChangedData(original, modified);

    expect(result).toEqual({
      tags: ["react", "typescript", "redux"],
      settings: { theme: "dark" },
    });
  });
});
