import { useState } from "react";
import { JsonForms } from "@jsonforms/react";
import { Button } from "~/@shadcn/ui/button";
import { vanillaRenderers } from "@jsonforms/vanilla-renderers";
import { customCells, customRenderers } from "./renderers";
import { UISchema } from "~/api/openapi/generated";
import { datadogLogs } from "@datadog/browser-logs";
import { diff } from "deep-object-diff";
import { Validator } from "jsonschema";
import type { ErrorObject } from "ajv";

// Returns an object, a subset of formData, containing only the keys that have changed and their new
// values.
//
// This only returns values for keys that exist in formData; if a key exists in the originalData but
// not in formData, it will not be included in the returned object.
export const getChangedData = (originalData: object, formData: object) => {
  // Iterate recursively through the the diff object, setting the value of each key to the value
  // in formData.
  const diffs = diff(originalData, formData);
  const updatedValuesForDiffs = (
    diffs: Record<string, any>,
    formRecord: Record<string, any>
  ) => {
    const updatedValues: Record<string, any> = {};
    for (const [key, value] of Object.entries(diffs)) {
      const formDataValue = formRecord[key];
      if (
        typeof formDataValue === "object" &&
        formDataValue !== null &&
        !Array.isArray(formDataValue)
      ) {
        updatedValues[key] = updatedValuesForDiffs(value, formDataValue);
      } else {
        updatedValues[key] = formDataValue;
      }
    }
    return updatedValues;
  };

  return updatedValuesForDiffs(diffs, formData as Record<string, any>);
};

interface PreferenceFormProps {
  title: string;
  jsonSchema: object;
  uiSchema: UISchema;
  data: object;
  onSubmit: (data: {
    schemaTitle: string;
    updatedValues: Record<string, any>;
  }) => void;
}

const PreferenceForm = ({
  title,
  jsonSchema,
  uiSchema,
  data,
  onSubmit,
}: PreferenceFormProps) => {
  const [formData, setFormData] = useState(data);
  const [validationErrors, setValidationErrors] = useState<ErrorObject[]>([]);
  const isValid = validationErrors.length === 0;
  const validator = new Validator();

  // Get the schema identifier
  const schemaTitle = (jsonSchema as any).title ?? "unknown-schema-title";
  if (schemaTitle === "unknown-schema-title") {
    datadogLogs.logger.warn(
      "PreferenceForm: Schema title is missing, using default 'unknown-schema-title'."
    );
  }

  const handleChange = ({ data }: { data: any }) => {
    setFormData(data);

    // JSONForms uses AJV library for validation by default, but AJV relies on `eval` to compile
    // schemas, which requires the dangerous `unsafe-eval` CSP directive. To avoid this, we use a
    // different JSON schema validator that does not use `eval` and feed its error back into the
    // JsonForms component. This will be slower than AJV, but the schemas we are using are small and
    // simple, and generated at runtime, so there is not a good way for us to use AJV.

    // Convert jsonschema validation errors to AJV ErrorObject format expected by JSONForms. Per the
    // example at https://jsonforms.io/docs/validation/#external-validation-errors, we only need the
    // instancePath and keyword fields, but we include other easy-to-map fields. Experimentally,
    // the parentSchema field is also necessary in order for errors with oneOf properties to be
    // displayed correctly.
    const mappedErrors: ErrorObject[] = validator
      .validate(data, jsonSchema)
      .errors.map((error) => ({
        instancePath: `/${error.path.join("/")}`,
        schemaPath: `#/properties/${error.path.join("/")}/${error.name}`,
        keyword: error.name,
        message: error.message || "Validation error",
        params: {},
        parentSchema: error.schema instanceof Object ? error.schema : undefined,
      }));
    setValidationErrors(mappedErrors);
  };

  const handleSubmit = () => {
    onSubmit({
      schemaTitle,
      updatedValues: getChangedData(data, formData),
    });
  };

  return (
    <div className="w-full">
      <h1 className="mb-6 text-2xl font-semibold">{title}</h1>
      <JsonForms
        schema={jsonSchema}
        uischema={uiSchema}
        data={formData}
        renderers={[...vanillaRenderers, ...customRenderers]}
        cells={[...customCells]}
        onChange={handleChange}
        validationMode="NoValidation"
        additionalErrors={validationErrors}
      />

      <div className="mt-6">
        <Button
          type="submit"
          color="primary"
          onClick={handleSubmit}
          disabled={!isValid}
        >
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default PreferenceForm;
