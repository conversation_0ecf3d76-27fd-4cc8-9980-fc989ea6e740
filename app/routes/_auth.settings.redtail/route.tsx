import {
  ActionFunctionArgs,
  data,
  redirect,
  type LoaderFunctionArgs,
  type MetaFunction,
} from "react-router";
import { Form, Link, useActionData, useLoaderData } from "react-router";
import { Typography } from "~/@ui/Typography";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import {
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
} from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import { ArrowLeft, Eye, EyeOff, Lock, Mail } from "lucide-react";
import { useState } from "react";
import { Button } from "~/@shadcn/ui/button";
import { flattenZodErrors, formDataToObject } from "~/utils/validation";
import { z } from "zod";
import { logError } from "~/utils/log.server";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, CrmApi } from "~/api/openapi/generated";

// Types
const RedtailLoginFormStruct = z.object({
  username: z.string().min(1, "Username required"),
  password: z.string().min(3, "Invalid password"),
});

// Exports
export const meta: MetaFunction = () => [
  { title: "Settings - Redtail Integration" },
  { name: "description", content: "Settings - Redtail Integration" },
];

export const action = async ({ request }: ActionFunctionArgs) => {
  // Attempt to authenticate user using email strategy
  try {
    // Validate form data
    const validatedFormData = RedtailLoginFormStruct.parse(
      formDataToObject(await request.formData())
    );
    const config = new Configuration(await configurationParameters(request));
    await new CrmApi(config).crmGenerateRedtailUserKey({
      redtailCredentials: {
        username: validatedFormData.username,
        password: validatedFormData.password,
      },
    });
    return redirect("/settings/redtail");
  } catch (error) {
    // Form validation errors
    if (error instanceof z.ZodError) {
      logError("error", error);
      return data({ errors: flattenZodErrors(error) }, { status: 400 });
    }

    return data(
      {
        errors: [
          "Failed to connect Redtail, please check your credentials or contact support.",
        ],
      },
      { status: 400 }
    );
  }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const config = new Configuration(await configurationParameters(request));
    const { active } = await new CrmApi(
      config
    ).crmGetRedtailIntegrationStatus();
    return { active };
  } catch (error) {
    return { active: false };
  }
};

const Route = () => {
  const { active } = useLoaderData<typeof loader>();
  const data = useActionData<typeof action>();
  const formErrors = useFormErrors(data);
  const [showPassword, setShowPassword] = useState(false);
  return (
    <div className="flex flex-col gap-3 self-stretch">
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings/integrations">
            <ArrowLeft />
          </Link>
        </Button>
        <h2 className="text-2xl font-semibold">Redtail integration</h2>
      </div>
      <br />
      <Typography color="primary">
        Integration is <b>{active ? "active" : "inactive"}</b>
      </Typography>
      {!active && (
        <div>
          <Typography variant="h3" color="primary">
            Integrate With Redtail Credentials
          </Typography>
          <Typography color="primary">
            Connecting Redtail to Zeplyn allows you to sync your contacts, notes
            and tasks between the two platforms.{" "}
          </Typography>
          <Typography color="primary">
            If authorized, Zeplyn will be able to read and write data to Redtail
            on your behalf. Please provide your current Redtail credentials to
            authorize Zeplyn.
          </Typography>
          <Form
            id="redtailForm"
            className="flex flex-col self-stretch"
            method="post"
          >
            <FormAlertsStack errors={formErrors} />
            <FormField name="username" required>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input
                  size="lg"
                  placeholder="Your Redtail username"
                  type="text"
                  leftIcon={<Mail />}
                />
              </FormControl>
              <FormDescription className="flex justify-between">
                <span>&nbsp;</span>
              </FormDescription>
              <FormMessage />
            </FormField>

            <FormField name="password" required>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input
                  size="lg"
                  placeholder="Your Redtail password"
                  type={showPassword ? "text" : "password"}
                  leftIcon={<Lock />}
                  rightIcon={
                    <Button
                      title={showPassword ? "Hide password" : "Show password"}
                      variant="ghost"
                      size="icon-sm"
                      onClick={(event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        setShowPassword((prev) => !prev);
                      }}
                    >
                      {showPassword ? <EyeOff /> : <Eye />}
                    </Button>
                  }
                />
              </FormControl>
              <FormDescription className="flex justify-between">
                <span>&nbsp;</span>
              </FormDescription>
              <FormMessage />
            </FormField>

            <div className="flex flex-col self-stretch px-0 py-6">
              <Button size="lg" type="submit">
                Authorize
              </Button>
            </div>
          </Form>
        </div>
      )}
      {active && (
        <div>
          <Typography color="primary">
            Zeplyn is connected to Redtail. If you are having difficulties with
            the integration, please contact support at:
          </Typography>
          <br />
          <Typography color="warning">
            <a href="mailto:<EMAIL>">
              <b><EMAIL></b>
            </a>
          </Typography>
        </div>
      )}
    </div>
  );
};
export default Route;
