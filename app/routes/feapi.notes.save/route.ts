// API URL: /feapi/notes/save
import { ActionFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, NoteApi } from "~/api/openapi/generated";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const {
      noteId,
      clientName,
      clientId,
      meetingName,
      attendees,
      summary,
      advisorNotes,
      keyTakeaways,
      actionItems,
    } = body;

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const noteApi = new NoteApi(configuration);

    await noteApi.noteEditNote({
      noteId,
      editNoteRequest: {
        client: {
          name: clientName,
          uuid: clientId,
          email: null,
        },
        meetingName,
        attendees,
        summary,
        advisorNotes,
        keyTakeaways,
        actionItems,
      },
    });

    return { success: true };
  } catch (e) {
    return { success: false };
  }
};
