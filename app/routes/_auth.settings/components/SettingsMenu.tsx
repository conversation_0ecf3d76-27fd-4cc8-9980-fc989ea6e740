import { Link, useLocation } from "react-router";
import {
  <PERSON>s,
  <PERSON>lette,
  ReceiptText,
  <PERSON><PERSON><PERSON>,
  Settings2,
  <PERSON><PERSON>ser,
  <PERSON><PERSON><PERSON>H<PERSON>zontal,
  User,
} from "lucide-react";

import {
  MenuItem,
  MenuItemId,
  PreferencesResponse,
} from "~/api/openapi/generated";
import { cn } from "~/@shadcn/utils";

// Custom type for menu items that can include both API-generated and custom items
interface CustomMenuItem {
  id: string;
  label: string;
  items?: CustomMenuItem[];
}

type SettingsMenuProps = {
  menuData: MenuItem[] | undefined;
  preferences: PreferencesResponse;
};

const SettingsMenu = ({ menuData, preferences }: SettingsMenuProps) => {
  const userPreferenceSection: CustomMenuItem[] =
    preferences.userPreferences.length > 0
      ? [
          {
            id: "preferences",
            label: "Preferences",
            items: preferences.userPreferences.map((schema) => ({
              id: `preferences/user/${schema.title
                .toLowerCase()
                .replace(/\s+/g, "-")}`,
              label: schema.title,
            })),
          },
        ]
      : [];

  const orgPreferenceSection: CustomMenuItem[] =
    preferences.orgPreferences.length > 0
      ? [
          {
            id: "organization-preferences",
            label: "Organization Preferences",
            items: preferences.orgPreferences.map((schema) => ({
              id: `preferences/organization/${schema.title
                .toLowerCase()
                .replace(/\s+/g, "-")}`,
              label: schema.title,
            })),
          },
        ]
      : [];

  return (
    <>
      {menuData?.map((item) => {
        if (item.id === MenuItemId.Preferences) {
          return userPreferenceSection.map((item) => (
            <MenuRowItem key={item.id} item={item} isOuterMost />
          ));
        }
        if (item.id === MenuItemId.OrgPreferences) {
          return orgPreferenceSection.map((item) => (
            <MenuRowItem key={item.id} item={item} isOuterMost />
          ));
        }
        return (
          <MenuRowItem
            key={item.id}
            item={item as unknown as CustomMenuItem}
            isOuterMost
          />
        );
      })}
    </>
  );
};

const MenuRowItem = ({
  item,
  isOuterMost,
}: {
  item: CustomMenuItem;
  isOuterMost?: boolean;
}) => {
  const { id, label, items } = item;
  const MenuIcon = getIcon(id);

  const location = useLocation();
  const targetPath = `/settings/${id}`;
  const isActiveLink = location.pathname === targetPath;

  const content = (
    <>
      <div className={cn("flex items-center", isActiveLink && "text-primary")}>
        {!isOuterMost && <MenuIcon className="h-6 w-6" />}
        <span
          className={cn(
            "ml-1 whitespace-nowrap",
            isOuterMost && "text-2xl font-semibold"
          )}
        >
          {label}
        </span>
      </div>
      {items?.map((item) => (
        <div className="ml-4" key={item.id}>
          <MenuRowItem item={item} />
        </div>
      ))}
    </>
  );

  // don't show links for outer-most items
  if (!isOuterMost) {
    return (
      <Link to={targetPath} className="mt-4 flex items-center">
        {content}
      </Link>
    );
  }

  return <div className="mt-4 flex flex-col">{content}</div>;
};

function getIcon(id: string) {
  if (id === "preferences" || id.startsWith("preferences/")) {
    return SlidersHorizontal;
  }

  switch (id) {
    // my account
    case MenuItemId.Integrations:
      return Blocks;
    case MenuItemId.Settings:
      return Settings;
    case MenuItemId.ProfileDetails:
      return User;
    case MenuItemId.PlanDetails:
      return ReceiptText;
    case MenuItemId.UserInterface:
      return Palette;

    // admin
    case MenuItemId.UserImpersonation:
      return ShieldUser;

    // preferences
    case MenuItemId.OrgPreferences:
    case MenuItemId.Preferences:
      return SlidersHorizontal;

    default:
      return Settings2;
  }
}

export default SettingsMenu;
