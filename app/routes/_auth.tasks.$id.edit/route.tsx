import { Clock, Calendar } from "lucide-react";
import {
  data,
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
  type MetaFunction,
} from "react-router";
import {
  Form,
  unstable_usePrompt,
  useActionData,
  useLoaderData,
  useSubmit,
} from "react-router";
import { format } from "date-fns";
import { AfterHydration } from "~/utils/hydration";
import { TaskDueAtCard } from "~/@ui/tasks/TaskDueAtCard";
import { logError } from "~/utils/log.server";
import { z } from "zod";
import { flattenZodErrors, formDataToObject } from "~/utils/validation";
import { TaskEditableTitle } from "~/@ui/tasks/TaskEditableTitle";
import { getNotes } from "~/api/notes/getNotes.server";
import { BackButton } from "~/@ui/buttons/BackButton";
import { SaveButton } from "~/@ui/buttons/SaveButton";
import { DeleteButton } from "~/@ui/buttons/DeleteButton";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import { Separator } from "~/@shadcn/ui/separator";
import { Typography } from "~/@ui/Typography";
import React, { useState } from "react";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import { FormControl, FormField, FormLabel } from "~/@shadcn/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { Textarea } from "~/@shadcn/ui/textarea";
import { Combobox } from "~/@ui/Combobox";
import { ConfirmModal } from "~/@ui/ConfirmModal";
import {
  ApiRoutersTaskModelsClient,
  Configuration,
  TaskApi,
} from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";

// Constants
const ERROR_MISSING_PARAMETER = 'Missing route parameter "id"';

// Types
const EditTaskFormStruct = z.object({
  details: z.string().nullable().default(null),
  dueAt: z.string().nullable().default(null),
  title: z.string().min(1, "Missing 'title' field"),
  "title.checkbox": z.literal("on").optional(),
  parentNoteUuid: z
    .string()
    .min(1, "Missing 'parentNoteUuid' field")
    .nullable(),
  assignee: z.optional(z.string()),
});

// Exports
export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `Task - ${data?.task.title}` },
    { name: "description", content: "Edit task" },
  ];
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
  try {
    if (!params.id) throw Error(ERROR_MISSING_PARAMETER);
    const taskId = params.id;

    // Parse the form data
    const formData = await request.formData();
    const actionType = formData.get("actionType");

    const taskAPI = new TaskApi(
      new Configuration(await configurationParameters(request))
    );

    if (actionType === "delete-task") {
      await taskAPI.taskDeleteTask({ taskUuid: taskId });
      return redirect("/tasks");
    }

    // Validate form data
    const validatedFormData = EditTaskFormStruct.parse(
      formDataToObject(formData)
    );

    // Client sends us "---" as a placeholder for "nothing selected", but the
    // Django backend expects a `null`
    if (validatedFormData.parentNoteUuid === "---") {
      validatedFormData.parentNoteUuid = null;
    }

    // Derive task status from title checkbox state
    const { "title.checkbox": checkbox } = validatedFormData;
    const completed = checkbox === "on" ? true : false;

    // Send payload to backend
    await taskAPI.taskEditTask({
      taskUuid: taskId,
      taskUpdate: {
        assignee: validatedFormData.assignee,
        completed: completed,
        description: validatedFormData.details,
        dueDate: validatedFormData.dueAt
          ? new Date(validatedFormData.dueAt)
          : null,
        parentNoteUuid: validatedFormData.parentNoteUuid,
        title: validatedFormData.title,
      },
    });
    return redirect(`/tasks/${taskId}`);
  } catch (error) {
    logError("app/routes/tasks.$id.edit.tsx action error", error);

    // Form validation errors
    if (error instanceof z.ZodError) {
      return data({ errors: flattenZodErrors(error) }, { status: 400 });
    }

    return data({ error: "Failed to save task" }, { status: 500 });
  }
};

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  try {
    if (!params.id) throw Error(ERROR_MISSING_PARAMETER);

    // Load task details
    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const task = await new TaskApi(configuration).taskViewTask({
      taskUuid: params.id,
    });
    const notes = await getNotes({ request });
    return data({ task, notes });
  } catch (e) {
    logError("app/routes/tasks.$id.edit.tsx loader error", e);
    return redirect("/tasks");
  }
};

const Route = () => {
  const { task, notes } = useLoaderData<typeof loader>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const actionData = useActionData<typeof action>();
  const formErrors = useFormErrors(actionData);
  const [selectedAssignee, setSelectedAssignee] =
    React.useState<ApiRoutersTaskModelsClient>(
      task.assignee || { name: "", uuid: "" }
    );
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleDelete = () => {
    const formElem = document.getElementById("editTaskForm");
    if (formElem instanceof HTMLFormElement) {
      const formData = new FormData(formElem);
      formData.append("actionType", "delete-task");
      submit(formData, { method: "post" });
    }
  };

  const confirmDelete = () => {
    handleDelete();
    closeModal();
  };

  // Track if user has modified form fields. If they have, warn then that
  // navigating away will discard unsaved changes.
  const [touched, setTouched] = useState(false);
  const submit = useSubmit();
  unstable_usePrompt({
    message: "Discard unsaved changes?",
    when: ({ currentLocation, nextLocation }) => {
      return touched && currentLocation.pathname !== nextLocation.pathname;
    },
  });
  return (
    <SidebarV2
      favorSidebarOnMobile
      header={
        <HeaderV2
          left={
            <div className="lg:hidden">
              <BackButton to={`/tasks/${task.uuid}`} tooltip="Back to task" />
            </div>
          }
          right={
            <div className="flex flex-row gap-2">
              <DeleteButton onClick={openModal} tooltip="Delete task" />
              <SaveButton
                tooltip="Save task"
                onClick={() => {
                  const formElem = document.getElementById("editTaskForm");
                  if (formElem instanceof HTMLFormElement) {
                    const formData = new FormData(formElem);
                    formData.append(
                      "assignee",
                      (selectedAssignee.uuid || "").toString()
                    );
                    submit(formData, {
                      method: "post",
                    });
                  }
                }}
              />
            </div>
          }
        />
      }
    >
      <Form
        id="editTaskForm"
        method="post"
        className="flex h-full flex-col items-center self-stretch"
      >
        <div className="flex flex-col gap-3 self-stretch px-6 pb-6">
          <FormAlertsStack errors={formErrors} />
          <TaskEditableTitle
            id="title"
            name="title"
            title={task.title}
            completed={task.completed}
            onChange={(nextTitle) => {
              if (nextTitle !== task.title) setTouched(true);
            }}
          />
          <AfterHydration>
            <Typography className="inline-flex items-center rounded-md">
              <Clock />
              <span className="mx-1 grow">Created</span>
              <Typography asChild color="secondary" variant="body2">
                <span>
                  {format(new Date(task.created), "ccc, MMM do, h:mm aaa")}
                </span>
              </Typography>
            </Typography>
          </AfterHydration>
          <Separator className="my-2" />
          <FormField id="parentNoteUuid" name="parentNoteUuid">
            <FormLabel>Parent note</FormLabel>
            <Select
              name="parentNoteUuid"
              defaultValue={task.parentNoteUuid ?? "---"}
              onValueChange={(nextValue) => {
                if (nextValue !== task.parentNoteUuid) setTouched(true);
              }}
            >
              <FormControl>
                <SelectTrigger leftIcon={<Calendar />}>
                  <SelectValue />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="---">---</SelectItem>
                {notes.map((note) => (
                  <SelectItem key={note.uuid} value={note.uuid}>
                    {note.meetingName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <FormField id="assignee" name="assignee">
            <FormLabel>Assignee</FormLabel>
            <FormControl>
              <Combobox
                placeholder={"Assignee"}
                options={(task.assignees || []).map((assignee) => ({
                  label: assignee.name,
                  value: assignee.uuid,
                }))}
                selected={selectedAssignee.uuid}
                onChange={(nextValue) => {
                  const selectedAssignee = (task.assignees || []).find(
                    (ele) => ele.uuid === nextValue
                  );
                  if (selectedAssignee) {
                    setSelectedAssignee(selectedAssignee);
                    setTouched(true);
                  }
                }}
                triggerClassName="w-full"
                commandClassName="w-full"
              />
            </FormControl>
          </FormField>
          <AfterHydration>
            <TaskDueAtCard dueAt={task.dueDate} />
          </AfterHydration>
          <FormField id="details" name="details">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea
                id="details"
                resizable
                variant="outline"
                placeholder="Add description (optional)"
                rows={5}
                defaultValue={task.description ?? undefined}
                onChange={(event) => {
                  if (event.target.value !== task.description) setTouched(true);
                }}
              />
            </FormControl>
          </FormField>
        </div>
      </Form>
      <ConfirmModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onConfirm={confirmDelete}
        title="Confirm Delete"
        message="Are you sure you want to delete this task?"
      />
    </SidebarV2>
  );
};
export default Route;
