import { type MetaFunction } from "react-router";
import { useNavigate } from "react-router";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";
import { useLayoutEffect } from "react";
import { Typography } from "~/@ui/Typography";
import { useNotesFromContext } from "~/routes/_auth.notes/route";
import { ProcessingStatus } from "~/api/openapi/generated";

// Helpers
const useRedirectToFirstNoteOnDesktop = () => {
  const navigate = useNavigate();
  const { matchedBreakpoints } = useTailwindBreakpoints();
  const parentData = useNotesFromContext();
  const firstNoteId = parentData.notes.filter(
    (note) => note.status === ProcessingStatus.Processed
  )[0]?.uuid;
  const redirectToFirstNote = matchedBreakpoints.has("lg");

  useLayoutEffect(() => {
    if (firstNoteId && redirectToFirstNote) {
      navigate(`/notes/${firstNoteId}`, { replace: true });
    }
  }, [navigate, firstNoteId, redirectToFirstNote]);

  return { firstNoteId };
};

// Exports
export const meta: MetaFunction = () => [
  { title: "Notes" },
  { name: "description", content: "View all notes" },
];

const Route = () => {
  const { firstNoteId } = useRedirectToFirstNoteOnDesktop();

  return (
    <SidebarV2 header={<HeaderV2 />}>
      <div className="flex flex-col gap-3 self-stretch px-5">
        <Typography
          className="my-8 text-center"
          color="secondary"
          variant="body2"
        >
          {firstNoteId
            ? "Loading note..."
            : "Select a note to view its details."}
        </Typography>
      </div>
    </SidebarV2>
  );
};
export default Route;
