// API URL: /feapi/notes/id
import { LoaderFunctionArgs } from "react-router";
import { getNoteById } from "~/api/notes/getNoteById.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const noteId = url.searchParams.get("noteId");

  if (!noteId) {
    throw new Error("Missing noteId");
  }

  const note = await getNoteById({
    noteId,
    request,
  });
  return note;
};
