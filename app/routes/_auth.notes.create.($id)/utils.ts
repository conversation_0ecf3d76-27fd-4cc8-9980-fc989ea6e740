import { CreateNoteFormStruct } from "~/routes/_auth.notes.create.($id)/types";
import { formDataToObject } from "~/utils/validation";

// NOTE: @debojyotighosh Since the following exports rely on server side methods, they should be moved to a "server utils" folder.

export const getPayloadFromForm = async (request: Request) => {
  // Parse formData using standard Web API
  const formData = await request.formData();

  return CreateNoteFormStruct.parse(formDataToObject(formData));
};
