import React from "react";
import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { MeetingCategory, MeetingType } from "~/api/openapi/generated";
import { AttendeeOptions } from "~/api/attendees/types";
import getMeetingNameFromAttendees from "./utils/getMeetingNameFromAttendees";

// Custom hook that mimics the useEffect logic from the route component
const useMeetingTitleUpdate = ({
  initialMeetingTitle,
  isTitleUserEdited,
  userId,
  attendees,
  meetingType,
}: {
  initialMeetingTitle?: string;
  isTitleUserEdited: boolean;
  userId: string;
  attendees: AttendeeOptions;
  meetingType: MeetingType;
}) => {
  const [title, setTitle] = React.useState<string>(() => {
    if (initialMeetingTitle) {
      return initialMeetingTitle;
    }
    return getMeetingNameFromAttendees(userId, attendees, meetingType);
  });

  React.useEffect(() => {
    // exit if title was provided via query params or note title
    if (initialMeetingTitle) {
      return;
    }

    // exit if title was edited by user
    if (isTitleUserEdited) {
      return;
    }

    setTitle(getMeetingNameFromAttendees(userId, attendees, meetingType));
  }, [initialMeetingTitle, isTitleUserEdited, userId, attendees, meetingType]);

  return { title, setTitle };
};

describe("Meeting Title Update useEffect", () => {
  const userId = "user-123";
  
  const createMeetingType = (category: MeetingCategory): MeetingType => ({
    uuid: `${category}-uuid`,
    name: `${category} Meeting`,
    category,
    isShared: true,
  });

  const clientMeetingType = createMeetingType(MeetingCategory.Client);
  const internalMeetingType = createMeetingType(MeetingCategory.Internal);
  const debriefMeetingType = createMeetingType(MeetingCategory.Debrief);

  const defaultAttendees: AttendeeOptions = [
    { uuid: "attendee-1", name: "John Doe", type: "client" },
    { uuid: userId, name: "Current User", type: "user" },
  ];

  beforeEach(() => {
    // No mocking needed - we'll use the actual function
  });

  describe("Default behavior - title updates when meeting type changes", () => {
    it("should update title when meeting type changes from client to internal", () => {
      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: undefined,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      // Initial title should be generated for client meeting
      expect(result.current.title).toBe("Meeting with John Doe");

      // Change meeting type to internal
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: false,
          userId,
          attendees: defaultAttendees,
          meetingType: internalMeetingType,
        });
      });

      // Title should update to internal meeting format
      expect(result.current.title).toBe("Discussion with John Doe");
    });

    it("should update title when attendees change", () => {
      const newAttendees: AttendeeOptions = [
        { uuid: "attendee-2", name: "Jane Smith", type: "client" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: undefined,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      // Initial title with John Doe
      expect(result.current.title).toBe("Meeting with John Doe");

      // Change attendees to Jane Smith
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: false,
          userId,
          attendees: newAttendees,
          meetingType: clientMeetingType,
        });
      });

      // Title should update to reflect new attendee
      expect(result.current.title).toBe("Meeting with Jane Smith");
    });

    it("should update title when both meeting type and attendees change", () => {
      const newAttendees: AttendeeOptions = [
        { uuid: "attendee-2", name: "Jane Smith", type: "user" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: undefined,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      // Initial title for client meeting with John Doe
      expect(result.current.title).toBe("Meeting with John Doe");

      // Change both meeting type to internal and attendees to Jane Smith
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: false,
          userId,
          attendees: newAttendees,
          meetingType: internalMeetingType,
        });
      });

      // Title should update to internal meeting format with new attendee
      expect(result.current.title).toBe("Discussion with Jane Smith");
    });
  });

  describe("Edge case 1 - initialMeetingTitle provided", () => {
    it("should NOT update title when initialMeetingTitle is provided, even if meeting type changes", () => {
      const initialTitle = "Custom Initial Title";

      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: initialTitle,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      expect(result.current.title).toBe(initialTitle);

      // Change meeting type - title should NOT update
      act(() => {
        rerender({
          initialMeetingTitle: initialTitle,
          isTitleUserEdited: false,
          userId,
          attendees: defaultAttendees,
          meetingType: internalMeetingType,
        });
      });

      expect(result.current.title).toBe(initialTitle);
    });

    it("should NOT update title when initialMeetingTitle is provided, even if attendees change", () => {
      const initialTitle = "Custom Initial Title";
      const newAttendees: AttendeeOptions = [
        { uuid: "attendee-2", name: "Jane Smith", type: "client" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: initialTitle,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      expect(result.current.title).toBe(initialTitle);

      // Change attendees - title should NOT update
      act(() => {
        rerender({
          initialMeetingTitle: initialTitle,
          isTitleUserEdited: false,
          userId,
          attendees: newAttendees,
          meetingType: clientMeetingType,
        });
      });

      expect(result.current.title).toBe(initialTitle);
    });
  });

  describe("Edge case 2 - user edited title", () => {
    it("should NOT update title when isTitleUserEdited is true, even if meeting type changes", () => {
      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: undefined,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      expect(result.current.title).toBe("Meeting with John Doe");

      // Simulate user editing the title
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: true,
          userId,
          attendees: defaultAttendees,
          meetingType: clientMeetingType,
        });
      });

      // Now change meeting type - title should NOT update
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: true,
          userId,
          attendees: defaultAttendees,
          meetingType: internalMeetingType,
        });
      });

      expect(result.current.title).toBe("Meeting with John Doe");
    });

    it("should NOT update title when isTitleUserEdited is true, even if attendees change", () => {
      const newAttendees: AttendeeOptions = [
        { uuid: "attendee-2", name: "Jane Smith", type: "client" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: undefined,
            isTitleUserEdited: false,
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      expect(result.current.title).toBe("Meeting with John Doe");

      // Simulate user editing the title
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: true,
          userId,
          attendees: defaultAttendees,
          meetingType: clientMeetingType,
        });
      });

      // Now change attendees - title should NOT update
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: true,
          userId,
          attendees: newAttendees,
          meetingType: clientMeetingType,
        });
      });

      expect(result.current.title).toBe("Meeting with John Doe");
    });

    it("should allow manual title updates even when isTitleUserEdited is true", () => {
      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: undefined,
            isTitleUserEdited: false, // Start with false to get initial title
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      expect(result.current.title).toBe("Meeting with John Doe");

      // Manually set title (simulating user input)
      act(() => {
        result.current.setTitle("User Edited Title");
      });

      expect(result.current.title).toBe("User Edited Title");

      // Now set isTitleUserEdited to true and change meeting type - title should still NOT auto-update
      act(() => {
        rerender({
          initialMeetingTitle: undefined,
          isTitleUserEdited: true,
          userId,
          attendees: defaultAttendees,
          meetingType: internalMeetingType,
        });
      });

      expect(result.current.title).toBe("User Edited Title");
    });
  });

  describe("Combined edge cases", () => {
    it("should prioritize initialMeetingTitle over isTitleUserEdited flag", () => {
      const initialTitle = "Initial Title from Query";

      const { result, rerender } = renderHook(
        (props) => useMeetingTitleUpdate(props),
        {
          initialProps: {
            initialMeetingTitle: initialTitle,
            isTitleUserEdited: true, // Both conditions are true
            userId,
            attendees: defaultAttendees,
            meetingType: clientMeetingType,
          },
        }
      );

      expect(result.current.title).toBe(initialTitle);

      // Change meeting type - title should NOT update due to initialMeetingTitle
      act(() => {
        rerender({
          initialMeetingTitle: initialTitle,
          isTitleUserEdited: true,
          userId,
          attendees: defaultAttendees,
          meetingType: internalMeetingType,
        });
      });

      expect(result.current.title).toBe(initialTitle);
    });
  });
});
