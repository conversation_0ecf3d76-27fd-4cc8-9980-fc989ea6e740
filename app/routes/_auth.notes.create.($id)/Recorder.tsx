import React, {
  ReactNode,
  createContext,
  use<PERSON>allback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useWakeLock } from "react-screen-wake-lock";
import { Mi<PERSON>, Pause, RefreshCcw } from "lucide-react";
import { Typography } from "~/@ui/Typography";
import { cn } from "~/@shadcn/utils";
import { Button, ButtonProps } from "~/@shadcn/ui/button";
import { Card } from "~/@shadcn/ui/card";
import MicrophoneSelector from "~/@ui/MicrophoneSelector";
import { toast } from "react-toastify";
import { datadogLogs } from "@datadog/browser-logs";

// Fragments
const Pulser = ({ active = false }: { active?: boolean }) => (
  <span className="relative mr-2 inline-flex h-3 w-3">
    {active && (
      <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-destructive opacity-75" />
    )}
    <span
      className={cn(
        "relative inline-flex h-3 w-3 rounded-full",
        active ? "bg-destructive" : "bg-secondary"
      )}
    />
  </span>
);

const RecordingTime = ({
  isRecording = false,
  isPaused = false,
  seconds,
}: {
  isRecording?: boolean;
  isPaused?: boolean;
  seconds: number;
}) => {
  const minutesString = String(Math.floor(seconds / 60)).padStart(2, "0");
  const secondsString = String(seconds % 60).padStart(2, "0");
  return (
    <>
      <Typography>
        <Pulser active={isRecording && !isPaused} />
        <span>
          {minutesString}:{secondsString}
        </span>
      </Typography>
      <Typography color="secondary" variant="body2">
        <span>
          {!isRecording && "Not started"}
          {isRecording && !isPaused && "In progress"}
          {isRecording && isPaused && "Paused"}
        </span>
      </Typography>
    </>
  );
};

const RecordButton = ({ label, ...props }: ButtonProps & { label: string }) => (
  <Button className="w-28" variant="destructive" {...props}>
    <Mic />
    {label}
  </Button>
);

const PauseButton = (props: ButtonProps) => (
  <Button className="w-28" variant="destructive" {...props}>
    <Pause />
    Pause
  </Button>
);

const ResetButton = (props: ButtonProps) => (
  <Button variant="ghost-destructive" {...props}>
    Reset
    <RefreshCcw className="!h-5 !w-5" />
  </Button>
);

// Exports
type RecorderControls = {
  isPaused: boolean;
  isRecording: boolean;
  recordingTime: number;
  recordingBlob?: Blob | null;
  recordingFragments: Blob[];
  micDevice: string;
  volume: number;
  startRecordingWithWakeLock: (fragmentMilliseconds?: number) => void;
  stopRecordingWithWakeLock: () => void;
  togglePauseResumeWithWakeLock: () => void;
  resetRecorder: () => void;
  setMicDevice: React.Dispatch<React.SetStateAction<string>>;
};

export const RecorderContext = createContext<RecorderControls>({
  isPaused: false,
  isRecording: false,
  recordingTime: 0,
  recordingBlob: undefined,
  recordingFragments: [],
  micDevice: "",
  volume: 0,
  startRecordingWithWakeLock: () => undefined,
  stopRecordingWithWakeLock: () => undefined,
  togglePauseResumeWithWakeLock: () => undefined,
  resetRecorder: () => undefined,
  setMicDevice: () => undefined,
});

export const RecorderProvider = ({ children }: { children: ReactNode }) => {
  const wakeLock = useWakeLock({
    onRequest: () => datadogLogs.logger.info("Wake lock requested"),
    onRelease: () => datadogLogs.logger.info("Wake lock released"),
    onError: (error) => datadogLogs.logger.error("Wake lock error:", {}, error),
  });
  const [micDevice, setMicDevice] = useState<string>("");
  const [volume, setVolume] = useState(0);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingBlob, setRecordingBlob] = useState<Blob | null>(null);
  const [recordingFragments, setRecordingFragments] = useState<Blob[]>([]);
  const [recordingTime, setRecordingTime] = useState(0);
  const [timer, setTimer] = useState<ReturnType<typeof setInterval> | null>(
    null
  );
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);

  // This effect checks if there has been 5 seconds of continuous low volume after recording starts.
  // If so, it shows a toast to the user.
  // We use a ref to track when low volume first occurred.
  const lowVolumeStartRef = useRef<number | null>(null);
  useEffect(() => {
    if (!isRecording || isPaused) {
      lowVolumeStartRef.current = null;
      return;
    }

    if (recordingTime < 5) return;
    const volumeThreshold = 1;

    if (volume < volumeThreshold) {
      if (!lowVolumeStartRef.current) {
        lowVolumeStartRef.current = Date.now();
      } else {
        const elapsed = Date.now() - lowVolumeStartRef.current;
        if (elapsed >= 5000) {
          toast.warn("No audio detected. Please check your microphone.", {
            toastId: "no-audio-sec",
          });
          lowVolumeStartRef.current = null;
        }
      }
    } else {
      lowVolumeStartRef.current = null;
    }
  }, [volume, isRecording, isPaused, recordingTime]);

  const startRecording = useCallback(
    (fragmentMilliseconds?: number) => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert("Media Devices API not supported in this browser.");
        return;
      }

      navigator.mediaDevices
        .getUserMedia({ audio: { deviceId: micDevice } })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          const chunks: BlobPart[] = [];
          recorder.ondataavailable = (event) => {
            if (event.data.size === 0) return;
            chunks.push(event.data);
            setRecordingFragments((prev) => [...prev, event.data]);
          };

          recorder.onstop = () => {
            const blob = new Blob(chunks, { type: "audio/webm" });
            setRecordingBlob(blob);
            setRecordingTime(0);
            setIsRecording(false);
          };

          recorder.start(fragmentMilliseconds);
          setIsRecording(true);

          const audioContext = new AudioContext();
          const analyser = audioContext.createAnalyser();
          const microphone = audioContext.createMediaStreamSource(stream);
          analyser.fftSize = 2048;
          analyser.smoothingTimeConstant = 0.8;
          microphone.connect(analyser);
          setAudioContext(audioContext);
          setAudioStream(stream);

          const processVolume = () => {
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(dataArray);
            const sum = dataArray.reduce((a, b) => a + b, 0);
            const averageVolume = sum / dataArray.length;
            setVolume(Math.min(Math.round(averageVolume), 100));
            requestAnimationFrame(processVolume);
          };

          processVolume();

          const interval = setInterval(() => {
            setRecordingTime((prev) => prev + 1);
          }, 1000);
          setTimer(interval);
        })
        .catch((error) => {
          datadogLogs.logger.error("Error accessing microphone:", {}, error);
        });
    },
    [micDevice]
  );

  const stopRecording = useCallback(() => {
    if (mediaRecorder && mediaRecorder.state !== "inactive") {
      mediaRecorder.stop();
    }

    // Close audio context and stop stream
    if (audioStream) {
      audioStream.getTracks().forEach((track) => track.stop());
    }
    if (audioContext) {
      audioContext.close();
    }

    clearInterval(timer!);
    setTimer(null);
  }, [mediaRecorder, audioContext, audioStream, timer]);

  const togglePauseResume = useCallback(() => {
    if (mediaRecorder) {
      if (mediaRecorder.state === "paused") {
        mediaRecorder.resume();
        setIsPaused(false);

        // Restart the timer when resuming
        const interval = setInterval(() => {
          setRecordingTime((prev) => prev + 1);
        }, 1000);
        setTimer(interval);
      } else if (mediaRecorder.state === "recording") {
        mediaRecorder.pause();
        setIsPaused(true);

        // Clear the interval when pausing
        clearInterval(timer!);
        setTimer(null);
      }
    }
  }, [mediaRecorder, timer]);

  const resetRecorder = useCallback(() => {
    setRecordingTime(0);
    setIsRecording(false);
    setIsPaused(false);
    setMediaRecorder(null);
    setAudioStream(null);
    setAudioContext(null);
    setTimeout(() => {
      setRecordingFragments([]);
      setRecordingBlob(null);
    }, 0);
  }, []);

  const contextValue = useMemo(() => {
    const playStartSound = () => new Audio("/sounds/start.mp3").play();
    const playStopSound = () => new Audio("/sounds/stop.mp3").play();
    return {
      isPaused,
      isRecording,
      recordingTime,
      recordingBlob,
      recordingFragments,
      micDevice,
      volume,
      startRecordingWithWakeLock: (fragmentMilliseconds?: number) => {
        if (wakeLock.isSupported) wakeLock.request();
        setRecordingTime(0);
        startRecording(fragmentMilliseconds);
        playStartSound();
      },
      stopRecordingWithWakeLock: () => {
        if (wakeLock.isSupported) wakeLock.release();
        if (isRecording && !isPaused) playStopSound();
        stopRecording();
      },
      togglePauseResumeWithWakeLock: () => {
        if (isPaused) playStartSound();
        else if (isRecording) playStopSound();
        togglePauseResume();
      },
      resetRecorder: () => {
        if (wakeLock.isSupported) wakeLock.release();
        stopRecording();
        resetRecorder();
      },
      setMicDevice,
    };
  }, [
    micDevice,
    isPaused,
    isRecording,
    recordingBlob,
    recordingFragments,
    recordingTime,
    resetRecorder,
    startRecording,
    stopRecording,
    togglePauseResume,
    wakeLock,
    volume,
  ]);

  return (
    <RecorderContext.Provider value={contextValue}>
      {children}
    </RecorderContext.Provider>
  );
};

export const RecorderCard = ({
  onStartRecording,
  needConsent,
  fragmentMilliseconds,
}: {
  onStartRecording: () => void;
  needConsent: boolean;
  fragmentMilliseconds?: number;
}) => {
  const {
    isPaused,
    isRecording,
    recordingTime,
    micDevice,
    resetRecorder,
    startRecordingWithWakeLock,
    togglePauseResumeWithWakeLock,
    setMicDevice,
    volume,
    recordingBlob,
  } = useContext(RecorderContext);
  const [hasConsent, setHasConsent] = useState(false);

  const onMicDeviceChange = useCallback(
    (deviceId: string) => {
      setMicDevice(deviceId);
    },
    [setMicDevice]
  );

  return (
    <Card
      compact
      className={cn("relative grow flex-row items-center gap-3 self-stretch")}
    >
      <div className="flex flex-col gap-3">
        {needConsent && (
          <div className="flex gap-3">
            <Typography variant="body2" className="flex">
              Consent Received
            </Typography>
            <input
              type="checkbox"
              checked={hasConsent}
              onChange={(e) => setHasConsent(e.target.checked)}
            />
          </div>
        )}
        {(!needConsent || (needConsent && hasConsent)) && (
          <div className="flex select-none flex-col items-start gap-3 text-card-foreground">
            <MicrophoneSelector
              onDeviceChange={onMicDeviceChange}
              selectedDevice={micDevice}
              disabled={recordingTime > 0 || isRecording}
            />
            <div className="flex flex-col gap-3">
              {isRecording && !isPaused && (
                <div className="mt-2 h-2 flex-grow overflow-hidden rounded bg-gray-300">
                  <div
                    className="h-full bg-primary transition-all duration-100 ease-in-out"
                    style={{ width: `${volume * 2}%` }} // Multiplied by 2 to make it easier to see volume changes
                  />
                </div>
              )}
              {recordingBlob ? (
                <RecordingTime
                  isRecording={isRecording}
                  isPaused={isPaused}
                  seconds={recordingTime}
                />
              ) : (
                <div className="flex select-none flex-row items-center gap-3 text-card-foreground">
                  {!isRecording ? (
                    <RecordButton
                      label="Start"
                      onClick={() => {
                        onStartRecording();
                        startRecordingWithWakeLock(fragmentMilliseconds);
                      }}
                    />
                  ) : isPaused ? (
                    <RecordButton
                      label="Resume"
                      onClick={() => togglePauseResumeWithWakeLock()}
                    />
                  ) : (
                    <PauseButton onClick={togglePauseResumeWithWakeLock} />
                  )}
                  <div className="flex w-full grow flex-col">
                    <RecordingTime
                      isRecording={isRecording}
                      isPaused={isPaused}
                      seconds={recordingTime}
                    />
                  </div>
                  <div className="flex shrink-0">
                    {isRecording && <ResetButton onClick={resetRecorder} />}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};
