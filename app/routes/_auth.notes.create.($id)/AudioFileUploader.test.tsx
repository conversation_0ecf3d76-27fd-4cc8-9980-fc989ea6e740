import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { AudioFileUploader } from "./AudioFileUploader";

describe("AudioFileUploader", () => {
  const mockSetTouched = vi.fn();
  const mockOnFileUploaded = vi.fn();

  const label = "The note will be saved when you select a file.";

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render audio file input when consent is not needed", () => {
    render(
      <AudioFileUploader
        needConsent={false}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    expect(screen.getByLabelText(label)).toBeInTheDocument();
    expect(screen.queryByText("Consent Received")).not.toBeInTheDocument();
  });

  it("should render consent checkbox when consent is needed", () => {
    render(
      <AudioFileUploader
        needConsent={true}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    expect(screen.getByText("Consent Received")).toBeInTheDocument();
    expect(screen.getByRole("checkbox")).toBeInTheDocument();
    expect(screen.queryByLabelText(label)).not.toBeInTheDocument();
  });

  it("should not render consent checkbox when consent is not needed", () => {
    render(
      <AudioFileUploader
        needConsent={false}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    expect(screen.queryByText("Consent Received")).not.toBeInTheDocument();
    expect(screen.queryByText("checkbox")).not.toBeInTheDocument();
    expect(screen.getByLabelText(label)).toBeInTheDocument();
  });

  it("should show audio file input after consent is given", async () => {
    const user = userEvent.setup();
    render(
      <AudioFileUploader
        needConsent={true}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    const checkbox = screen.getByRole("checkbox");
    await user.click(checkbox);

    expect(screen.getByLabelText(label)).toBeInTheDocument();
  });

  it("should call setTouched and onFileUploaded when file is selected", async () => {
    const user = userEvent.setup();
    render(
      <AudioFileUploader
        needConsent={false}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    const file = new File(["audio content"], "test.mp3", {
      type: "audio/mpeg",
    });
    const fileInput = screen.getByLabelText(label);

    await user.upload(fileInput, file);

    expect(mockSetTouched).toHaveBeenCalledWith(true);
    expect(mockOnFileUploaded).toHaveBeenCalledWith(file);
  });

  it("should not call onFileUploaded when unsupported file type is selected", async () => {
    const user = userEvent.setup({ applyAccept: false });
    render(
      <AudioFileUploader
        needConsent={false}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    const unsupportedFile = new File(["text content"], "test.txt", {
      type: "text/plain",
    });
    const fileInput = screen.getByLabelText(label);

    await user.upload(fileInput, unsupportedFile);

    expect(mockSetTouched).toHaveBeenCalledWith(true);
    expect(mockOnFileUploaded).not.toHaveBeenCalled();
  });

  it("should not call onFileUploaded when no file is selected", async () => {
    const user = userEvent.setup({ applyAccept: false });
    render(
      <AudioFileUploader
        needConsent={false}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    // Upload a file first so that the upload with an empty
    // file list triggers a change.
    const fileInput = screen.getByLabelText(label);
    const file = new File(["audio content"], "test.mp3", {
      type: "audio/mpeg",
    });
    await user.upload(fileInput, [file]);
    vi.clearAllMocks();

    // Upload the empty file.
    await user.upload(fileInput, []);

    expect(mockSetTouched).toHaveBeenCalledWith(true);
    expect(mockOnFileUploaded).not.toHaveBeenCalled();
  });

  it("handles multiple files correctly", async () => {
    const user = userEvent.setup();
    render(
      <AudioFileUploader
        needConsent={false}
        setTouched={mockSetTouched}
        onFileUploaded={mockOnFileUploaded}
      />
    );

    const file1 = new File(["audio content 1"], "test1.mp3", {
      type: "audio/mpeg",
    });
    const file2 = new File(["audio content 2"], "test2.mp3", {
      type: "audio/mpeg",
    });
    const fileInput = screen.getByLabelText(label);

    await user.upload(fileInput, [file1, file2]);

    expect(mockSetTouched).toHaveBeenCalledWith(true);
    expect(mockOnFileUploaded).toHaveBeenCalledOnce();
    expect(mockOnFileUploaded).toHaveBeenCalledWith(file1);
  });
});
