import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Title,
} from "~/@shadcn/ui/dialog";
import { MeetingTypeSelector } from "~/routes/_auth.notes.create.($id)/components/MeetingTypeSelector";
import { MeetingType } from "~/api/openapi/generated";
import { Button } from "~/@shadcn/ui/button";
import { Spinner } from "~/@ui/assets/Spinner";

interface MeetingTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (meetingType: MeetingType) => void;
  meetingTypes: MeetingType[];
  meetingType: MeetingType;
  setMeetingType: (meetingType: MeetingType) => void;
  isLoading: boolean;
}

const MeetingTypeModal: React.FC<MeetingTypeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  meetingTypes,
  meetingType,
  setMeetingType,
  isLoading,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate prep</DialogTitle>
        </DialogHeader>
        <MeetingTypeSelector
          meetingType={meetingType}
          setMeetingType={setMeetingType}
          meetingTypes={meetingTypes}
          label="Select a meeting type to generate meeting prep"
        />
        <DialogFooter>
          <Button onClick={() => onSave(meetingType)} disabled={isLoading}>
            {isLoading && <Spinner />}
            Save & generate prep
          </Button>
          <Button onClick={onClose} variant="ghost" disabled={isLoading}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MeetingTypeModal;
