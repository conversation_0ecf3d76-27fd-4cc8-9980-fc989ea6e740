const tutorialSteps = [
  {
    title: "Welcome to Your Notes Page",
    intro:
      "Here’s where all your meeting notes live and where you can create a meeting. Create, review, edit, sync, or delete notes as needed.",
  },
  {
    title: "Meeting Tabs",
    element: "[data-onboarding='meeting-tabs']",
    intro:
      "Before a meeting, you’ll see Meeting Details and Meeting Prep. Meeting Details shows key info like the meeting title, attendees, and time. Making it easy to confirm you’re in the right place! Meeting Prep is where you can add talking points, review past notes, and get yourself ready.",
  },
  {
    title: "Meeting Type",
    element: "#meetingType",
    intro:
      "Every meeting needs a type! This helps <PERSON><PERSON><PERSON> tailor your Meeting Prep. Meetings default to Client, but you can choose from any available type in the dropdown. Just make sure to select one before getting started with prep.",
  },
  {
    title: "Who’s attending?",
    element: "#attendees",
    intro:
      "When your calendar is connected, everyone you’ve invited will automatically appear here. Need to add someone? Just select them from the dropdown, start typing their name, or enter their email address manually.",
  },
  {
    title: "Audio Source",
    element: "[data-onboarding='audio-source']",
    intro:
      "<PERSON><PERSON><PERSON> can take notes no matter how you meet. Capture conversations live with a mic call, within virtual platforms like Zoom, Webex, Teams, or Google Meet, or let <PERSON><PERSON><PERSON> call you for a conference call. Have a recording instead? You can also upload audio files, and <PERSON><PERSON>lyn will generate notes for you.",
  },
  {
    title: "Sending the Notetaker",
    element: "#send-notetaker",
    hasDynamicElement: true,
    intro:
      "Zeplyn can auto-join your virtual meetings, but you also have the option to send the Notetaker manually. Just enter the meeting link, and <PERSON><PERSON>lyn will join and start capturing notes for you.",
  },
];

export default tutorialSteps;
