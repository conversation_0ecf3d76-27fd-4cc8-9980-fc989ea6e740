import { describe, it, expect } from "vitest";
import { MeetingCategory, MeetingType } from "~/api/openapi/generated";
import { AttendeeOptions } from "~/api/attendees/types";
import getMeetingNameFromAttendees from "./getMeetingNameFromAttendees";

describe("getMeetingNameFromAttendees", () => {
  const userId = "user-123";

  const createMeetingType = (category: MeetingCategory): MeetingType => ({
    uuid: `${category}-uuid`,
    name: `${category} Meeting`,
    category,
    isShared: true,
  });

  const clientMeetingType = createMeetingType(MeetingCategory.Client);
  const internalMeetingType = createMeetingType(MeetingCategory.Internal);
  const debriefMeetingType = createMeetingType(MeetingCategory.Debrief);

  describe("with attendees present", () => {
    it("should generate client meeting title with single attendee", () => {
      const attendees: AttendeeOptions = [
        { uuid: "attendee-1", name: "<PERSON>", type: "client" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, clientMeetingType);
      expect(result).toBe("Meeting with <PERSON> Doe");
    });

    it("should generate internal meeting title with multiple attendees", () => {
      const attendees: AttendeeOptions = [
        { uuid: "attendee-1", name: "Alice Smith", type: "user" },
        { uuid: "attendee-2", name: "Bob Johnson", type: "user" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, internalMeetingType);
      expect(result).toBe("Discussion with Alice Smith and Bob Johnson");
    });

    it("should generate debrief meeting title with three attendees", () => {
      const attendees: AttendeeOptions = [
        { uuid: "attendee-1", name: "Alice", type: "client" },
        { uuid: "attendee-2", name: "Bob", type: "user" },
        { uuid: "attendee-3", name: "Carol", type: "user" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, debriefMeetingType);
      expect(result).toBe("Debrief for Alice, Bob and Carol");
    });

    it("should filter out current user from attendee list", () => {
      const attendees: AttendeeOptions = [
        { uuid: userId, name: "Current User", type: "user" },
        { uuid: "attendee-1", name: "John Doe", type: "client" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, clientMeetingType);
      expect(result).toBe("Meeting with John Doe");
    });
  });

  describe("with no other attendees", () => {
    it("should use 'a client' fallback for client meetings", () => {
      const attendees: AttendeeOptions = [
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, clientMeetingType);
      expect(result).toBe("Meeting with a client");
    });

    it("should use 'a client' fallback for debrief meetings", () => {
      const attendees: AttendeeOptions = [
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, debriefMeetingType);
      expect(result).toBe("Debrief for a client");
    });

    it("should use 'colleagues' fallback for internal meetings", () => {
      const attendees: AttendeeOptions = [
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, internalMeetingType);
      expect(result).toBe("Discussion with colleagues");
    });
  });

  describe("with no attendees", () => {
    it("should handle empty attendees array", () => {
      const attendees: AttendeeOptions = [];

      const result = getMeetingNameFromAttendees(userId, attendees, clientMeetingType);
      expect(result).toBe("Meeting with a client");
    });
  });

  describe("with unknown meeting type category", () => {
    it("should use default 'Meeting with' prefix for unknown category", () => {
      const unknownMeetingType = {
        uuid: "unknown-uuid",
        name: "Unknown Meeting",
        category: "unknown" as MeetingCategory,
        isShared: true,
      };

      const attendees: AttendeeOptions = [
        { uuid: "attendee-1", name: "John Doe", type: "client" },
        { uuid: userId, name: "Current User", type: "user" },
      ];

      const result = getMeetingNameFromAttendees(userId, attendees, unknownMeetingType);
      expect(result).toBe("Meeting with John Doe");
    });
  });
});
