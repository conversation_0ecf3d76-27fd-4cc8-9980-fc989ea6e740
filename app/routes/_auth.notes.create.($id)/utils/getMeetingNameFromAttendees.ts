import { AttendeeOptions } from "~/api/attendees/types";
import { MeetingType } from "~/api/openapi/generated";

import { MEETING_TYPES } from "~/routes/_auth.notes.create.($id)/types";

export const getMeetingNameFromAttendees = (
  userId: string,
  attendees: AttendeeOptions,
  meetingType: MeetingType
): string => {
  // Ensure attendees is an array; if undefined, default to an empty array
  const filteredAttendees = (attendees ?? []).filter(
    (item) => item.uuid !== userId
  );

  let attendeeString: string;

  if (filteredAttendees.length > 0) {
    // Join attendee names into a sentence-like string
    attendeeString = filteredAttendees
      .map((item) => item.name)
      .join(", ")
      .replace(/,([^,]*)$/, " and$1");
  } else {
    // Fallback for when there are no other attendees
    attendeeString =
      meetingType.category === "debrief" || meetingType.category === "client"
        ? "a client"
        : "colleagues";
  }

  // Get the title prefix based on the meeting type
  const titlePrefix =
    MEETING_TYPES[meetingType.category]?.titlePrefix || "Meeting with";

  // Return the formatted meeting name
  return `${titlePrefix} ${attendeeString}`;
};

export default getMeetingNameFromAttendees;
