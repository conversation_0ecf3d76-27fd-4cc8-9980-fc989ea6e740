import { z } from "zod";
import { MeetingCategory } from "~/api/openapi/generated";

export const MEETING_TYPES = {
  [MeetingCategory.Client]: {
    titlePrefix: "Meeting with",
  },
  [MeetingCategory.Internal]: {
    titlePrefix: "Discussion with",
  },
  [MeetingCategory.Debrief]: {
    titlePrefix: "Debrief for",
  },
};

export const CreateNoteFormStruct = z.object({
  fileContent: z.instanceof(Blob).optional(),
  fileType: z.string().optional(),
  meetingType: z.string().optional(),
  attendees: z.string().optional(),
  meetingLink: z.string().optional(),
  meetingName: z.string().optional(),
  meetingSourceID: z.string().optional(),
  scheduledEventUUID: z.string().optional(),
  action: z.string().optional(),
  botId: z.string().optional(),
  interactionId: z.string().optional(),
  startTime: z.coerce.date().optional(),
  endTime: z.coerce.date().optional(),
  sequenceNumber: z.coerce.number().optional(),
  fragmentsNonce: z.string().optional(),
  linkedCRMEntityID: z.string().optional(),
  linkedCRMEntityName: z.string().optional(),
});
