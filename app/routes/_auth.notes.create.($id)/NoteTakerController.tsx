import React, { useState } from "react";
import { Phone, SendHorizonal, Link } from "lucide-react";

import { ActionTypes } from "~/utils/const";
import { BotMeetingType, BotStatus } from "~/api/openapi/generated";
import { <PERSON><PERSON> } from "~/@shadcn/ui/button";
import { Card } from "~/@shadcn/ui/card";
import { Input, inputWrapperVariants } from "~/@shadcn/ui/input";
import { Spinner } from "~/@ui/assets/Spinner";
import { Typography } from "~/@ui/Typography";
import { cn } from "~/@shadcn/utils";
import { Form, useSubmit } from "react-router";
import { useFuzzySearchList } from "@nozbe/microfuzz/react.js";
import PhoneInput from "react-phone-number-input";

// Messages to display in the notetaker controller per bot type.
const messagesByBotType = {
  [BotMeetingType.PhoneCall]: {
    starting: "Starting Recording",
    stopping: "Stopping Recording",
    enterValidLink: "Please input a valid phone number",
    sendingBot: "Setting up the call",
    leavingMeeting: "Ending the call",
    start: "Start Recording",
    stop: "Stop Recording",
    finish: "End Call",
    sendDescription: "Record a phone call",
    sending: "Calling",
    send: "Call",
    sendInProgress: "Call in progress",
  },
  [BotMeetingType.VideoCall]: {
    starting: "Starting Notetaking",
    stopping: "Stopping Notetaking",
    enterValidLink: "Please input a valid meeting link",
    sendingBot: "Sending Notetaker to the meeting",
    leavingMeeting: "Notetaker is leaving the meeting",
    start: "Start Notetaking",
    stop: "Stop Notetaking",
    finish: "Leave Meeting",
    sendDescription: "Send the notetaker to your video meeting",
    sending: "Sending Notetaker",
    send: "Send Notetaker",
    sendInProgress: "Notetaker sent",
  },
};

export type NoteTakerControllerProps = {
  botId: string;
  botStatus: BotStatus | undefined;
  meetingLink: string;
  setMeetingLink: (string: string) => void;
  meetingLinkSuggestions: { link: string; name: string }[];
  onTouched: () => void;
  needConsent: boolean;
  sendBotAndCreateNote: () => void;
  saveNoteTaking: () => void;
  spinnerCTA: string;
  setSpinnerCTA: (string: string) => void;
  supportsPauseResume: boolean;
  botType: BotMeetingType;
  botOperationLoading: boolean;
};

export const NoteTakerController = ({
  botId,
  botStatus,
  meetingLink,
  setMeetingLink,
  meetingLinkSuggestions,
  onTouched,
  needConsent,
  sendBotAndCreateNote,
  saveNoteTaking,
  spinnerCTA,
  setSpinnerCTA,
  supportsPauseResume,
  botType,
  botOperationLoading,
}: NoteTakerControllerProps) => {
  const [hasConsent, setHasConsent] = useState(
    (botStatus &&
      botStatus !== BotStatus.Unknown &&
      botStatus !== BotStatus.NotCreated) ||
      false
  );
  const isProcessing =
    botOperationLoading ||
    botStatus == BotStatus.Scheduled ||
    botStatus == BotStatus.InWaitingRoom;

  const filteredLinkSuggestions = useFuzzySearchList({
    list: meetingLinkSuggestions,
    queryText: meetingLink,
    getText: (o) => [o.link, o.name],
    mapResultItem: ({ item, score, matches: [highlightRanges] }) => ({
      item,
      highlightRanges,
    }),
  });
  const isPhoneCall = botType == BotMeetingType.PhoneCall;
  const messages = messagesByBotType[botType];

  const submit = useSubmit();
  const startRecording = () => {
    const formData = new FormData();
    formData.append("action", ActionTypes.RESUME_RECORDING);
    botId && formData.append("botId", botId);
    submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
    setSpinnerCTA(messages.starting);
  };

  const stopRecording = () => {
    const formData = new FormData();
    formData.append("action", ActionTypes.PAUSE_RECORDING);
    formData.append("botId", botId);
    submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
    setSpinnerCTA(messages.stopping);
  };

  const sendBot: React.MouseEventHandler<HTMLButtonElement> = (event) => {
    if (!meetingLink) return alert(messages.enterValidLink);
    setSpinnerCTA(messages.sendingBot);
    sendBotAndCreateNote();
  };

  const onLeaveMeeting = () => {
    setSpinnerCTA(messages.leavingMeeting);
    saveNoteTaking();
  };

  const getButtons = () => (
    <div>
      {supportsPauseResume && botStatus === BotStatus.InCallNotRecording ? (
        <>
          <Button
            className="w-35 mr-2"
            variant="destructive"
            type="submit"
            name="intent"
            value="start_recording"
            onClick={(e) => {
              onTouched();
              startRecording();
            }}
            disabled={isProcessing}
          >
            {messages.start}
          </Button>
        </>
      ) : supportsPauseResume && botStatus === BotStatus.InCallRecording ? (
        <>
          <Button
            className="w-35 mr-2"
            type="submit"
            name="intent"
            value="Stop Notetaking"
            onClick={(e) => {
              onTouched();
              stopRecording();
            }}
            disabled={isProcessing}
          >
            {messages.stop}
          </Button>
        </>
      ) : null}
      {checkIfBotIsInCall() && (
        <Button type="submit" onClick={onLeaveMeeting} disabled={isProcessing}>
          {messages.finish}
        </Button>
      )}
    </div>
  );

  const checkIfBotIsInCall = () => {
    return (
      botStatus === BotStatus.InCallNotRecording ||
      botStatus === BotStatus.InCallRecording
    );
  };

  const [isInputFocused, setIsInputFocused] = useState(false);

  const getMeetingLinkInput = () => (
    <div>
      <div className="relative">
        {isPhoneCall ? (
          <Form>
            <PhoneInput
              defaultCountry="US"
              placeholder="Enter phone number..."
              className={cn(
                inputWrapperVariants(),
                "flex w-full bg-transparent shadow-none transition-colors placeholder:text-secondary hover:bg-transparent focus-visible:outline-none has-[input:focus]:ring-0"
              )}
              numberInputProps={{
                className:
                  "flex w-full bg-transparent shadow-none hover:bg-transparent has-[input:focus]:ring-0 transition-colors placeholder:text-secondary focus-visible:outline-none",
              }}
              disabled={isProcessing || checkIfBotIsInCall()}
              value={meetingLink}
              onChange={(value) => {
                setMeetingLink(value ?? "");
                onTouched();
              }}
              onFocus={() => setIsInputFocused(true)}
              onBlur={() => setIsInputFocused(false)}
            />
          </Form>
        ) : (
          <Input
            placeholder="Paste video meeting link..."
            className="flex w-full bg-transparent shadow-none hover:bg-transparent has-[input:focus]:ring-0"
            leftIcon={<Link />}
            disabled={isProcessing || checkIfBotIsInCall()}
            value={meetingLink}
            onChange={(event) => {
              setMeetingLink(event.currentTarget.value);
              onTouched();
            }}
            onFocus={() => setIsInputFocused(true)}
            onBlur={() => setIsInputFocused(false)}
          />
        )}
        {isInputFocused && filteredLinkSuggestions.length > 0 && (
          <div className="absolute z-10 mt-2 rounded-md border border-gray-300 bg-white shadow-lg">
            <ul className="max-h-60 overflow-y-auto">
              {filteredLinkSuggestions.map((item, index) => (
                <li
                  key={index}
                  className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                  onMouseDown={() => setMeetingLink(item.item.link)}
                >
                  {item.item.link} ({item.item.name})
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <div className="mt-2 flex items-center">
        <div className="mr-3">{messages.sendDescription}</div>
        <input type="hidden" name="action" />
        <Button
          id="send-notetaker"
          type="submit"
          disabled={isProcessing || checkIfBotIsInCall()}
          onClick={(e) => sendBot(e)}
        >
          {!checkIfBotIsInCall()
            ? isProcessing
              ? messages.sending
              : messages.send
            : messages.sendInProgress}
          {!checkIfBotIsInCall() ? (
            isProcessing ? (
              <Spinner />
            ) : isPhoneCall ? (
              <Phone className="!h-5 !w-5" />
            ) : (
              <SendHorizonal className="!h-5 !w-5" />
            )
          ) : null}
        </Button>
      </div>
    </div>
  );

  const getConsentBox = () => (
    <div className="flex w-full  items-center justify-between">
      <div className="flex gap-3">
        <Typography variant="body2" className="flex ">
          Consent Received
        </Typography>
        <input
          type="checkbox"
          checked={hasConsent}
          onChange={(e) => setHasConsent(e.target.checked)}
        />
      </div>
      {isProcessing && (
        <div className="flex items-center">
          {isProcessing && spinnerCTA} &nbsp;
          <Spinner />
        </div>
      )}
    </div>
  );

  return (
    <Card
      compact
      className={cn("relative grow flex-row items-center gap-3 self-stretch")}
    >
      <div className="flex w-full flex-col space-y-4">
        {needConsent && getConsentBox()}
        {(!needConsent || hasConsent) && getMeetingLinkInput()}
        {(!needConsent || hasConsent) && getButtons()}
      </div>
    </Card>
  );
};
