import { useState } from "react";
import { Typography } from "~/@ui/Typography";
import { Card } from "~/@shadcn/ui/card";

export const AudioFileUploader = ({
  needConsent,
  setTouched,
  onFileUploaded,
}: {
  needConsent: boolean;
  setTouched: (touched: boolean) => void;
  onFileUploaded: (fileData: Blob) => void;
}) => {
  const [hasConsent, setHasConsent] = useState(false);

  return (
    <Card
      compact
      className={"relative grow flex-row items-center gap-3 self-stretch"}
    >
      <div className="flex flex-col gap-3">
        {needConsent && (
          <div className="flex gap-3">
            <Typography variant="body2" className="flex">
              Consent Received
            </Typography>
            <input
              type="checkbox"
              checked={hasConsent}
              onChange={(e) => setHasConsent(e.target.checked)}
            />
          </div>
        )}
        {(!needConsent || (needConsent && hasConsent)) && (
          <div className="flex select-none flex-col items-start gap-3 text-card-foreground">
            <div className="flex flex-col gap-2">
              <label htmlFor="audio-file">
                The note will be saved when you select a file.
              </label>
              <input
                id="audio-file"
                type="file"
                accept="audio/aac,audio/mpeg,audio/webm,audio/ogg,audio/mp4,.mp3,.m4a"
                onChange={(e) => {
                  const fileInput = e.target as HTMLInputElement;
                  setTouched(true);
                  const f = fileInput.files?.item(0);
                  if (!f) return;
                  if (!f.type.startsWith("audio/")) return;
                  onFileUploaded(f);
                }}
              />
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};
