import type { RevalidationState } from "react-router";
import { <PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>t } from "lucide-react";
import { ReactElement, type ReactNode } from "react";
import { SerializeFrom } from "~/types/remix";

import { Button } from "~/@shadcn/ui/button";
import { cn } from "~/@shadcn/utils";
import { Spinner } from "~/@ui/assets/Spinner";
import { FollowUpStatus, NoteResponse } from "~/api/openapi/generated";
import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";

// Helpers
export const hasSummary = (note: SerializeFrom<NoteResponse>) =>
  (note.summaryByTopics?.sections?.length ?? 0) > 0;
export const hasTranscript = (note: SerializeFrom<NoteResponse>) =>
  (note.transcript.utterances?.length ?? 0) > 0;

// Fragments
type TabContentsProps = {
  activeTab: string;
  children: ReactNode;
  value: string;
};
const TabContents = ({ activeTab, children, value }: TabContentsProps) => (
  <div className={cn("flex flex-col gap-2", value !== activeTab && "hidden")}>
    {children}
  </div>
);

// Exports
export interface FollowUpTab {
  title: string;
  status: FollowUpStatus;
  uuid: string;
  tab: ReactElement;
}

type TabType = {
  label: ReactNode;
  value: string;
};

export const NoteTabGroup = ({
  note,
  detailsTab,
  summaryTab,
  transcriptTab,
  prepTab,
  followUpTabs = [],
  currentTab = "details",
  setCurrentTab,
  revalidator,
}: {
  note: SerializeFrom<NoteResponse>;
  detailsTab: ReactNode;
  summaryTab: ReactNode;
  transcriptTab: ReactNode;
  prepTab: ReactNode;
  followUpTabs: FollowUpTab[];
  currentTab: string;
  setCurrentTab: (tab: string) => void;
  revalidator: {
    revalidate: () => void;
    state: RevalidationState;
  };
}) => {
  const tabs: TabType[] = [
    {
      label: "Details",
      value: "details",
    },
    ...(summaryTab && hasSummary(note)
      ? [
          {
            label: "Summary",
            value: "summary",
          },
        ]
      : []),
    ...(transcriptTab && hasTranscript(note)
      ? [
          {
            label: "Transcript",
            value: "transcript",
          },
        ]
      : []),
    ...(prepTab
      ? [
          {
            label: "Meeting prep",
            value: "prep",
          },
        ]
      : []),
  ];

  followUpTabs.forEach(({ title, status, uuid }) => {
    const label = (
      <>
        {status === FollowUpStatus.Processing && (
          <Ellipsis aria-label="Loading" size={20} className="mr-1" />
        )}
        {(status === FollowUpStatus.Failed ||
          status === FollowUpStatus.Unknown) && (
          <TriangleAlert
            aria-label="Warning"
            size={20}
            className="mr-1 text-warning"
          />
        )}
        {title}
      </>
    );

    tabs.push({
      label,
      value: uuid,
    });
  });

  return (
    <>
      <div className="flex items-center gap-2 overflow-x-auto md:overflow-x-visible">
        <Tabs
          value={currentTab}
          onValueChange={setCurrentTab}
          className="md:flex-wrap"
          data-onboarding="notes-navigation"
        >
          <TabsList>
            {tabs.map(({ label, value }) => (
              <TabsTrigger key={value} value={value}>
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
        {followUpTabs.find(
          ({ status }) => status === FollowUpStatus.Processing
        ) && (
          <Button
            variant="ghost"
            size="icon-sm"
            onClick={revalidator.revalidate}
            disabled={revalidator.state === "loading"}
          >
            {revalidator.state === "loading" ? (
              <Spinner data-testid="spinner" />
            ) : (
              <RefreshCw aria-label="Refresh" size={20} />
            )}
          </Button>
        )}
      </div>
      <TabContents activeTab={currentTab} value="details">
        {detailsTab}
      </TabContents>
      {summaryTab && hasSummary(note) && (
        <TabContents activeTab={currentTab} value="summary">
          {summaryTab}
        </TabContents>
      )}
      {transcriptTab && hasTranscript(note) && (
        <TabContents activeTab={currentTab} value="transcript">
          {transcriptTab}
        </TabContents>
      )}
      {prepTab && (
        <TabContents activeTab={currentTab} value="prep">
          {prepTab}
        </TabContents>
      )}
      {followUpTabs.map(({ uuid, tab }, index) => (
        <TabContents key={index} activeTab={currentTab} value={uuid}>
          {tab}
        </TabContents>
      ))}
    </>
  );
};
