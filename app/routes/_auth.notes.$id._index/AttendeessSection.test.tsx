import { render, screen } from "@testing-library/react";
import { AttendeesSection } from "./AttendeesSection";
import { AttendeeInfo } from "~/api/openapi/generated";
import { AttendeeOptions } from "~/api/attendees/types";

describe("AttendeesSection", () => {
  it("renders attendees correctly", () => {
    const attendees: AttendeeOptions = [
      {
        uuid: "option1",
        name: "<PERSON>",
        type: "client",
      },
      {
        uuid: "option2",
        name: "<PERSON>",
        type: "user",
      },
      {
        uuid: "3",
        name: "Unknown User",
        type: "unknown",
      },
    ];
    const noteAttendees: AttendeeInfo[] = [
      {
        uuid: "1",
        name: "<PERSON>",
        type: "client",
        speakerPercentage: 25,
        clientUuid: "option1",
      },
      {
        uuid: "2",
        name: "<PERSON>",
        type: "user",
        speakerPercentage: 70,
        userUuid: "option2",
      },
      {
        uuid: "3",
        name: "Unknown User",
        type: "unknown",
        speakerPercentage: 5,
      },
    ];
    render(
      <AttendeesSection
        attendees={attendees}
        attendeesFromNote={noteAttendees}
      />
    );
    expect(screen.getByText("<PERSON> (25%)")).toBeInTheDocument();
    expect(screen.getByText("Jane Doe (70%)")).toBeInTheDocument();
    expect(screen.getByText("Unknown User (5%)")).toBeInTheDocument();
  });

  it("renders attendees correctly without percentages", () => {
    const attendees: AttendeeOptions = [
      {
        uuid: "option1",
        name: "John Doe",
        type: "client",
      },
      {
        uuid: "option2",
        name: "Jane Doe",
        type: "user",
      },
      {
        uuid: "3",
        name: "Unknown User",
        type: "unknown",
      },
    ];
    const noteAttendees: AttendeeInfo[] = [
      {
        uuid: "1",
        name: "John Doe",
        type: "client",
        clientUuid: "option1",
      },
      {
        uuid: "2",
        name: "Jane Doe",
        type: "user",
        userUuid: "option2",
      },
      {
        uuid: "3",
        name: "Unknown User",
        type: "unknown",
      },
    ];
    render(
      <AttendeesSection
        attendees={attendees}
        attendeesFromNote={noteAttendees}
      />
    );
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("Jane Doe")).toBeInTheDocument();
    expect(screen.getByText("Unknown User")).toBeInTheDocument();
  });

  it("handles attendees correctly without note attendees", () => {
    const attendees: AttendeeOptions = [
      {
        uuid: "option1",
        name: "John Doe",
        type: "client",
      },
      {
        uuid: "option2",
        name: "Jane Doe",
        type: "user",
      },
      {
        uuid: "3",
        name: "Unknown User",
        type: "unknown",
      },
    ];
    render(<AttendeesSection attendees={attendees} attendeesFromNote={[]} />);
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("Jane Doe")).toBeInTheDocument();
    expect(screen.getByText("Unknown User")).toBeInTheDocument();
  });

  it("renders 'No attendees' message when no attendees are provided", () => {
    render(<AttendeesSection attendees={[]} attendeesFromNote={[]} />);
    expect(screen.getByText("No attendees.")).toBeInTheDocument();
  });
});
