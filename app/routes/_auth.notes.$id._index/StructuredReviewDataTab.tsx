import { useFetcher } from "react-router";
import { useEffect, useState } from "react";
import { CopyIcon } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { Label } from "~/@shadcn/ui/label";
import { RadioGroup, RadioGroupItem } from "~/@shadcn/ui/radio-group";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { Typography } from "~/@ui/Typography";
import {
  StructuredReviewData,
  StructuredReviewDataEntry,
} from "./followUpDataTypes";
import { cn } from "~/@shadcn/utils";
import { copyFormattedVersionToClipboard } from "~/utils/copyToClipboard";

interface OptionCheckboxListProps {
  options: string[];
  selected: string[];
  disabled: boolean;
  onValueChange: (selected: string[]) => void;
}

const OptionCheckboxList: React.FC<OptionCheckboxListProps> = ({
  disabled,
  options,
  selected,
  onValueChange,
}) => {
  const [checked, setChecked] = useState<string[]>(selected);

  const handleCheckboxChange = (value: string) => {
    const newChecked = [...checked];
    const index = newChecked.indexOf(value);

    if (index === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(index, 1);
    }

    setChecked(newChecked);
    onValueChange(newChecked);
  };

  return (
    <div>
      {options.map((option) => (
        <div key={option} className="flex items-center">
          <Checkbox
            disabled={disabled}
            value={option}
            checked={checked.includes(option)}
            onCheckedChange={() => handleCheckboxChange(option)}
          />
          <label>{option}</label>
        </div>
      ))}
    </div>
  );
};

const ReviewEntries = ({
  entries,
  isEditable,
  updateReviewDataDiscussed,
  updateReviewDataNote,
  updateReviewDataSelected,
  updateReviewDataMultiSelected,
  updateReviewDataSubentries,
  headerTextClassName,
  listsClassName,
}: {
  entries: StructuredReviewDataEntry[];
  isEditable: boolean;
  updateReviewDataDiscussed: (index: number, discussed: boolean) => void;
  updateReviewDataNote: (index: number, note: string) => void;
  updateReviewDataSelected: (index: number, selected: string) => void;
  updateReviewDataMultiSelected: (index: number, selected: string[]) => void;
  updateReviewDataSubentries: (
    index: number,
    subentries: StructuredReviewDataEntry[]
  ) => void;
  headerTextClassName?: string;
  listsClassName?: string;
}) => {
  return (
    <>
      {entries.map((section, index) => (
        <li key={section.id}>
          <div className="flex flex-col">
            <div className="flex">
              {section.kind === "bullet" ? (
                <Typography className={"text-lg"}>{section.topic}</Typography>
              ) : (
                <span
                  className={cn(
                    "mx-1 grow overflow-hidden truncate text-ellipsis whitespace-nowrap leading-9",
                    headerTextClassName
                  )}
                  title={section.topic}
                >
                  {section.topic}
                </span>
              )}
              {section.kind != "header" && section.discussed !== undefined && (
                <div className="flex">
                  <Button
                    size="sm"
                    disabled={!isEditable}
                    variant={section.discussed ? "success" : "outline"}
                    className="mr-1"
                    onClick={() => updateReviewDataDiscussed(index, true)}
                  >
                    Discussed
                  </Button>
                  <Button
                    size="sm"
                    disabled={!isEditable}
                    variant={!section.discussed ? "destructive" : "outline"}
                    onClick={() => updateReviewDataDiscussed(index, false)}
                  >
                    Not discussed
                  </Button>
                </div>
              )}
            </div>
            <div className="flex">
              {section.options && section.kind == "select" && (
                <RadioGroup
                  defaultValue={section.selected}
                  disabled={!isEditable}
                  onValueChange={(value) =>
                    updateReviewDataSelected(index, value)
                  }
                >
                  {section.options.map((option) => (
                    <div key={option} className="flex items-center space-x-2">
                      <RadioGroupItem value={option} id={option} />
                      <Label htmlFor={option}>{option}</Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
              {section.options && section.kind == "multiselect" && (
                <OptionCheckboxList
                  options={section.options}
                  disabled={!isEditable}
                  selected={section.multi_selected ?? []}
                  onValueChange={(value) =>
                    updateReviewDataMultiSelected(index, value)
                  }
                />
              )}
            </div>
          </div>
          {isEditable && (
            <Typography className="text-lg" asChild>
              <TextareaGrowable
                placeholder="Add a note"
                value={section.explanation}
                onChange={(e) => updateReviewDataNote(index, e.target.value)}
              />
            </Typography>
          )}
          {!isEditable && section.explanation && (
            <Typography className="whitespace-pre-line text-lg">
              {section.explanation}
            </Typography>
          )}
          <ul
            className={cn(
              "flex list-disc flex-col pl-4 marker:text-warning",
              listsClassName
            )}
          >
            {section?.subentries && (
              <ReviewEntries
                entries={section.subentries}
                isEditable={isEditable}
                updateReviewDataNote={(childIndex, note) => {
                  const newSubentries = [...(section.subentries ?? [])];
                  newSubentries[childIndex]!.explanation = note;
                  updateReviewDataSubentries(index, newSubentries);
                }}
                updateReviewDataDiscussed={(childIndex, discussed) => {
                  const newSubentries = [...(section.subentries ?? [])];
                  newSubentries[childIndex]!.discussed = discussed;
                  updateReviewDataSubentries(index, newSubentries);
                }}
                updateReviewDataSelected={(childIndex, selected) => {
                  const newSubentries = [...(section.subentries ?? [])];
                  newSubentries[childIndex]!.selected = selected;
                  updateReviewDataSubentries(index, newSubentries);
                }}
                updateReviewDataMultiSelected={(childIndex, selected) => {
                  const newSubentries = [...(section.subentries ?? [])];
                  newSubentries[childIndex]!.multi_selected = selected;
                  updateReviewDataSubentries(index, newSubentries);
                }}
                updateReviewDataSubentries={(childIndex, subentries) => {
                  const newSubentries = [...(section.subentries ?? [])];
                  newSubentries[childIndex]!.subentries = subentries;
                  updateReviewDataSubentries(index, newSubentries);
                }}
                headerTextClassName={headerTextClassName}
                listsClassName={listsClassName}
              />
            )}
          </ul>
        </li>
      ))}
    </>
  );
};

// Exports
export const StructuredDataReviewTab = ({
  title,
  followUpUUID,
  noteUUID,
  reviewData,
  isEditable,
  headerTextClassName = "text-xl font-semibold",
  listsClassName = "gap-2",
}: {
  title: string;
  followUpUUID: string;
  noteUUID: string;
  reviewData: StructuredReviewData;
  isEditable: boolean;
  headerTextClassName?: string;
  listsClassName?: string;
}) => {
  const fetcher = useFetcher();
  const [data, setData] = useState(reviewData);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (!isEditable && hasChanges && data) {
      fetcher.submit(
        {
          followUpId: followUpUUID,
          followUpData: JSON.stringify(data),
          actionType: `update-meeting-follow-up`,
        },
        {
          method: "post",
          action: `/notes/${noteUUID}`,
          encType: "multipart/form-data",
        }
      );
      setHasChanges(false);
    }
  }, [
    isEditable,
    hasChanges,
    data,
    fetcher,
    followUpUUID,
    noteUUID,
    reviewData,
  ]);

  if (!data) {
    return null;
  }

  function updateReviewDataDiscussed(index: number, discussed: boolean) {
    setData((prevData) => {
      const newData = { ...prevData };
      newData.review_entries[index]!.discussed = discussed;
      return newData;
    });
    setHasChanges(true);
  }
  function updateReviewDataNote(index: number, note: string) {
    setData((prevData) => {
      const newData = { ...prevData };
      newData.review_entries[index]!.explanation = note;
      return newData;
    });
    setHasChanges(true);
  }
  function updateReivewDataSelected(index: number, selected: string) {
    setData((prevData) => {
      const newData = { ...prevData };
      newData.review_entries[index]!.selected = selected;
      return newData;
    });
    setHasChanges(true);
  }
  function updateReviewDataMultiSelected(index: number, selected: string[]) {
    setData((prevData) => {
      const newData = { ...prevData };
      newData.review_entries[index]!.multi_selected = selected;
      return newData;
    });
    setHasChanges(true);
  }
  function updateReviewDataSubentries(
    index: number,
    subentries: StructuredReviewDataEntry[]
  ) {
    setData((prevData) => {
      const newData = { ...prevData };
      newData.review_entries[index]!.subentries = subentries;
      return newData;
    });
    setHasChanges(true);
  }

  const handleCopySection = () => {
    copyFormattedVersionToClipboard(
      [
        {
          title: "Meeting transcript",
          list: data.review_entries.map(
            (entry) => `<b>${entry.topic}</b>: ${entry.explanation}`
          ),
        },
      ],
      "Meeting transcript"
    );
  };

  return (
    <>
      <h2 className="flex text-xl font-bold">
        {title}
        <CopyIcon
          className="ml-2 cursor-pointer text-gray-500 hover:text-black"
          onClick={handleCopySection}
        />
      </h2>

      <ol
        className={cn(
          "mt-2 flex list-decimal flex-col pl-4 marker:text-warning",
          listsClassName
        )}
      >
        <ReviewEntries
          entries={data.review_entries}
          isEditable={isEditable}
          updateReviewDataDiscussed={updateReviewDataDiscussed}
          updateReviewDataNote={updateReviewDataNote}
          updateReviewDataSelected={updateReivewDataSelected}
          updateReviewDataMultiSelected={updateReviewDataMultiSelected}
          updateReviewDataSubentries={updateReviewDataSubentries}
          headerTextClassName={headerTextClassName}
          listsClassName={listsClassName}
        />
      </ol>
    </>
  );
};
