import { ChevronDown, ChevronRight, RefreshCcw } from "lucide-react";
import { useEffect, useState } from "react";

import { Button } from "~/@shadcn/ui/button";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { Switch } from "~/@shadcn/ui/switch";
import { cn } from "~/@shadcn/utils";
import {
  ActionItem,
  CRMSyncItemSelection,
  CRMSyncSection,
} from "~/api/openapi/generated";

type DataType = {
  Icon: React.ElementType;
  name: string;
  key: string;
  selectionContent?: string | React.ReactNode;
  rawData?: any;
  isReadOnly?: boolean;
};

type SyncDataSelectionModalProps = {
  dataSet?: DataType[];
  onClose: () => void;
  onContinue: (data: Record<string, CRMSyncItemSelection>) => void;
  isSaving: boolean;
};

type SelectionDataType = { [key: string]: CRMSyncItemSelection };

const SyncDataSelectionModal = ({
  dataSet,
  onClose,
  onContinue,
  isSaving,
}: SyncDataSelectionModalProps) => {
  const [selectionData, setSelectionData] = useState<SelectionDataType>({});
  const [hasLoaded, setHasLoaded] = useState(false); // true if `selectionData` has been loaded from `dataSet`

  useEffect(() => {
    if (dataSet) {
      setSelectionData(
        dataSet.reduce(
          (acc, curr) => {
            // don't add read-only items to the selection data; else API will fail
            if (!curr.isReadOnly) {
              acc[curr.key] = {
                includeSection: true,
                includedItems: getAllActionItemUuids(dataSet, curr.key, true),
              };
            }
            return acc;
          },
          {} as Record<string, CRMSyncItemSelection>
        )
      );

      setHasLoaded(true);
    }
  }, [dataSet]);

  // selecting a section would automatically populate the included items list
  const updateSelectionData = (key: string) => {
    if (!dataSet) {
      return;
    }

    setSelectionData((prev) => ({
      ...prev,
      [key]: {
        includeSection: !prev[key]?.includeSection,
        includedItems: getAllActionItemUuids(
          dataSet,
          key,
          !prev[key]?.includeSection
        ),
      },
    }));
  };

  // toggling any item would automatically include the section as well
  const updateIncludedItems = (
    key: keyof typeof selectionData,
    uuid: string
  ) => {
    setSelectionData((prev) => ({
      ...prev,
      [key]: {
        includeSection: true,
        includedItems: prev[key]?.includedItems?.includes(uuid)
          ? prev[key]?.includedItems?.filter((item) => item !== uuid)
          : [...(prev[key]?.includedItems || []), uuid],
      },
    }));
  };

  // check if at least one non-readonly section is selected
  const isAnySectionSelected = Object.entries(selectionData).some(
    ([key, { includeSection: value }]) => {
      const isReadOnly = dataSet?.find((item) => item.key === key)?.isReadOnly;
      return !isReadOnly && value;
    }
  );

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-5xl p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle>Attributes to sync</DialogTitle>
          <DialogDescription>
            {hasLoaded
              ? "Select the data you want to sync to your CRM. Once this note is synced, you will not be able to edit it on Zeplyn."
              : "Please wait while we load the details for you.."}
          </DialogDescription>
        </DialogHeader>

        <div className="my-2 flex max-h-[60vh] min-h-[40vh] flex-col gap-2 overflow-auto md:grid md:min-h-[50vh] md:grid-cols-2 md:content-start md:items-start">
          {hasLoaded &&
            dataSet?.map((item) => (
              <AttributeCell
                item={item}
                key={item.key}
                isSelected={selectionData[item.key]?.includeSection}
                onCheckedChange={() => updateSelectionData(item.key)}
                includedItems={selectionData[item.key]?.includedItems || []}
                onToggleIncludedItem={(uuid: string) =>
                  updateIncludedItems(item.key, uuid)
                }
              />
            ))}
        </div>

        <DialogFooter>
          <div className="w-full">
            {hasLoaded && (
              <DialogDescription>
                By clicking <strong>Synchronize</strong>, you certify that the
                interaction record you are syncing is accurate.
              </DialogDescription>
            )}

            <div className="mt-2 flex items-center justify-end gap-2">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                disabled={!hasLoaded || !isAnySectionSelected || isSaving}
                onClick={() => onContinue(selectionData)}
              >
                <RefreshCcw />
                {isSaving ? "Synchronizing..." : "Synchronize"}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

function getAllActionItemUuids(
  dataSet: DataType[],
  key: string,
  isSelected: boolean
) {
  switch (key) {
    case CRMSyncSection.Tasks:
      if (!isSelected) {
        return [];
      }
      return dataSet
        .find((item) => item.key === CRMSyncSection.Tasks)
        ?.rawData?.map((item: ActionItem) => item.uuid);
    default:
      return [];
  }
}

const AttributeCell = ({
  item: { Icon, key, name, selectionContent, rawData, isReadOnly },
  isSelected,
  onCheckedChange,
  includedItems,
  onToggleIncludedItem,
}: {
  item: DataType;
  isSelected?: boolean;
  onCheckedChange: () => void;
  includedItems?: string[];
  onToggleIncludedItem: (uuid: string) => void;
}) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const ToggleIcon = isCollapsed ? ChevronRight : ChevronDown;

  const isPartiallySelected =
    isSelected &&
    !!includedItems?.length &&
    includedItems?.length < rawData.length;

  return (
    <div
      key={key}
      className={cn(
        "rounded-sm border border-muted bg-muted p-4",
        (isSelected || isReadOnly) && "border-success",
        !isCollapsed && "md:self-stretch"
      )}
      onClick={() => setIsCollapsed(!isCollapsed)}
    >
      <div className="flex items-center justify-between">
        <span className="flex items-center gap-2 font-semibold">
          <Icon size={18} className="text-muted-foreground" />
          {name}
        </span>

        {isPartiallySelected && (
          <span className="ml-auto text-xs font-bold uppercase text-muted-foreground">
            Partial <span className="hidden sm:inline">sync</span>
          </span>
        )}

        <Switch
          disabled={isReadOnly}
          checked={isReadOnly ? true : isSelected}
          onCheckedChange={onCheckedChange}
          onClick={(e) => {
            // stop propagation to prevent the cell from collapsing / expanding
            e.stopPropagation();
          }}
          title={isReadOnly ? "This attribute will always be synced" : ""}
          className={isPartiallySelected ? "ml-2" : "ml-auto"}
        />
        <ToggleIcon className="ml-2 cursor-pointer" />
      </div>
      {!isCollapsed &&
        (selectionContent ? (
          <div className="mt-2 text-sm text-muted-foreground">
            {selectionContent}
          </div>
        ) : (
          <ItemSelectionSection
            data={rawData}
            includedItems={includedItems}
            toggleItem={onToggleIncludedItem}
          />
        ))}
    </div>
  );
};

// render list of items - each with a checkbox
const ItemSelectionSection = ({
  data,
  includedItems,
  toggleItem,
}: {
  data: any;
  includedItems?: string[];
  toggleItem: (uuid: string) => void;
}) => {
  return (
    <div className="mt-2 flex flex-col gap-2 text-sm text-muted-foreground">
      {data.map((item: any) => (
        <div
          key={item.uuid}
          className="flex items-center gap-2"
          onClick={(e) => {
            e.stopPropagation(); // stops click event from reaching parent div
          }}
        >
          <Checkbox
            checked={includedItems?.includes(item.uuid)}
            onCheckedChange={() => toggleItem(item.uuid)}
          />
          <span>{item.content}</span>
        </div>
      ))}
    </div>
  );
};

export default SyncDataSelectionModal;
