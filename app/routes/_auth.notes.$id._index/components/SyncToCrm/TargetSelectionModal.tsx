import { useState } from "react";
import { BriefcaseBusiness } from "lucide-react";

import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON>Footer,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Button } from "~/@shadcn/ui/button";

import { CRMUploadTarget } from "~/api/openapi/generated";
import { Combobox } from "~/@ui/Combobox";

type ClientSelectionModalProps = {
  onClose: () => void;
  onContinue: (uploadTargetId: string) => void;
  targets: CRMUploadTarget[];
  isSaving: boolean;
};

const TargetSelectionModal = ({
  onClose,
  onContinue,
  targets,
  isSaving,
}: ClientSelectionModalProps) => {
  const [selectedTarget, setSelectedTarget] = useState<
    CRMUploadTarget | undefined
  >();

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Select CRM Upload Target
          </DialogTitle>
        </DialogHeader>
        <FormField id="uploadTarget" name="uploadTarget">
          <FormLabel className="text-sm font-medium text-gray-700">
            Upload Target
          </FormLabel>
          <Combobox
            options={targets.map((target) => ({
              value: target.id,
              label: target.name,
            }))}
            placeholder="Select an upload target"
            leftIcon={<BriefcaseBusiness />}
            onChange={(value) =>
              setSelectedTarget(targets.find((t) => t.id === value))
            }
            selected={selectedTarget?.id}
            searchOnLabel={true}
            modal
          />
        </FormField>
        <DialogFooter className="pt-6">
          <Button onClick={onClose} variant="ghost">
            Cancel
          </Button>
          <Button
            onClick={() => {
              selectedTarget && onContinue(selectedTarget.id);
            }}
            disabled={!selectedTarget || isSaving}
          >
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TargetSelectionModal;
