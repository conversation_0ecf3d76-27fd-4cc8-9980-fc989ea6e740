import { useMemo, useState } from "react";
import { useRevalidator } from "react-router";
import { RefreshCcw } from "lucide-react";
import { toast } from "react-toastify";

import { Button } from "~/@shadcn/ui/button";
import {
  ApiRoutersCrmClientResponse,
  CRMSyncItemSelection,
  CRMUploadTarget,
  NoteResponse,
} from "~/api/openapi/generated";
import SyncDataSelectionModal from "./SyncDataSelectionModal";

import { getCardData } from "./utils";
import ClientSelectionModal from "./ClientSelectionModal";
import LabeledValue from "~/types/LabeledValue";
import { EditableNoteActions } from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import TargetSelectionModal from "./TargetSelectionModal";
import { fetchPost } from "~/utils/fetch";

enum Modal {
  Selection = "selection",
  ClientSelection = "client-selection",
  TargetSelection = "target-selection",
}

type SyncToCrmFlowProps = {
  clients?: ApiRoutersCrmClientResponse[];
  currentClient?: LabeledValue;
  dispatch?: React.Dispatch<EditableNoteActions>;
  data?: NoteResponse;
  noteId?: string;
  disabled?: boolean;
};
const SyncToCrmFlow = ({
  clients,
  currentClient,
  dispatch,
  data,
  noteId,
  disabled,
}: SyncToCrmFlowProps) => {
  const [noteData, setNoteData] = useState(data);
  const [selectedModal, setSelectedModal] = useState<string | null>();
  const [selections, setSelections] = useState<
    Record<string, CRMSyncItemSelection>
  >({});
  const [syncTargets, setSyncTargets] = useState<CRMUploadTarget[]>([]);

  const [isSaving, setIsSaving] = useState(false);

  const revalidator = useRevalidator();

  async function loadNoteData() {
    const noteData = await fetch(`/feapi/notes/id?noteId=${noteId}`).then(
      (res) => res.json()
    );

    setNoteData(noteData);
  }

  // when the S2C CTA is clicked
  const onClickCta = () => {
    // check if noteData is loaded
    if (!noteData && noteId) {
      // make API call using noteId
      loadNoteData();
    }

    // open client selection dropdown if client has not been selected (and a list has been provided);
    // else open the selection modal
    if (!currentClient && clients?.length) {
      setSelectedModal(Modal.ClientSelection);
    } else {
      setSelectedModal(Modal.Selection);
    }
  };

  const closeAll = () => {
    setSelectedModal(null);
  };

  const saveClient = async (selectedClient: LabeledValue) => {
    setIsSaving(true);

    try {
      const clientToUpdate =
        clients?.find(({ uuid }) => uuid === selectedClient?.value) ?? null;

      if (!clientToUpdate || !dispatch) {
        return;
      }

      dispatch({
        type: "updateClient",
        client: clientToUpdate,
      });

      const { success } = await fetchPost("/feapi/notes/save", {
        noteId: noteData?.uuid,
        clientName: clientToUpdate.name,
        clientId: clientToUpdate.uuid,
      });

      if (success) {
        setSelectedModal(Modal.Selection); // trigger the next phase
      } else {
        throw new Error("Failed to save client");
      }
    } catch (e) {
      toast.error("Failed to save client. Please try again!!!", {
        autoClose: 2000,
        toastId: "save-client-error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const saveToCrmFromAttributeSelection = async (
    syncItems: Record<string, CRMSyncItemSelection>
  ) => {
    // save `syncItems` in case upload targets flow is invoked
    setSelections(syncItems);

    saveToCrm(syncItems, "");
  };

  const saveToCrmFromTargetSelection = (uploadTargetId: string) => {
    saveToCrm(selections, uploadTargetId);
  };

  const saveToCrm = async (
    selections: Record<string, CRMSyncItemSelection>,
    uploadTargetId: string
  ) => {
    setIsSaving(true);

    try {
      const { success, userInputRequired, uploadTargetOptions, error } =
        await fetchPost("/feapi/notes/sync-to-crm", {
          noteId: noteData?.uuid,
          uploadTargetId,
          syncItems: selections,
        });

      // handle outright failures
      if (!success) {
        toast.error(`Failed to sync note to CRM. ${error}`, {
          autoClose: 2000,
          toastId: "sync-to-crm-error",
        });
        return;
      }

      // happy flow
      if (!userInputRequired) {
        closeAll();

        toast.success("Note synced to CRM", {
          autoClose: 2000,
          toastId: "sync-to-crm-success",
        });
        // Ensure toast is displayed before revalidating
        setTimeout(() => {
          revalidator.revalidate();
        }, 0);

        return;
      }

      // in case user input is required, open the target selection modal
      if (userInputRequired && uploadTargetOptions) {
        setSelectedModal(Modal.TargetSelection);
        setSyncTargets(uploadTargetOptions);

        return;
      }

      // There won't be any case where `userInputRequired` is true and `uploadTargetOptions` is undefined. Right? RIGHT??
      throw new Error("Unexpected response from server");
    } catch (e) {
      toast.error("Failed to sync note to CRM. Please try again", {
        autoClose: 2000,
        toastId: "sync-to-crm-error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const selectionModalData = useMemo(() => getCardData(noteData), [noteData]);

  return (
    <>
      <Button
        onClick={onClickCta}
        variant="ghost"
        className=""
        data-onboarding="sync-crm-cta"
        aria-label="Sync to CRM"
        disabled={disabled}
      >
        <RefreshCcw />

        <span className="hidden sm:block">Sync to CRM</span>
      </Button>

      {selectedModal === Modal.ClientSelection && clients?.length && (
        <ClientSelectionModal
          onClose={closeAll}
          onContinue={saveClient}
          clients={clients}
          currentClient={currentClient}
          isSaving={isSaving}
        />
      )}

      {selectedModal === Modal.Selection && (
        <SyncDataSelectionModal
          dataSet={selectionModalData}
          onClose={closeAll}
          onContinue={saveToCrmFromAttributeSelection}
          isSaving={isSaving}
        />
      )}

      {selectedModal === Modal.TargetSelection && (
        <TargetSelectionModal
          onClose={closeAll}
          onContinue={saveToCrmFromTargetSelection}
          targets={syncTargets}
          isSaving={isSaving}
        />
      )}
    </>
  );
};

export default SyncToCrmFlow;
