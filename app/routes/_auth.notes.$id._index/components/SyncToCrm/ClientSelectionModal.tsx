import { useState } from "react";
import { BriefcaseBusiness } from "lucide-react";

import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogFooter,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Button } from "~/@shadcn/ui/button";

import CrmClientsDropdown from "../CrmClientsDropdown";
import LabeledValue from "~/types/LabeledValue";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";

type ClientSelectionModalProps = {
  onClose: () => void;
  onContinue: (selectedClient: LabeledValue) => void;
  clients: ApiRoutersCrmClientResponse[];
  currentClient: LabeledValue | undefined;
  isSaving: boolean;
};

const ClientSelectionModal = ({
  onClose,
  onContinue,
  clients,
  currentClient,
  isSaving,
}: ClientSelectionModalProps) => {
  const [selectedClient, setSelectedClient] = useState<
    LabeledValue | undefined
  >(currentClient); // default to current client; will be updated by user

  const onChangeDropdown = (item: LabeledValue | undefined) => {
    setSelectedClient(item);
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Which client do you want to sync to?
          </DialogTitle>
        </DialogHeader>
        {clients.length > 0 && (
          <FormField id="clientId" name="clientId">
            <FormLabel className="text-sm font-medium text-gray-700">
              CRM Client
            </FormLabel>
            <CrmClientsDropdown
              placeholder="Select a client"
              leftIcon={<BriefcaseBusiness />}
              onChange={onChangeDropdown}
              selectedObject={selectedClient}
              searchOnLabel={true}
              modal
            />
          </FormField>
        )}
        <DialogFooter className="pt-6">
          <Button onClick={onClose} variant="ghost">
            Cancel
          </Button>
          <Button
            disabled={!selectedClient || isSaving}
            onClick={() => selectedClient && onContinue(selectedClient)}
          >
            {isSaving ? "Saving..." : "Confirm"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClientSelectionModal;
