import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";
import CrmClientsDropdown from "./CrmClientsDropdown";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";

// Mock the VirtualizedCombobox to avoid complex virtualization testing
vi.mock("~/@ui/VirtualizedCombobox", () => ({
  VirtualizedCombobox: ({
    options,
    placeholder,
    leftIcon,
    onChange,
    disabled,
    selectedObject,
    loadOptions,
    itemSize,
    maxHeightPx,
  }: any) => {
    return (
      <div data-testid="virtualized-combobox">
        <button
          data-testid="combobox-trigger"
          disabled={disabled}
          onClick={() => loadOptions?.("")}
        >
          {selectedObject?.label || placeholder || "Select client"}
        </button>
        {leftIcon && <div data-testid="left-icon">{leftIcon}</div>}
        <div data-testid="options-list">
          {options.map((option: any) => (
            <div
              key={option.value}
              data-testid={`option-${option.value}`}
              onClick={() => onChange(option)}
            >
              {option.label}
            </div>
          ))}
        </div>
        <div data-testid="combobox-props">
          <span data-testid="item-size-prop">{itemSize}</span>
          <span data-testid="max-height-prop">{maxHeightPx}</span>
        </div>
      </div>
    );
  },
}));

describe("CrmClientsDropdown", () => {
  const mockOnChange = vi.fn();

  const mockClientsResponse = {
    clients: [
      { uuid: "client-1", name: "John Doe", type: "individual" },
      { uuid: "client-2", name: "Jane Smith", type: "business" },
      { uuid: "client-3", name: "Acme Corp", type: "business" },
    ] as ApiRoutersCrmClientResponse[],
    nextPageToken: "next-page-token",
  };

  const defaultProps = {
    onChange: mockOnChange,
  };

  beforeEach(() => {
    global.fetch = vi.fn();
    vi.mocked(global.fetch).mockResolvedValue({
      json: () => Promise.resolve(mockClientsResponse),
    } as Response);
  });

  it("renders with default props", () => {
    render(<CrmClientsDropdown {...defaultProps} />);

    expect(screen.getByTestId("virtualized-combobox")).toBeInTheDocument();
    expect(screen.getByTestId("combobox-trigger")).toBeInTheDocument();
    expect(screen.getByText("Select client")).toBeInTheDocument();
  });

  it("renders with custom placeholder", () => {
    render(
      <CrmClientsDropdown {...defaultProps} placeholder="Choose a client" />
    );

    expect(screen.getByText("Choose a client")).toBeInTheDocument();
  });

  it("renders with left icon", () => {
    const leftIcon = <span data-testid="custom-icon">🔍</span>;

    render(<CrmClientsDropdown {...defaultProps} leftIcon={leftIcon} />);

    expect(screen.getByTestId("left-icon")).toBeInTheDocument();
    expect(screen.getByTestId("custom-icon")).toBeInTheDocument();
  });

  it("renders with selected object", () => {
    const selectedObject = { value: "client-1", label: "John Doe" };

    render(
      <CrmClientsDropdown {...defaultProps} selectedObject={selectedObject} />
    );

    expect(screen.getByText("John Doe")).toBeInTheDocument();
  });

  it("renders in disabled state", () => {
    render(<CrmClientsDropdown {...defaultProps} disabled />);

    const trigger = screen.getByTestId("combobox-trigger");
    expect(trigger).toBeDisabled();
  });

  it("loads clients on first API call", async () => {
    const user = userEvent.setup();
    render(<CrmClientsDropdown {...defaultProps} />);

    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        "/feapi/clients/get?searchTerm=&cursor=&pageSize=20"
      );
    });

    await waitFor(() => {
      expect(screen.getByTestId("option-client-1")).toBeInTheDocument();
    });

    expect(screen.getByTestId("option-client-2")).toBeInTheDocument();
    expect(screen.getByTestId("option-client-3")).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("Jane Smith")).toBeInTheDocument();
    expect(screen.getByText("Acme Corp")).toBeInTheDocument();
  });

  it("handles client selection", async () => {
    const user = userEvent.setup();
    render(<CrmClientsDropdown {...defaultProps} />);

    // Load the clients
    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(screen.getByTestId("option-client-1")).toBeInTheDocument();
    });

    // Select a client
    const option = screen.getByTestId("option-client-1");
    await user.click(option);

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith({
        value: "client-1",
        label: "John Doe",
      });
    });
  });

  it("handles search with new search term", async () => {
    const user = userEvent.setup();

    // Mock the VirtualizedCombobox to simulate search
    vi.mocked(global.fetch)
      .mockResolvedValueOnce({
        json: () => Promise.resolve(mockClientsResponse),
      } as Response)
      .mockResolvedValueOnce({
        json: () =>
          Promise.resolve({
            clients: [
              { uuid: "client-4", name: "Search Result", type: "individual" },
            ],
            nextPageToken: "",
          }),
      } as Response);

    const TestComponent = () => {
      const [searchTerm, setSearchTerm] = React.useState("");

      return (
        <div>
          <input
            data-testid="search-input"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <CrmClientsDropdown {...defaultProps} />
        </div>
      );
    };

    render(<TestComponent />);

    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        "/feapi/clients/get?searchTerm=&cursor=&pageSize=20"
      );
    });

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "john");

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });
  });

  it("handles API error gracefully", async () => {
    const user = userEvent.setup();

    // Mock fetch to reject but catch the error to prevent unhandled rejection
    vi.mocked(global.fetch).mockImplementation(() =>
      Promise.reject(new Error("API Error")).catch(() => {
        // Silently catch the error to prevent unhandled rejection
        return {
          json: () => Promise.resolve({ clients: [], nextPageToken: "" }),
        } as Response;
      })
    );

    render(<CrmClientsDropdown {...defaultProps} />);

    const trigger = screen.getByTestId("combobox-trigger");

    await user.click(trigger);

    await waitFor(() => {
      expect(screen.getByTestId("virtualized-combobox")).toBeInTheDocument();
    });
  });

  it("handles empty API response", async () => {
    const user = userEvent.setup();

    vi.mocked(global.fetch).mockResolvedValue({
      json: () => Promise.resolve({ clients: [], nextPageToken: "" }),
    } as Response);

    render(<CrmClientsDropdown {...defaultProps} />);

    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled();
    });

    const optionsList = screen.getByTestId("options-list");
    expect(optionsList).toBeEmptyDOMElement();
  });
});
