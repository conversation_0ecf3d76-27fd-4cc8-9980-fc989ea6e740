import { useState, useEffect, useCallback } from "react";
import { useFetcher } from "react-router";
import { toast } from "react-toastify";
import { Sparkles } from "lucide-react";

import { SummarySection } from "~/api/openapi/generated";
import AskAnythingModal, { AnswerStatus } from "~/@ui/AskAnythingModal";
import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

type FetcherData = {
  actionType?: string;
  success?: boolean;
  error?: string;
  answer?: SummarySection;
  searchQueryId?: string;
  sectionIndex?: number;
};

const AskMeAnythingSection = ({
  noteId,
  handleAddSectionToSummarySuccess,
  query,
  setQuery,
  triggerSearch,
  setTriggerSearch,
}: {
  noteId: string;
  handleAddSectionToSummarySuccess: (sectionIndex: number | null) => void;
  query: string;
  setQuery: (query: string) => void;
  triggerSearch: boolean;
  setTriggerSearch: (triggerSearch: boolean) => void;
}) => {
  const fetcher = useFetcher<FetcherData>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [answerStatus, setAnswerStatus] = useState<AnswerStatus>(
    AnswerStatus.IDLE
  );
  const [answer, setAnswer] = useState<SummarySection | null>(null);
  const [searchQueryId, setSearchQueryId] = useState<string>("");

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);
  const openToggle = (status: boolean) => {
    if (status) {
      setQuery("");
    } else {
      setQuery("");
      closeModal();
    }
  };

  useEffect(() => {
    if (fetcher.state !== "idle" || !fetcher.data) {
      return;
    }
    if (fetcher.data.actionType === "search-note") {
      if (fetcher.data.success && fetcher.data?.answer) {
        setAnswerStatus(AnswerStatus.SUCCESS);
        setAnswer(fetcher.data.answer);
        setSearchQueryId(fetcher.data.searchQueryId ?? "");
      } else {
        setAnswerStatus(AnswerStatus.FAILED);
      }
    } else if (fetcher.data.actionType === "add-section-to-summary") {
      if (fetcher.data.success) {
        toast.success("Your answer was added to the summary");
        handleAddSectionToSummarySuccess(fetcher.data.sectionIndex ?? null);
        setQuery("");
        closeModal();
      } else {
        toast.error("Failed to add to summary");
      }
    }
  }, [fetcher.data, fetcher.state]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleClick = useCallback(() => {
    setAnswer(null);
    setTriggerSearch(false);
    const formData = new FormData();
    formData.append("actionType", "search-note");
    formData.append("query", query);

    setAnswerStatus(AnswerStatus.IN_PROCESS);
    fetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteId}`,
      encType: "multipart/form-data",
    });
  }, [setTriggerSearch, query, fetcher, noteId]);

  useEffect(() => {
    if (query && triggerSearch) {
      openModal();
      handleClick();
    }
  }, [handleClick, query, triggerSearch]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
      openModal();
      handleClick();
    }
  };

  const handleAddToSummary = () => {
    const formData = new FormData();
    formData.append("actionType", "add-section-to-summary");
    formData.append("searchQueryId", searchQueryId);
    formData.append("noteId", noteId);

    fetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteId}/`,
      encType: "multipart/form-data",
    });
  };

  return (
    <div>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline_magic"
            onClick={openModal}
            data-onboarding="ask-anything"
          >
            <Sparkles className="!h-5 !w-5" />{" "}
            <span className="bg-magic hidden bg-clip-text text-transparent sm:block">
              Ask Anything
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Ask anything about this note</TooltipContent>
      </Tooltip>

      <AskAnythingModal
        isOpen={isModalOpen}
        onOpenChange={openToggle}
        answerStatus={answerStatus}
        query={query}
        answer={answer}
        handleKeyDown={handleKeyDown}
        onInputChange={(e) => {
          setAnswerStatus(AnswerStatus.IDLE);
          setQuery(e.target.value);
        }}
        handleAsk={() => {
          openModal();
          handleClick();
        }}
        handleAddToSummary={handleAddToSummary}
        setAnswerStatus={setAnswerStatus}
        setQuery={setQuery}
      />
    </div>
  );
};

export default AskMeAnythingSection;
