import { useMemo, useState } from "react";
import { useRevalidator, useSubmit } from "react-router";
import { toast } from "react-toastify";
import { CopyIcon } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import { Typography } from "~/@ui/Typography";
import { RemapSpeakersModal } from "./RemapSpeakersModal";
import { NoteResponse } from "~/api/openapi/generated";
import { copyFormattedVersionToClipboard } from "~/utils/copyToClipboard";
import { SerializeFrom } from "~/types/remix";

// Exports
export const TranscriptTab = ({
  note,
  enableRemap,
}: {
  note: SerializeFrom<NoteResponse>;
  enableRemap: boolean;
}) => {
  const [isModalOpen, setModalOpen] = useState(false);
  const submit = useSubmit();
  const revalidator = useRevalidator();

  // Extract unique speakers from the transcript
  const speakers = useMemo(() => {
    const speakerSet = new Set<string>();
    (note.transcript?.utterances ?? []).forEach((item) => {
      speakerSet.add(item.speaker);
    });
    return Array.from(speakerSet);
  }, [note.transcript]);

  // Extract the attendees list
  const attendees = useMemo(() => {
    return note.attendees;
  }, [note.attendees]);

  const handleRemapConfirm = (mapping: { [key: string]: string }) => {
    const swaps = Object.entries(mapping).map(([current_alias, new_alias]) => ({
      current_alias,
      new_alias,
    }));

    submit(
      {
        noteId: note.uuid,
        swaps: JSON.stringify(swaps),
        actionType: "send-attendee-remap",
      },
      {
        method: "post",
        action: `/notes/${note.uuid}`,
        encType: "multipart/form-data",
      }
    );

    setModalOpen(false);
    revalidator.revalidate();

    toast.success("Remapping initiated", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "light",
    });
  };

  const handleCopySection = () => {
    copyFormattedVersionToClipboard(
      [
        {
          title: "Meeting transcript",
          list: note.transcript?.utterances?.map(
            (item) => `${item.start} ${item.speaker}: ${item.text}`
          ),
        },
      ],
      "Meeting transcript"
    );
  };

  return (
    <>
      <div className="flex items-center gap-1">
        <Typography variant="h3" color="primary">
          Meeting transcript
        </Typography>
        <CopyIcon
          className="ml-2 cursor-pointer text-gray-500 hover:text-black"
          onClick={handleCopySection}
        />
        {note.features?.includes("remap_speakers") && (
          <Button
            size="default"
            className="ml-auto flex items-center gap-2 rounded-xl bg-[#0F172A] px-4 py-2 text-white hover:bg-[#0E1627] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            onClick={() => setModalOpen(true)}
            disabled={!enableRemap}
          >
            Remap Speakers
          </Button>
        )}
      </div>

      <ul className="flex list-disc flex-col gap-2 pl-4 marker:text-warning">
        {(note.transcript?.utterances ?? []).map((item) => (
          <li key={`${item.speaker}-${item.start}`}>
            <div className="flex">
              <Typography
                className="w-16 shrink-0 leading-6"
                color="secondary"
                variant="body2"
              >
                {item.start.split(".").shift()}
              </Typography>

              <div className="flex flex-col">
                <Typography variant="h3">{item.speaker}</Typography>
                <Typography className="text-lg">{item.text}</Typography>
              </div>
            </div>
          </li>
        ))}
      </ul>

      <RemapSpeakersModal
        isOpen={isModalOpen}
        onClose={() => setModalOpen(false)}
        speakers={speakers}
        attendees={attendees ?? []}
        onConfirm={handleRemapConfirm}
      />
    </>
  );
};
