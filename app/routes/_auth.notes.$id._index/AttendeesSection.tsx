import { SerializeFrom } from "~/types/remix";
import { UsersIcon } from "lucide-react";
import { AttendeeTag } from "~/@ui/attendees/attendeeTags";
import { Typography } from "~/@ui/Typography";
import { AttendeeOptions } from "~/api/attendees/types";
import { AttendeeInfo } from "~/api/openapi/generated";

export const AttendeesSection = ({
  attendees,
  attendeesFromNote,
}: {
  attendees: AttendeeOptions;
  attendeesFromNote: SerializeFrom<AttendeeInfo>[];
}) => {
  // Extract speaker percentages from attendeesFromNote and add them to attendees.
  const attendeesWithPercentages = attendees.map((attendee) => {
    const noteAttendee = attendeesFromNote.find((noteAttendee) => {
      return (
        (noteAttendee.type === "client" &&
          noteAttendee.clientUuid === attendee.uuid) ||
        (noteAttendee.type === "user" &&
          noteAttendee.userUuid === attendee.uuid) ||
        (noteAttendee.type === "unknown" && noteAttendee.uuid === attendee.uuid)
      );
    });
    if (!noteAttendee) {
      return attendee;
    }
    return { ...attendee, speakerPercentage: noteAttendee.speakerPercentage };
  });
  return (
    <div className="inline-flex gap-2">
      <UsersIcon />
      {attendeesWithPercentages.length > 0 ? (
        <span className="flex flex-wrap gap-2">
          {attendeesWithPercentages.map((attendee) => (
            <AttendeeTag key={attendee.uuid} {...attendee} />
          ))}
        </span>
      ) : (
        <Typography color="secondary" variant="body2">
          No attendees.
        </Typography>
      )}
    </div>
  );
};
