import { SectionItemTextBlock } from "~/api/openapi/generated";

type SettingTypeTextBlockProps = SectionItemTextBlock;

const SettingTypeTextBlock = ({
  label,
  details,
}: SettingTypeTextBlockProps) => {
  return (
    <div className="mt-6 rounded-md border p-4">
      <h3 className="text-lg font-bold">{label}</h3>
      <p className="mt-3">{details}</p>
    </div>
  );
};

export default SettingTypeTextBlock;
