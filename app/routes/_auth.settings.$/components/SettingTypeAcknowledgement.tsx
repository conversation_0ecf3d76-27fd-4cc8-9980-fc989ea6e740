import { useState } from "react";

import { SectionItemAcknowledgementField } from "~/api/openapi/generated";
import { Checkbox } from "~/@shadcn/ui/checkbox";

type SettingTypeAcknowledgementProps = SectionItemAcknowledgementField & {
  onChange: (id: string, value: boolean) => void;
};
const SettingTypeAcknowledgement = ({
  id,
  label,
  value,
  onChange,
}: SettingTypeAcknowledgementProps) => {
  const [isChecked, setIsChecked] = useState(!!value);
  return (
    <div className="mt-6 flex items-center">
      <label htmlFor={id}>{label}</label>
      <Checkbox
        id={id}
        className="ml-2"
        checked={isChecked}
        onClick={() => {
          setIsChecked(!isChecked);
          onChange(id, !isChecked);
        }}
      />
    </div>
  );
};

export default SettingTypeAcknowledgement;
