import { useState } from "react";

import { SectionItemSingleChoiceField } from "~/api/openapi/generated";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "~/@shadcn/ui/select";

type SettingTypeSwitchProps = SectionItemSingleChoiceField & {
  onChange: (id: string, value: string) => void;
};

const SettingTypeDropdown = ({
  id,
  label,
  value,
  options,
  onChange,
}: SettingTypeSwitchProps) => {
  const [selectValue, setSelectValue] = useState(value?.toString());

  const onChangeValue = (value: string) => {
    setSelectValue(value);
    onChange(id, value);
  };

  return (
    <div className="mt-6 flex items-center">
      <span>{label}</span>
      <Select onValueChange={onChangeValue} value={selectValue}>
        <SelectTrigger className="ml-2 w-auto">
          <SelectValue placeholder="Select a value" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {options?.map(({ id, label }) => (
              <SelectItem key={id} value={id}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default SettingTypeDropdown;
