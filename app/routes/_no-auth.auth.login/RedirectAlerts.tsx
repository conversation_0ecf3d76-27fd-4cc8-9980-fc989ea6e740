import { useSearchParams } from "react-router";
import {
  CheckCircledIcon,
  ExclamationTriangleIcon,
} from "@radix-ui/react-icons";
import { AlertDescription, AlertTitle, Alert } from "~/@shadcn/ui/alert";

// Fragments
const RegistrationRedirectAlert = () => (
  <div className="my-4 flex flex-col gap-2">
    <Alert variant="success">
      <CheckCircledIcon className="h-5 w-5 text-success" />
      <AlertTitle>Account created!</AlertTitle>
      <AlertDescription>
        We sent an e-mail to you for verification. Follow the link provided to
        finalize the signup process. Please contact us if you do not receive it
        within a few minutes.
      </AlertDescription>
    </Alert>
  </div>
);
const ForgotPasswordRedirectAlert = () => (
  <div className="my-4 flex flex-col gap-2">
    <Alert variant="success">
      <CheckCircledIcon className="h-5 w-5 text-success" />
      <AlertTitle>Password reset email sent!</AlertTitle>
      <AlertDescription>
        Check your inbox for an email with a password reset link. Follow the
        link provided to complete the password reset process. Please contact us
        if you do not receive it within a few minutes.
      </AlertDescription>
    </Alert>
  </div>
);
const ResetPasswordRedirectError = () => (
  <div className="my-4 flex flex-col gap-2">
    <Alert variant="destructive">
      <ExclamationTriangleIcon className="h-5 w-5 text-destructive" />
      <AlertTitle>Invalid password reset link.</AlertTitle>
      <AlertDescription>
        The password reset link is invalid, or it might have expired.
      </AlertDescription>
    </Alert>
  </div>
);
const ResetPasswordRedirectSuccess = () => (
  <div className="my-4 flex flex-col gap-2">
    <Alert variant="success">
      <CheckCircledIcon className="h-5 w-5 text-success" />
      <AlertTitle>Password reset successful.</AlertTitle>
      <AlertDescription>Please login using your new password.</AlertDescription>
    </Alert>
  </div>
);

// Exports
export const RedirectAlerts = () => {
  const [searchParams] = useSearchParams();
  return (
    <>
      {searchParams.has("registration-redirect") && (
        <RegistrationRedirectAlert />
      )}
      {searchParams.has("forgot-password-redirect") && (
        <ForgotPasswordRedirectAlert />
      )}
      {searchParams.has("reset-password-error") && (
        <ResetPasswordRedirectError />
      )}
      {searchParams.has("reset-password-success") && (
        <ResetPasswordRedirectSuccess />
      )}
    </>
  );
};
