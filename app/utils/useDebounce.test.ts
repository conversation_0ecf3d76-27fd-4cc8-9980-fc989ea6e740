import { describe, it, expect, vi } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useDebounce } from "./useDebounce";

describe("useDebounce", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should return the initial value immediately", () => {
    const initialValue = "test";
    const { result } = renderHook(() => useDebounce(initialValue, 500));
    expect(result.current).toBe(initialValue);
  });

  it("should not update the value before the delay has passed", () => {
    const initialValue = "test";
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: initialValue, delay: 500 } }
    );

    // Change the value
    rerender({ value: "updated", delay: 500 });

    // Advance time but not enough to trigger the update
    act(() => {
      vi.advanceTimersByTime(400);
    });

    expect(result.current).toBe(initialValue);
  });

  it("should update the value after the delay has passed", () => {
    const initialValue = "test";
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: initialValue, delay: 500 } }
    );

    // Change the value
    const updatedValue = "updated";
    rerender({ value: updatedValue, delay: 500 });

    // Advance time enough to trigger the update
    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(result.current).toBe(updatedValue);
  });

  it("should handle multiple value changes within the delay period", () => {
    const initialValue = "test";
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: initialValue, delay: 500 } }
    );

    // Change the value multiple times
    rerender({ value: "intermediate1", delay: 500 });

    act(() => {
      vi.advanceTimersByTime(200);
    });

    rerender({ value: "intermediate2", delay: 500 });

    act(() => {
      vi.advanceTimersByTime(200);
    });

    const finalValue = "final";
    rerender({ value: finalValue, delay: 500 });

    expect(result.current).toBe(initialValue);

    // Advance time to trigger the update
    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(result.current).toBe(finalValue);
  });

  it("should handle delay changes", () => {
    const initialValue = "test";
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: initialValue, delay: 500 } }
    );

    // Change the value and delay
    const updatedValue = "updated";
    rerender({ value: updatedValue, delay: 1000 });

    // Advance time but not enough for the new delay
    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(result.current).toBe(initialValue);

    // Advance time to reach the new delay
    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(result.current).toBe(updatedValue);
  });

  it("should clean up timeout on unmount", () => {
    const clearTimeoutSpy = vi.spyOn(global, "clearTimeout");

    const { unmount } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: "test", delay: 500 } }
    );

    unmount();

    expect(clearTimeoutSpy).toHaveBeenCalled();
  });
});
