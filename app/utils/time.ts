export const secondsToDurationString = (seconds: number) => {
  return new Date(seconds * 1000).toISOString().substring(11, 19);
};

export type Period = "am" | "pm";
export type TimeParts = {
  hours: number;
  hours24: number;
  minutes: number;
  period: "am" | "pm";
};
export const hours24ToHoursPeriod = (
  hours24: number
): Pick<TimeParts, "hours" | "period"> => {
  // hours24:0 / hours:12 / "12 am"
  if (hours24 === 0) return { hours: 12, period: "am" };

  // hours24:1-11 / hours:1-11 / "1-11 am"
  if (hours24 >= 1 && hours24 < 12) return { hours: hours24, period: "am" };

  // hours24:12 / hours:12 / "12 pm"
  if (hours24 === 12) return { hours: 12, period: "pm" };

  // hours24:13-23 / hours:1-11 / "1-11 pm"
  if (hours24 >= 12 && hours24 < 24) {
    return { hours: hours24 - 12, period: "pm" };
  }

  throw Error(
    `Invalid hour24 value: ${hours24}. Value must be in the 0-23 range.`
  );
};

export const hoursPeriodToHours24 = (hours: number, period: Period) => {
  if (hours === 12) {
    // hours24:0 / hours:12 / "12 am"
    if (period === "am") return 0;
    // hours24:12 / hours:12 / "12 pm"
    else return 12;
  }

  if (hours >= 1 && hours < 12) {
    // hours24:1-11 / hours:1-11 / "1-11 am"
    if (period === "am") return hours;
    // hours24:13-23 / hours:1-11 / "1-11 pm"
    else return hours + 12;
  }

  throw Error(
    `Invalid hours or period values: hours=${hours}, period=${period}. Hours must be in the 1-12 range, and period must be <am|pm>.`
  );
};

export const dateToTimeParts = (date: Date): TimeParts => {
  const hours24 = date.getHours();
  const minutes = date.getMinutes();

  const { hours, period } = hours24ToHoursPeriod(hours24);

  return { hours, hours24, minutes, period };
};

export const timePartsToDate = (timeParts: TimeParts): Date => {
  const date = new Date();
  date.setHours(timeParts.hours24, timeParts.minutes);
  return date;
};
