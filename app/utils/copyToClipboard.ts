import { toast } from "react-toastify";

import stripHtml from "./stripHtml";
import { marked } from "marked";

export const copyToClipboard = (text: string, section?: string) => {
  navigator.clipboard.writeText(text).then(
    () => {
      if (section) toast.success(`Copied ${section} to clipboard!`);
      else toast.success("Copied to clipboard!");
    },
    (err) => {
      toast.error("Failed to copy text.");
    }
  );
};

type CopyableSection = {
  title?: string;
  list?: string[] | CopyableSection[];
};
export const copyFormattedVersionToClipboard = async (
  data: CopyableSection[],
  section?: string
) => {
  const htmlContent = data
    .map((section) => generateContent(section))
    .join("\n\n");

  const clipboardItemData = {
    "text/plain": stripHtml(htmlContent),
    "text/html": htmlContent,
  };

  const clipboardItem = new ClipboardItem(clipboardItemData);
  await navigator.clipboard.write([clipboardItem]);

  if (section) toast.success(`Copied ${section} to clipboard!`);
  else toast.success("Copied to clipboard!");
};

function generateContent(section: CopyableSection, isChild?: boolean) {
  let content = "";
  if (section.title) {
    // h2 for outer sections, h3 for inner ones
    const tag = isChild ? "h3" : "h2";
    content += `<${tag}>${section.title}</${tag}>\n\n`;
  }
  if (section.list) {
    if (typeof section.list[0] === "string") {
      content += `<ul><li>${section.list.join("</li>\n<li>")}</li></ul>`;
    } else {
      content += "<ul><li>";
      content += section.list
        .map((innerList) => {
          return generateContent(innerList as CopyableSection, true);
        })
        .join("</li>\n\n<li>");
      content += "</li></ul>";
    }
  }
  return content;
}

type CopyableTableData = {
  title: string;
  description?: string;
  data: string[][];
};
export const copyFormattedTableToClipboard = async (
  data: CopyableTableData,
  section?: string
) => {
  let htmlContent = "";
  htmlContent += `<h2>${data.title}</h2>\n`;
  if (data.description) {
    htmlContent += `<p>${data.description}</p>\n`;
  }
  htmlContent += `<table style="border-collapse: collapse"><tbody>`;
  htmlContent += data.data
    .map(
      (row) =>
        `<tr>${row.map((cell) => `<td style="border: 1px solid #ccc; padding: 5px;">${cell}</td>`).join("   ")}</tr>`
    )
    .join("\n");

  const clipboardItemData = {
    "text/plain": stripHtml(htmlContent),
    "text/html": htmlContent,
  };

  const clipboardItem = new ClipboardItem(clipboardItemData);
  await navigator.clipboard.write([clipboardItem]);

  if (section) toast.success(`Copied ${section} to clipboard!`);
  else toast.success("Copied to clipboard!");
};

export const copyFromMarkdownToClipboard = async (
  data: string,
  section?: string
) => {
  const htmlContent = await marked.parse(data);

  const clipboardItemData = {
    "text/plain": stripHtml(htmlContent),
    "text/html": htmlContent,
  };

  const clipboardItem = new ClipboardItem(clipboardItemData);
  await navigator.clipboard.write([clipboardItem]);

  if (section) toast.success(`Copied ${section} to clipboard!`);
  else toast.success("Copied to clipboard!");
};
