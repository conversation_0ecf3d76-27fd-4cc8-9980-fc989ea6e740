import { useEffect, useState } from "react";
import { useLocation } from "react-router";

import { useFlag } from "~/context/flags";

type Options = {
  triggerViaUrl?: boolean;
  sectionName?: string;
  checks?: (() => boolean)[];
};
function useOnboarding(key: string, options?: Options) {
  const { triggerViaUrl, sectionName, checks } = options ?? {};

  const [isTutorialEnabled, setIsTutorialEnabled] = useState(false);
  const [isMarkedAsComplete, setIsMarkedAsComplete] = useState(false);

  const enableInAppTutorials = useFlag("EnableInAppTutorials");
  const location = useLocation();

  const sectionUrlKey = location.search
    ? new URLSearchParams(location.search).get("onboarding-section")
    : "";

  const isEnabledForCurrentPath =
    location.search &&
    new URLSearchParams(location.search).get("onboarding") === "true";

  const generatedKey = `onboarding-${key}`;

  // check if the tutorial has already been completed by checking local storage;
  // if not, set the tutorial to be enabled
  useEffect(() => {
    // tutorial should be disabled if flag is disabled
    if (!enableInAppTutorials) {
      setIsTutorialEnabled(false);
      return;
    }

    // if marked as complete by the parent component, keep it disabled
    if (isMarkedAsComplete) {
      setIsTutorialEnabled(false);
      return;
    }

    // if a section name is provided, check if the current URL matches it
    if (sectionName && sectionUrlKey !== sectionName) {
      setIsTutorialEnabled(false);
      return;
    }

    // if checks exist and any of them fail, disable tutorial
    if (checks && checks.some((check) => !check())) {
      setIsTutorialEnabled(false);
      return;
    }

    if (triggerViaUrl) {
      if (!isEnabledForCurrentPath) {
        setIsTutorialEnabled(false);
      } else {
        // settimeout added to avoid the random glitch where the tutorial's CSS position values break
        setTimeout(() => {
          setIsTutorialEnabled(true);
        }, 500);
      }

      return;
    }

    if (!generatedKey) {
      return;
    }

    const tutorialCompleted = localStorage.getItem(generatedKey);
    if (!tutorialCompleted) {
      setIsTutorialEnabled(true);
    }
  }, [
    enableInAppTutorials,
    generatedKey,
    triggerViaUrl,
    isEnabledForCurrentPath,
    checks,
    sectionName,
    sectionUrlKey,
    isMarkedAsComplete,
  ]);

  function completeTutorial() {
    setIsTutorialEnabled(false);
    setIsMarkedAsComplete(true);
    localStorage.setItem(generatedKey, "true");
  }

  return { isTutorialEnabled, completeTutorial };
}

export default useOnboarding;
