import { inspect } from "util";

// Constants
/* These sequences can be used to control terminal color */
const INFO_PREFIX = "\x1b[44m\x1b[30m info \x1b[0m";
const ERROR_PREFIX = "\x1b[41m\x1b[30m erro \x1b[0m";

// Exports
export const prettyPrintObject = (obj: any) =>
  inspect(obj, {
    depth: null,
    colors: true,
    showHidden: false,
  });

export const logInfo = (message: string, extra: any) => {
  if (process.env.ZEPLYN_LOG_LEVEL === "info") {
    // eslint-disable-next-line no-console
    console.info(INFO_PREFIX, message, prettyPrintObject(extra));
  }
};

export const logError = (message: string, extra: any) => {
  if (
    process.env.ZEPLYN_LOG_LEVEL === "info" ||
    process.env.ZEPLYN_LOG_LEVEL === "error"
  ) {
    // eslint-disable-next-line no-console
    console.error(ERROR_PREFIX, message, prettyPrintObject(extra));
  }
};
