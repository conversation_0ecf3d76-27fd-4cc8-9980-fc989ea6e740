/**
 * Encodes a string to base64, safely handling Unicode characters (e.g., accents, emojis).
 * @param {string} string - The input string to encode.
 * @returns {string} A base64-encoded string.
 */
export default function safeBtoa(string: string) {
  // convert the input string to UTF-8 bytes
  const encoder = new TextEncoder();
  // encode the string into a Uint8Array of UTF-8 bytes, handling all Unicode characters
  const bytes = encoder.encode(string);
  // convert the byte array to a Latin1 string (chars 0-255) that btoa can process
  const latin1String = String.fromCharCode(...bytes);
  // encode the Latin1 string to base64
  return btoa(latin1String);
}
