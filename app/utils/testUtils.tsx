import { Fragment } from "react";
import { render } from "@testing-library/react";
import { createMemoryRouter, RouteObject, RouterProvider } from "react-router";

export function renderRouter(routes: RouteObject[], initialEntries = ["/"]) {
  const router = createMemoryRouter(routes, { initialEntries });
  return render(<RouterProvider router={router} />);
}

export function renderRouterWithWrapper(
  routes: any[],
  initialEntries = ["/"],
  Wrapper: React.ComponentType<any> = Fragment
) {
  const wrappedRoutes = routes.map((route) => ({
    ...route,
    Component: () => (
      <Wrapper>
        <route.Component />
      </Wrapper>
    ),
  }));
  return renderRouter(wrappedRoutes, initialEntries);
}
