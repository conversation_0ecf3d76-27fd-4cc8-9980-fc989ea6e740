import { describe, it, expect } from "vitest";
import safeAtob from "./safeAtob";

describe("safeAtob", () => {
  it("should decode a simple base64 string", () => {
    const base64 = "SGVsbG8gd29ybGQ=";
    const expected = "Hello world";
    expect(safeAtob(base64)).toBe(expected);
  });

  it("should decode a base64 string with Unicode characters", () => {
    const base64 = "UG9rw6ltb24g4pqh77iP8J+SmvCfkpninaTvuI8=";
    const expected = "Pokémon ⚡️💚💙❤️";
    expect(safeAtob(base64)).toBe(expected);
  });

  it("should decode an empty string", () => {
    const base64 = "";
    const expected = "";
    expect(safeAtob(base64)).toBe(expected);
  });

  it("should throw an error for invalid base64 string", () => {
    const invalidBase64 = "invalid!";
    expect(() => safeAtob(invalidBase64)).toThrow();
  });
});
