import { ReactNode, useEffect, useState } from "react";

/**
 * Returns hydration status:
 * - on server, always returns `false`
 * - on client, returns `false` on first render, returns `true` afterwards
 */
export const useHydrated = (() => {
  let hydrating = true;
  return () => {
    const [hydrated, setHydrated] = useState(() => !hydrating);

    useEffect(() => {
      hydrating = false;
      setHydrated(true);
    }, []);

    return hydrated;
  };
})();

/**
 * Only renders children after hydration. Useful for rendering values that are
 * likely to yield different results on the server vs client, such as timestamps
 * and random values.
 */
export const AfterHydration = ({ children }: { children: ReactNode }) => {
  const isHydrated = useHydrated();
  return isHydrated ? <>{children}</> : null;
};

/**
 * Returns a function that will conditionally return a value if hydration is
 * completed, or a fallback if it is not.
 */
export const useAfterHydration = () => {
  const isHydrated = useHydrated();
  return function afterHydration<Value>(value: Value, fallback: Value): Value {
    return isHydrated ? value : fallback;
  };
};
