import { isFlagEnabled } from "./flagsInCookies"; // adjust the import path

describe("isFlagEnabled", () => {
  it("returns true when flag exists in cookie", () => {
    const mockRequest = new Request("http://localhost", {
      headers: {
        Cookie: 'flags=["feature1","feature2"]',
      },
    });

    const result = isFlagEnabled(mockRequest, "feature1");
    expect(result).toBe(true);
  });

  it("returns false when flag doesn't exist in cookie", () => {
    const mockRequest = new Request("http://localhost", {
      headers: {
        Cookie: 'flags=["feature1","feature2"]',
      },
    });

    const result = isFlagEnabled(mockRequest, "feature3");
    expect(result).toBe(false);
  });

  it("returns false when no cookie header exists", () => {
    const mockRequest = new Request("http://localhost");
    const result = isFlagEnabled(mockRequest, "feature1");
    expect(result).toBe(false);
  });

  it("returns false when cookie contains invalid JSON", () => {
    const mockRequest = new Request("http://localhost", {
      headers: {
        Cookie: "flags=invalid-json-here",
      },
    });

    const result = isFlagEnabled(mockRequest, "feature1");
    expect(result).toBe(false);
  });

  it("returns false when flags array is empty", () => {
    const mockRequest = new Request("http://localhost", {
      headers: {
        Cookie: "flags=[]",
      },
    });

    const result = isFlagEnabled(mockRequest, "feature1");
    expect(result).toBe(false);
  });

  it("returns false when cookie string is malformed", () => {
    const mockRequest = new Request("http://localhost", {
      headers: {
        Cookie: "flags;malformed",
      },
    });

    const result = isFlagEnabled(mockRequest, "feature1");
    expect(result).toBe(false);
  });
});
