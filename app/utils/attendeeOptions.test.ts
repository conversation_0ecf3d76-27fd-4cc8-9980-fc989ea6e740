import { describe, it, expect } from "vitest";
import { resolvedAttendeeOptions } from "./attendeeOptions";
import { AttendeeOption } from "~/api/attendees/types";
import { AttendeeInfo } from "~/api/openapi/generated/models/AttendeeInfo";

describe("resolvedAttendeeOptions", () => {
  const attendeeOptions: AttendeeOption[] = [
    { uuid: "user1", name: "<PERSON>", type: "user" },
    { uuid: "client1", name: "<PERSON>", type: "client" },
    { uuid: "user2", name: "<PERSON>", type: "user" },
  ];

  it("should handle empty inputs", () => {
    const result = resolvedAttendeeOptions([], undefined, []);
    expect(result).toEqual({
      initialAttendees: [],
      attendeeOptions: [],
    });
  });

  it("should match note attendees with existing options", () => {
    const noteAttendees: AttendeeInfo[] = [
      {
        uuid: "1",
        name: "<PERSON>",
        type: "user",
        userUuid: "user1",
        clientUuid: null,
      },
      {
        uuid: "2",
        name: "<PERSON>",
        type: "client",
        userUuid: null,
        clientUuid: "client1",
      },
    ];

    const result = resolvedAttendeeOptions(attendeeOptions, noteAttendees, []);
    expect(result.attendeeOptions).toEqual(attendeeOptions);
    expect(result.initialAttendees).toEqual(attendeeOptions.slice(0, 2));
  });

  it("should handle unknown note attendees", () => {
    const noteAttendees: AttendeeInfo[] = [
      {
        uuid: "997",
        name: "UnknownUser",
        type: "user",
        userUuid: "user123",
        clientUuid: null,
      },
      {
        uuid: "998",
        name: "UnknownClient",
        type: "client",
        userUuid: null,
        clientUuid: "client123",
      },
      {
        uuid: "999",
        name: "Unknown",
        type: "unknown",
        userUuid: null,
        clientUuid: null,
      },
    ];

    const result = resolvedAttendeeOptions(attendeeOptions, noteAttendees, []);
    const initialAttendeeOptions = [
      { uuid: "user123", name: "UnknownUser", type: "user" },
      { uuid: "client123", name: "UnknownClient", type: "client" },
      { uuid: "999", name: "Unknown", type: "unknown" },
    ];
    const sortedAttendeeOptions = [...initialAttendeeOptions];
    sortedAttendeeOptions.sort((a, b) => a.name.localeCompare(b.name));

    expect(result.attendeeOptions).toEqual([
      ...attendeeOptions,
      ...sortedAttendeeOptions,
    ]);
    expect(result.initialAttendees).toEqual(initialAttendeeOptions);
  });

  it("should deduplicate additional attendees", () => {
    const additionalAttendees: AttendeeOption[] = [
      { uuid: "1", name: "Alice", type: "user" },
      { uuid: "1", name: "Alice", type: "user" },
    ];

    const result = resolvedAttendeeOptions(
      attendeeOptions,
      undefined,
      additionalAttendees
    );
    expect(result.initialAttendees).toHaveLength(1);
  });

  it("should sort attendee options by name", () => {
    const additionalAttendees: AttendeeOption[] = [
      { uuid: "4", name: "Zach", type: "user" },
      { uuid: "5", name: "Amy", type: "user" },
    ];

    const result = resolvedAttendeeOptions(
      attendeeOptions,
      undefined,
      additionalAttendees
    );
    expect(result.attendeeOptions[0]?.name).toBe("Alice");
    expect(result.attendeeOptions[1]?.name).toBe("Amy");
  });

  it("should combine note attendees and additional attendees", () => {
    const noteAttendees: AttendeeInfo[] = [
      {
        uuid: "1",
        name: "Alice",
        type: "user",
        userUuid: "user1",
        clientUuid: null,
      },
    ];
    const additionalAttendees: AttendeeOption[] = [
      { uuid: "4", name: "Dave", type: "client" },
    ];

    const result = resolvedAttendeeOptions(
      attendeeOptions,
      noteAttendees,
      additionalAttendees
    );
    const initialAtendeeOptions = [
      { uuid: "user1", name: "Alice", type: "user" },
      { uuid: "4", name: "Dave", type: "client" },
    ];
    expect(result.initialAttendees).toEqual(initialAtendeeOptions);
    expect(result.attendeeOptions).toEqual(
      [...attendeeOptions.slice(1, 3), ...initialAtendeeOptions].sort((a, b) =>
        a.name.localeCompare(b.name)
      )
    );
  });

  it("should deduplicate note attendees with additional attendees", () => {
    const noteAttendees: AttendeeInfo[] = [
      {
        uuid: "1",
        name: "Alice",
        type: "user",
        userUuid: "user1",
        clientUuid: null,
      },
      {
        uuid: "2",
        name: "Bob",
        type: "client",
        userUuid: null,
        clientUuid: "client1",
      },
      {
        uuid: "3",
        name: "Carol",
        type: "unknown",
        userUuid: null,
        clientUuid: null,
      },
    ];
    const additionalAttendees: AttendeeOption[] = [
      { uuid: "user1", name: "AliceAdditional", type: "user" },
      { uuid: "client1", name: "BobAdditional", type: "client" },
      { uuid: "additional3", name: "Carol", type: "unknown" },
      { uuid: "additional4", name: "Dave", type: "unknown" },
    ];

    const result = resolvedAttendeeOptions(
      attendeeOptions,
      noteAttendees,
      additionalAttendees
    );
    expect(result.initialAttendees).toHaveLength(4);
    expect(result.initialAttendees).toEqual([
      { uuid: "user1", name: "Alice", type: "user" },
      { uuid: "client1", name: "Bob", type: "client" },
      { uuid: "3", name: "Carol", type: "unknown" },
      { uuid: "additional4", name: "Dave", type: "unknown" },
    ]);
  });
});
