import stripHtml from "./stripHtml";

describe("stripHtml (browser)", () => {
  it("removes simple HTML tags", () => {
    expect(stripHtml("<b>Hello</b> World")).toBe("Hello World");
  });

  it("removes nested HTML tags", () => {
    expect(stripHtml("<div><span>Test <b>123</b></span></div>")).toBe(
      "Test 123"
    );
  });

  it("returns empty string for empty input", () => {
    expect(stripHtml("")).toBe("");
  });

  it("handles HTML entities", () => {
    expect(stripHtml("Hello &amp; goodbye")).toBe("Hello & goodbye");
  });

  it("returns the same string if there are no HTML tags", () => {
    expect(stripHtml("Just plain text")).toBe("Just plain text");
  });

  it("handles input with only tags", () => {
    expect(stripHtml("<br><hr>")).toBe("");
  });

  it("handles malformed HTML gracefully", () => {
    expect(stripHtml("<div>Unclosed tag")).toBe("Unclosed tag");
  });

  it("handles comments in HTML", () => {
    expect(stripHtml("Hello<!-- comment -->World")).toBe("HelloWorld");
  });

  it("handles whitespace correctly", () => {
    expect(stripHtml("<div>  Hello   <span>World</span> </div>")).toBe(
      "  Hello   World "
    );
  });

  it("handles multiple root elements", () => {
    expect(stripHtml("<p>One</p><p>Two</p>")).toBe("OneTwo");
  });
});
