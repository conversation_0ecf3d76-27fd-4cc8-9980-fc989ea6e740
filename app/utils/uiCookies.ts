import getCookieValue from "./getCookieValue";

export function getTheme(cookieHeader: string | null) {
  if (!cookieHeader) {
    return "light";
  }

  let theme = "";

  try {
    const uiSettings = JSON.parse(
      decodeURIComponent(getCookieValue(cookieHeader, "ui-settings") || "")
    );

    theme = uiSettings.theme;
  } catch (e) {
    theme = "light";
  }

  return theme;
}

export function getUiSettingsFromCookies(cookieHeader: string | null) {
  if (!cookieHeader) {
    return {};
  }

  try {
    return JSON.parse(
      decodeURIComponent(getCookieValue(cookieHeader, "ui-settings") || "")
    );
  } catch (e) {
    return {};
  }
}

// save settings in cookie (expires in 365 days)
export function saveUiSettingsInCookies(settings: { [key: string]: any }) {
  document.cookie = `ui-settings=${encodeURIComponent(
    JSON.stringify(settings)
  )}; path=/; max-age=${60 * 60 * 24 * 365}`;
}
