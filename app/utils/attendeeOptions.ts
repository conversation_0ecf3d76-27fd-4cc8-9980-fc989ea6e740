import { AttendeeOption, AttendeeOptions } from "~/api/attendees/types";
import { AttendeeInfo } from "~/api/openapi/generated";
import { SerializeFrom } from "~/types/remix";

// Resolves an initial attendees and a list of attendee options that should be displayed to the
// user.
//
// The main reason for this logic is to reconcile the list of attendees that the server has provided
// (which contains no "unknown" attendees, i.e., attendees that do not map to Zeplyn data model
// entities) with the list of attendees that are known from the note (which may contain "unknown"
// attendees) and a list of additional initial attendees derived from another source. This function
// maps note attendees and additional attendees to the server-provided attendee options (when possible),
// and adds other attendees to the list of attendee options.
//
// Parameters:
//   - attendeeOptions: the list of attendees that the server has provided as options.
//   - noteAttendees: the list of attendees that are known from the note.
//   - additionalAttendees: the list of additional attendees, which may overlap with attendees in the
//     attendeeOptions list and the note.
//
// Returns:
//   An object with the following properties:
//     - initialAttendees: the list of attendees that should be displayed to the user initially.
//     - attendeeOptions: the list of attendees that should be displayed to the user as options,
//       including unknown attendees backfilled from the note, and unknown attendees populated from
//       the additional attendees list.
export const resolvedAttendeeOptions = (
  attendeeOptions: AttendeeOptions,
  noteAttendees: SerializeFrom<AttendeeInfo>[] | undefined,
  additionalAttendees: (AttendeeOption | undefined)[]
) => {
  // Map note attendees to known attendee options, and create local attendee options for unknown
  // attendees.
  const knownNoteAttendees: AttendeeOptions = [];
  const unknownNoteAttendees: AttendeeOptions = [];
  noteAttendees?.forEach((noteAttendee) => {
    const attendeeOption = attendeeOptions.find((attendee) => {
      if (noteAttendee.type === "client") {
        return (
          noteAttendee.clientUuid !== null &&
          attendee.uuid === noteAttendee.clientUuid
        );
      } else if (attendee.type === "user") {
        return (
          noteAttendee.userUuid !== null &&
          attendee.uuid === noteAttendee.userUuid
        );
      } else {
        return false;
      }
    });
    if (attendeeOption) {
      knownNoteAttendees.push(attendeeOption);
    } else {
      let uuid = noteAttendee.uuid;
      switch (noteAttendee.type) {
        case "client":
          if (noteAttendee.clientUuid) {
            uuid = noteAttendee.clientUuid;
          }
          break;
        case "user":
          if (noteAttendee.userUuid) {
            uuid = noteAttendee.userUuid;
          }
          break;
      }
      const newOptionToAdd: AttendeeOption = {
        name: noteAttendee.name,
        uuid: uuid,
        type: noteAttendee.type,
      };
      unknownNoteAttendees.push(newOptionToAdd);
    }
  });

  // Match up additional attendees with the attendee options list, to ensure UI consistency.
  const matchedPrefilledAttendees = additionalAttendees
    .filter((attendee) => attendee !== undefined)
    .map((attendee) => {
      // In theory, we could filter by attendee type, but the uniqueness guarantee of UUIDs is
      // adequate to ensure that clients don't match users.
      return (
        attendeeOptions.find((item) => item.uuid === attendee?.uuid) || attendee
      );
    }) as AttendeeOptions;

  // Uniquify the list of matched additional attendees against itself and against the list of note attendees.
  const foundKnownAttendeeUUIDs = new Set(
    knownNoteAttendees.map((attendee) => attendee.uuid)
  );
  // Unknown attendees are uniquified by name, since they don't have UUIDs.
  const foundUnknownAttendeeNames = new Set(
    unknownNoteAttendees.map((attendee) => attendee.name)
  );
  const uniqueMatchedPrefilledAttendees = matchedPrefilledAttendees.filter(
    (attendee) => {
      if (foundKnownAttendeeUUIDs.has(attendee.uuid)) {
        return false;
      }
      if (
        attendee.type === "unknown" &&
        foundUnknownAttendeeNames.has(attendee.name)
      ) {
        return false;
      }
      foundKnownAttendeeUUIDs.add(attendee.uuid);
      return true;
    }
  );

  // Determine which prefilled attendees are not in the attendee options list (i.e., are "unknown"
  // attendees).
  const attendeeOptionsUUIDs = new Set(
    attendeeOptions.map((attendee) => attendee.uuid)
  );
  const unknownAdditionalAttendees = uniqueMatchedPrefilledAttendees.filter(
    (attendee) => {
      return !attendeeOptionsUUIDs.has(attendee.uuid);
    }
  );

  /// The attendee options to use for allowing the user to select should include all
  /// server-delivered options, plus any options inferred from the note or from the additional
  /// attendees list.
  const mergedAttendeeOptions = attendeeOptions
    .concat(unknownNoteAttendees)
    .concat(unknownAdditionalAttendees)
    .sort((a, b) => a.name.localeCompare(b.name));

  // The initial list of attendees to use is the concatenation of any note attendees and any
  // prefilled attendees, with these two lists uniqified against each other (biasing for the note)
  const initialAttendees = knownNoteAttendees
    .concat(unknownNoteAttendees)
    .concat(uniqueMatchedPrefilledAttendees);

  return {
    initialAttendees,
    attendeeOptions: mergedAttendeeOptions,
  };
};
