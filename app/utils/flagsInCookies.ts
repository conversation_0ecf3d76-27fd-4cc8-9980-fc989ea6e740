import { fetchFlags } from "~/api/flags/flagFetcher.server";
import getCookieValue from "~/utils/getCookieValue";

// check if a specific flag is enabled; return true or false
export function isFlagEnabled(request: Request, flagKey: string) {
  try {
    const flagsCookie = JSON.parse(
      getCookieValue(request.headers.get("Cookie") ?? "", "flags") || ""
    );
    return flagsCookie.includes(flagKey);
  } catch (error) {
    return false;
  }
}

// fetch list of flags from API; save enabled flags in cookies; return the response
export async function cookieHeadersForFlags(
  request: Request
): Promise<HeadersInit> {
  const flags = await fetchFlags(request);

  return {
    "Set-Cookie": `flags=${JSON.stringify(
      getEnabledFlags(flags)
    )};Path=/;SameSite=Strict`,
  };
}

// get array of flags that are enabled
function getEnabledFlags(flags: {
  flags: { [key: string]: { is_active: boolean } };
}) {
  const enabledFlags: string[] = [];
  try {
    const { flags: flagsObj } = flags;
    Object.keys(flagsObj).forEach((key) => {
      if (flagsObj[key]?.is_active) {
        enabledFlags.push(key);
      }
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error parsing flags", error);
  }

  return enabledFlags;
}
