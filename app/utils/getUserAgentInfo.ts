import Bowser from "bowser";

import { defaultUa } from "~/context/userAgent";

export default function getUserAgentInfo(userAgent: string) {
  if (!userAgent) {
    return defaultUa;
  }

  const parser = Bowser.getParser(userAgent);
  const platform = parser.getPlatform();
  const os = parser.getOS();
  const browser = parser.getBrowser();

  const osName = os.name?.toLowerCase() ?? "";
  const browserName = browser.name?.toLowerCase() ?? "";

  return {
    isMobile: platform.type === "mobile" || platform.type === "tablet",
    isAndroid: osName.includes("android"),
    isIos: osName.includes("ios"),
    isFirefox: browserName.includes("firefox"),
    isChrome: browserName.includes("chrome"),
    isSafari: browserName.includes("safari"),
  };
}
