import { describe, it, expect } from "vitest";
import safeBtoa from "./safeBtoa";

describe("safeBtoa", () => {
  it("should encode a simple string to base64", () => {
    const input = "Hello world";
    const expected = "SGVsbG8gd29ybGQ=";
    expect(safeBtoa(input)).toBe(expected);
  });

  it("should encode a string with Unicode characters to base64", () => {
    const input = "Pokémon ⚡️💚💙❤️";
    const expected = "UG9rw6ltb24g4pqh77iP8J+SmvCfkpninaTvuI8=";
    expect(safeBtoa(input)).toBe(expected);
  });

  it("should encode an empty string to base64", () => {
    const input = "";
    const expected = "";
    expect(safeBtoa(input)).toBe(expected);
  });
});
