/**
 * Decodes a base64 string back to its original form, supporting Unicode characters.
 * @param {string} string - The base64-encoded string to decode.
 * @returns {string} The original decoded string.
 */
export default function safeAtob(string: string) {
  // decode base64 to a string and convert each character to its byte value
  const bytes = Uint8Array.from(atob(string), (c) => c.charCodeAt(0));
  // create a TextDecoder to interpret the bytes as UTF-8 and reconstruct the string
  const decoder = new TextDecoder();
  // decode the byte array back to the original Unicode string
  return decoder.decode(bytes);
}
