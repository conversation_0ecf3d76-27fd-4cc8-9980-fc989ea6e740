import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { fetchPost } from "./fetch";

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("fetchPost", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should make a POST request with correct parameters", async () => {
    const mockResponse = { success: true, data: "test data" };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/test";
    const body = { key: "value", number: 123 };

    await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
  });

  it("should return parsed JSON response", async () => {
    const mockResponse = {
      id: 1,
      message: "Success",
      data: { nested: "value" },
    };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/posts";
    const body = { title: "Test Post", content: "This is a test" };

    const result = await fetchPost(url, body);

    expect(result).toEqual(mockResponse);
  });

  it("should handle empty body object", async () => {
    const mockResponse = { success: true };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/empty";
    const body = {};

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}),
    });
    expect(result).toEqual(mockResponse);
  });

  it("should handle null body", async () => {
    const mockResponse = { success: true };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/null";
    const body = null;

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(null),
    });
    expect(result).toEqual(mockResponse);
  });

  it("should handle complex nested objects", async () => {
    const mockResponse = { processed: true };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/complex";
    const body = {
      user: {
        name: "John Doe",
        preferences: {
          theme: "dark",
          notifications: true,
        },
      },
      tags: ["important", "urgent"],
      metadata: {
        timestamp: new Date("2025-06-30T12:00:00Z"),
        version: 1,
      },
    };

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    expect(result).toEqual(mockResponse);
  });

  it("should handle array body", async () => {
    const mockResponse = { count: 3 };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/array";
    const body = [
      { id: 1, name: "Item 1" },
      { id: 2, name: "Item 2" },
      { id: 3, name: "Item 3" },
    ];

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    expect(result).toEqual(mockResponse);
  });

  it("should handle string body", async () => {
    const mockResponse = { received: "string" };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/string";
    const body = "plain string body";

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    expect(result).toEqual(mockResponse);
  });

  it("should handle boolean body", async () => {
    const mockResponse = { boolean: true };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/boolean";
    const body = true;

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    expect(result).toEqual(mockResponse);
  });

  it("should handle number body", async () => {
    const mockResponse = { number: 42 };
    const mockJsonPromise = Promise.resolve(mockResponse);
    const mockFetchPromise = Promise.resolve({
      json: () => mockJsonPromise,
    });

    mockFetch.mockResolvedValue(mockFetchPromise);

    const url = "https://api.example.com/number";
    const body = 42;

    const result = await fetchPost(url, body);

    expect(mockFetch).toHaveBeenCalledWith(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    expect(result).toEqual(mockResponse);
  });
});
