import { getHintUtils } from "@epic-web/client-hints";
import { clientHint as timeZoneHint } from "@epic-web/client-hints/time-zone";

// An object with utility functions for working with client hints.
export const hintsUtils = getHintUtils({ timeZone: timeZoneHint });

// Returns the client's time zone.
export function getClientTimeZone(request: Request) {
  return hintsUtils.getHints(request).timeZone;
}

// A component that renders a script tag with the client hint check script.
//
// This should be inserted into the HTML <head> of the root route.
export function ClientHintCheck() {
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: hintsUtils.getClientHintCheckScript(),
      }}
    />
  );
}
