export function data<T>(data: T, init: ResponseInit = {}): Response {
  const headers = new Headers(init.headers);

  if (!headers.has("Content-Type")) {
    headers.set("Content-Type", "application/json");
  }

  // Ensure undefined/null data becomes valid JSON ("null" or "{}")
  const body = data === undefined ? null : data;

  return new Response(JSON.stringify(body), {
    ...init,
    headers,
  });
}
