import { z } from "zod";

// !TODO: This is a very fast and dumb way to deal with Zod errors. We should
// switch to zod-validation-error library in the future
export const flattenZodErrors = (error: z.ZodError) => {
  const { formErrors, fieldErrors } = error.flatten();
  return [formErrors, Object.values(fieldErrors)]
    .flat(3)
    .filter((message): message is string => typeof message === "string");
};

export const formDataToObject = (formData: FormData) =>
  Object.fromEntries(formData.entries());

export const searchParamsToObject = (searchParams: URLSearchParams) =>
  Object.fromEntries(searchParams.entries());

export const isValidTitle = (title: string) => title.trim().length !== 0;
