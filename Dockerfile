FROM node:20.18.1 AS base

ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ARG BUILD_VERSION

#
# Install the dependencies, including development dependencies.
#
FROM base AS dev-deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm install --include=dev

#
# Construct the production dependencies
#
FROM dev-deps AS prod-deps
WORKDIR /app
RUN npm prune --omit=dev

#
# Build the app
#
FROM dev-deps AS build

# Install git in this build stage so that the Datadog CI tool can get info
# about the repo.
RUN apt-get update && apt-get install -y git

WORKDIR /app

# Build
COPY . .
RUN npm run build

# Upload sourcemaps
ENV DATADOG_SITE=us5.datadoghq.com
ENV DATADOG_API_HOST=api.us5.datadoghq.com
RUN npm install @datadog/datadog-ci
# The browser logs use a different service than the server-side logs, so upload
# the maps twice.
RUN --mount=type=secret,id=DATADOG_API_KEY,env=DATADOG_API_KEY \
  npx datadog-ci sourcemaps upload \
  build/client/assets \
  --service=frontend \
  --release-version=${BUILD_VERSION}  \
  --minified-path-prefix=/assets
RUN --mount=type=secret,id=DATADOG_API_KEY,env=DATADOG_API_KEY \
  npx datadog-ci sourcemaps upload \
  build/server \
  --service=frontend \
  --release-version=${BUILD_VERSION}  \
  --minified-path-prefix=/assets
RUN --mount=type=secret,id=DATADOG_API_KEY,env=DATADOG_API_KEY \
  npx datadog-ci sourcemaps upload \
  build/client/assets \
  --service=frontend-browser \
  --release-version=${BUILD_VERSION}  \
  --minified-path-prefix=/assets
RUN --mount=type=secret,id=DATADOG_API_KEY,env=DATADOG_API_KEY \
  npx datadog-ci sourcemaps upload \
  build/server \
  --service=frontend-browser \
  --release-version=${BUILD_VERSION}  \
  --minified-path-prefix=/assets

# Delete the sourcemaps
RUN find ./build -type f -name '*.map' -delete

#
# Running image
#
FROM base
WORKDIR /app
COPY --from=prod-deps /app/node_modules /app/node_modules
COPY --from=build /app/build /app/build
COPY --from=build /app/public /app/public
COPY . .

ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}
# https://docs.datadoghq.com/getting_started/tagging/unified_service_tagging
ENV DD_VERSION=${BUILD_VERSION}  
# Read in the frontend code to set the "version" for Datadog RUM.
# Ideally, we would use the image tag as the version here, but because we
# upload the sourcemaps during the build and then remove them, and Datadog
# requires a version code to correlate minified files to sourcemaps, we need
# the version used by the Datadog library to be set and fixed at build time.
ENV ZEPLYN_APP_VERSION=${BUILD_VERSION}  
CMD ["npm", "run", "serve"]
