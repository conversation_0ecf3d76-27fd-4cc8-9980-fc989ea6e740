# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/public/build

# misc
.DS_Store
.env.development
.env.development.local
.env.development.secrets
.cache

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local IDE configs
.vscode/settings.json
.vscode/vscode-npm-prepare-hook-events.log

# Generated code
app/api/openapi/generated

# Datadog bundled worker
/public/datadog-worker*
