import { parse } from "dotenv";
import { existsSync, readFileSync } from "fs";
import { resolve } from "path";

// Constants
const ALLOWED_EXPLICIT = [
  "NODE_ENV",
  "OTEL_EXPORTER_OTLP_ENDPOINT",
  "OTEL_SDK_DISABLED",
];
const INFO_PREFIX = "\x1b[44m\x1b[30m info \x1b[0m";

// Helpers
const filterAllowed = (
  /** @type {Record<string, string>} */
  parsed
) =>
  Object.fromEntries(
    Object.entries(parsed).filter(
      ([key]) =>
        ALLOWED_EXPLICIT.includes(key) ||
        // All env vars that start with ZEPLYN_ are allowed
        key.startsWith("ZEPLYN_")
    )
  );

const parseEnvFile = (filename) => {
  const cwd = process.cwd();
  const path = resolve(cwd, filename);

  if (existsSync(path)) {
    const contents = readFileSync(path, "utf8");
    const parsed = parse(contents);
    const allowed = filterAllowed(parsed);
    return allowed;
  }

  // eslint-disable-next-line no-console
  console.log(
    INFO_PREFIX,
    `[loadEnv] Could not find ${filename} env file, skipping`
  );

  return {};
};

/**
 * Reads and merges env vars from process.env and the different .env.* files.
 * Does NOT modify process.env
 */
export const readEnv = () => {
  const NODE_ENV = process.env.NODE_ENV ?? "development";

  // Process environment variables
  const processEnv = filterAllowed(process.env);

  // Process the .env files for the development environment (if present)
  const devEnv = parseEnvFile(".env.development");
  const devSecrets = parseEnvFile(`.env.development.secrets`);
  const devLocal = parseEnvFile(`.env.development.local`);

  const mergedEnv = {
    NODE_ENV,
    ...devEnv,
    ...devSecrets,
    ...devLocal,
    ...processEnv,
  };

  // eslint-disable-next-line no-console
  console.log(INFO_PREFIX, "[loadEnv] Merged environment variables", {
    devEnv,
    devSecrets,
    devLocal,
    processEnv,
    mergedEnv,
  });

  return mergedEnv;
};

/**
 * Reads and merges env vars, then mutates process.env with merged values
 */
export const loadEnv = (() => {
  let mergedEnv;
  return () => {
    // Only read envs if we haven't already
    if (mergedEnv) return mergedEnv;

    // Haven't read envs yet, read them and mutate process.env
    mergedEnv = readEnv(readEnv);
    process.env = { ...process.env, ...mergedEnv };
  };
})();
